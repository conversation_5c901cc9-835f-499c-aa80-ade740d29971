{"name": "esw_web", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=20.0.0", "npm": "10.x"}, "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "codegen": "graphql-codegen --config codegen.ts", "prepare": "husky install", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "build-storybook": "storybook build", "test": "npx cypress run --component && npx cypress run --e2e"}, "dependencies": {"-": "^0.0.1", "@apollo/client": "^3.8.7", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@million/lint": "^0.0.73", "@mui/icons-material": "^5.14.16", "@mui/joy": "^5.0.0-beta.48", "@mui/lab": "^6.0.0-beta.13", "@mui/material": "^5.14.17", "@mui/styled-engine-sc": "^6.0.0-alpha.5", "@preact/signals-core": "^1.8.0", "@preact/signals-react": "^2.2.0", "@sentry/react": "^8.42.0", "@sentry/vite-plugin": "^2.22.7", "@types/dotenv": "^8.2.0", "@types/jest": "^29.5.12", "@types/react-helmet": "^6.1.11", "D": "^1.0.0", "axios": "^1.6.7", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "dotenv": "^16.3.1", "graphql": "^16.8.1", "jsplumb": "^2.15.6", "lodash": "^4.17.21", "million": "^3.0.6", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-ga4": "^2.1.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.47.0", "react-query": "^3.39.3", "react-router-dom": "^6.18.0", "react-toastify": "^9.1.3", "styled-components": "^6.1.0", "use-debounce": "^10.0.0", "use-query-params": "^2.2.1", "zustand": "^4.5.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@cypress/react": "^8.0.1", "@cypress/react18": "^2.0.0", "@cypress/vite-dev-server": "^5.0.7", "@graphql-codegen/cli": "5.0.0", "@graphql-codegen/client-preset": "^4.1.0", "@graphql-codegen/introspection": "4.0.0", "@graphql-codegen/typescript-react-apollo": "^4.1.0", "@storybook/addon-essentials": "^7.6.5", "@storybook/addon-interactions": "^7.6.5", "@storybook/addon-links": "^7.6.5", "@storybook/addon-onboarding": "^1.0.10", "@storybook/blocks": "^7.6.5", "@storybook/react": "^7.6.5", "@storybook/react-vite": "^7.6.5", "@storybook/test": "^7.6.5", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/graphql": "^14.5.0", "@types/lodash": "^4.14.201", "@types/moment": "^2.13.0", "@types/react": "^18.2.15", "@types/react-beautiful-dnd": "^13.1.5", "@types/react-dom": "^18.2.7", "@types/react-query": "^1.2.9", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.3.3", "babel-plugin-styled-components": "^2.1.4", "cypress": "^13.9.0", "eslint": "^8.45.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-cypress": "^3.2.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "eslint-plugin-storybook": "^0.6.15", "husky": "^8.0.3", "prettier": "^3.1.0", "prettier-eslint": "^16.1.2", "storybook": "^7.6.5", "typescript": "^5.0.2", "vite": "^4.4.5"}}