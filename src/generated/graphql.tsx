import { gql } from '@apollo/client';
import * as Apollo from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  DateTime: { input: any; output: any; }
};

export type Athlete = {
  __typename?: 'Athlete';
  athlete_id: Scalars['ID']['output'];
  club_name: Scalars['String']['output'];
  first: Scalars['String']['output'];
  last: Scalars['String']['output'];
  organization_code?: Maybe<Scalars['String']['output']>;
  short_position?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  team_id: Scalars['ID']['output'];
  team_name: Scalars['String']['output'];
  uniform?: Maybe<Scalars['String']['output']>;
};

export type Bracket = PoolOrBracketBase & {
  __typename?: 'Bracket';
  consolation?: Maybe<Scalars['Float']['output']>;
  court_short_name?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['Float']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_id: Scalars['ID']['output'];
  external: PoolOrBracketExternal;
  flow_chart?: Maybe<Scalars['String']['output']>;
  group_id?: Maybe<Scalars['ID']['output']>;
  is_pool: Scalars['Float']['output'];
  match_count: Scalars['Float']['output'];
  matches: Array<Match>;
  name?: Maybe<Scalars['String']['output']>;
  pb_finishes: Array<TeamAdvancement>;
  pb_seeds: Array<PoolOrBracketSeedItem>;
  pb_stats: Array<PoolOrBracketStatItem>;
  round_id: Scalars['ID']['output'];
  settings?: Maybe<PoolOrBracketSettings>;
  short_name?: Maybe<Scalars['String']['output']>;
  sort_priority: Scalars['Float']['output'];
  team_count: Scalars['Float']['output'];
  team_pb_stat?: Maybe<PoolOrBracketStatItem>;
  teams: Array<Team>;
  uuid: Scalars['ID']['output'];
};


export type BracketTeamsArgs = {
  filterTeamsIds?: InputMaybe<Array<Scalars['ID']['input']>>;
};

export type BracketMatch = {
  __typename?: 'BracketMatch';
  court_id?: Maybe<Scalars['String']['output']>;
  court_name?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['Float']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  match_id?: Maybe<Scalars['String']['output']>;
  match_number?: Maybe<Scalars['Float']['output']>;
  ref_code?: Maybe<Scalars['String']['output']>;
  ref_name?: Maybe<Scalars['String']['output']>;
  ref_roster_id?: Maybe<Scalars['Float']['output']>;
  results?: Maybe<BracketMatchResults>;
  scores?: Maybe<Scalars['String']['output']>;
  show_previously_accepted_bid_team1?: Maybe<Scalars['String']['output']>;
  show_previously_accepted_bid_team2?: Maybe<Scalars['String']['output']>;
  source?: Maybe<BracketMatchSourceTeam>;
  team1_code?: Maybe<Scalars['String']['output']>;
  team1_name?: Maybe<Scalars['String']['output']>;
  team1_rank?: Maybe<Scalars['Float']['output']>;
  team1_roster_id?: Maybe<Scalars['Float']['output']>;
  team1_temp_name?: Maybe<Scalars['String']['output']>;
  team1_temp_roster_id?: Maybe<Scalars['String']['output']>;
  team2_name?: Maybe<Scalars['String']['output']>;
  team2_rank?: Maybe<Scalars['Float']['output']>;
  team2_roster_id?: Maybe<Scalars['Float']['output']>;
  team2_temp_name?: Maybe<Scalars['String']['output']>;
  team2_temp_roster_id?: Maybe<Scalars['Float']['output']>;
  temp_score?: Maybe<Scalars['String']['output']>;
  winner?: Maybe<Scalars['String']['output']>;
  winning_roster_id?: Maybe<Scalars['Float']['output']>;
  winning_team_id?: Maybe<Scalars['Float']['output']>;
  winning_team_name?: Maybe<Scalars['String']['output']>;
  winning_temp_name?: Maybe<Scalars['String']['output']>;
};

export type BracketMatchResults = {
  __typename?: 'BracketMatchResults';
  set1?: Maybe<Scalars['String']['output']>;
  set2?: Maybe<Scalars['String']['output']>;
  set3?: Maybe<Scalars['String']['output']>;
  set4?: Maybe<Scalars['String']['output']>;
  set5?: Maybe<Scalars['String']['output']>;
  team1?: Maybe<BracketMatchResultsTeam>;
  team2?: Maybe<BracketMatchResultsTeam>;
  winner?: Maybe<Scalars['String']['output']>;
};

export type BracketMatchResultsTeam = {
  __typename?: 'BracketMatchResultsTeam';
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_pct?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  points_lost?: Maybe<Scalars['Float']['output']>;
  points_won?: Maybe<Scalars['Float']['output']>;
  roster_team_id?: Maybe<Scalars['Float']['output']>;
  scores?: Maybe<Scalars['String']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
};

export type BracketMatchSourceTeam = {
  __typename?: 'BracketMatchSourceTeam';
  ref?: Maybe<BracketMatchSourceTeamItem>;
  team1?: Maybe<BracketMatchSourceTeamItem>;
  team2?: Maybe<BracketMatchSourceTeamItem>;
};

export type BracketMatchSourceTeamItem = {
  __typename?: 'BracketMatchSourceTeamItem';
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  overallSeed?: Maybe<Scalars['Float']['output']>;
  reseedRank?: Maybe<Scalars['Float']['output']>;
  seed?: Maybe<Scalars['Float']['output']>;
  type?: Maybe<Scalars['Float']['output']>;
};

export type BracketPool = {
  __typename?: 'BracketPool';
  flow_chart?: Maybe<Scalars['String']['output']>;
};

export type Club = {
  __typename?: 'Club';
  club_code: Scalars['String']['output'];
  club_name: Scalars['String']['output'];
  roster_club_id: Scalars['ID']['output'];
  state?: Maybe<Scalars['String']['output']>;
  teams: Array<Team>;
  teams_count: Scalars['Int']['output'];
};

export type ClubShortInfo = {
  __typename?: 'ClubShortInfo';
  club_code: Scalars['String']['output'];
  club_name: Scalars['String']['output'];
  roster_club_id: Scalars['ID']['output'];
  state?: Maybe<Scalars['String']['output']>;
};

export type Court = {
  __typename?: 'Court';
  name: Scalars['String']['output'];
  short_name?: Maybe<Scalars['String']['output']>;
  sort_priority?: Maybe<Scalars['Float']['output']>;
  uuid: Scalars['ID']['output'];
};

export type CourtGrid = {
  __typename?: 'CourtGrid';
  courts?: Maybe<Array<CourtMatchesCourt>>;
  divisions?: Maybe<Array<CourtMatchesDivision>>;
  hours?: Maybe<Array<CourtMatchesTime>>;
};

export type CourtMatchesCourt = {
  __typename?: 'CourtMatchesCourt';
  court_id?: Maybe<Scalars['String']['output']>;
  court_name?: Maybe<Scalars['String']['output']>;
  event_id?: Maybe<Scalars['Float']['output']>;
  matches?: Maybe<Array<CourtMatchesCourtMatch>>;
  short_name?: Maybe<Scalars['String']['output']>;
};

export type CourtMatchesCourtMatch = {
  __typename?: 'CourtMatchesCourtMatch';
  color?: Maybe<Scalars['String']['output']>;
  date_end?: Maybe<Scalars['Float']['output']>;
  date_start?: Maybe<Scalars['Float']['output']>;
  division_id?: Maybe<Scalars['Float']['output']>;
  division_name?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  match_id?: Maybe<Scalars['String']['output']>;
  match_name?: Maybe<Scalars['String']['output']>;
  results?: Maybe<CourtMatchesCourtMatchResults>;
  secs_finished?: Maybe<Scalars['Float']['output']>;
  team1_roster_id?: Maybe<Scalars['Float']['output']>;
  team2_roster_id?: Maybe<Scalars['Float']['output']>;
  team_1_name?: Maybe<Scalars['String']['output']>;
  team_2_name?: Maybe<Scalars['String']['output']>;
  team_ref_name?: Maybe<Scalars['String']['output']>;
};

export type CourtMatchesCourtMatchResults = {
  __typename?: 'CourtMatchesCourtMatchResults';
  set1?: Maybe<Scalars['String']['output']>;
  set2?: Maybe<Scalars['String']['output']>;
  team1?: Maybe<CourtMatchesCourtMatchResultsTeam>;
  team2?: Maybe<CourtMatchesCourtMatchResultsTeam>;
  winner?: Maybe<Scalars['String']['output']>;
};

export type CourtMatchesCourtMatchResultsTeam = {
  __typename?: 'CourtMatchesCourtMatchResultsTeam';
  scores?: Maybe<Scalars['String']['output']>;
};

export type CourtMatchesDivision = {
  __typename?: 'CourtMatchesDivision';
  division_id?: Maybe<Scalars['Float']['output']>;
  division_name?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  level?: Maybe<Scalars['String']['output']>;
  level_sort_order?: Maybe<Scalars['Float']['output']>;
  max_age?: Maybe<Scalars['Float']['output']>;
  sort_order?: Maybe<Scalars['Float']['output']>;
};

export type CourtMatchesTime = {
  __typename?: 'CourtMatchesTime';
  default?: Maybe<Scalars['String']['output']>;
  time?: Maybe<Scalars['Float']['output']>;
  time12?: Maybe<Scalars['String']['output']>;
};

export type CourtShortInfo = {
  __typename?: 'CourtShortInfo';
  short_name?: Maybe<Scalars['String']['output']>;
  uuid: Scalars['ID']['output'];
};

export type Division = {
  __typename?: 'Division';
  division_id: Scalars['ID']['output'];
  has_flow_chart?: Maybe<Scalars['Boolean']['output']>;
  matches_time_ranges: Array<MatchesTimeRange>;
  media: Array<EventMedia>;
  name: Scalars['String']['output'];
  qualified_teams: Array<Team>;
  rounds: Array<Round>;
  short_name?: Maybe<Scalars['String']['output']>;
  teams_count: Scalars['Float']['output'];
};


export type DivisionMediaArgs = {
  filterFileTypes?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type DivisionPool = {
  __typename?: 'DivisionPool';
  court_start?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['Float']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  is_empty_pb_stats?: Maybe<Scalars['Boolean']['output']>;
  is_pool?: Maybe<Scalars['Float']['output']>;
  pb_name?: Maybe<Scalars['String']['output']>;
  pb_short_name?: Maybe<Scalars['String']['output']>;
  r_name?: Maybe<Scalars['String']['output']>;
  r_short_name?: Maybe<Scalars['String']['output']>;
  r_sort_priority?: Maybe<Scalars['Float']['output']>;
  r_uuid?: Maybe<Scalars['String']['output']>;
  rr_name?: Maybe<Scalars['String']['output']>;
  rr_short_name?: Maybe<Scalars['String']['output']>;
  rr_sort_priority?: Maybe<Scalars['Float']['output']>;
  settings?: Maybe<PoolOrBracketSettings>;
  sort_priority?: Maybe<Scalars['Float']['output']>;
  team_count?: Maybe<Scalars['Float']['output']>;
  teams?: Maybe<Array<DivisionPoolTeam>>;
  uuid?: Maybe<Scalars['String']['output']>;
};

export type DivisionPoolTeam = {
  __typename?: 'DivisionPoolTeam';
  info: DivisionPoolTeamInfo;
  is_empty_team_pb_stats?: Maybe<Scalars['Boolean']['output']>;
  matches_lost?: Maybe<Scalars['String']['output']>;
  matches_won?: Maybe<Scalars['String']['output']>;
  opponent_organization_code?: Maybe<Scalars['String']['output']>;
  opponent_team_id?: Maybe<Scalars['Float']['output']>;
  opponent_team_name?: Maybe<Scalars['String']['output']>;
  points_ratio?: Maybe<Scalars['Float']['output']>;
  rank?: Maybe<Scalars['Float']['output']>;
  sets_lost?: Maybe<Scalars['String']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['String']['output']>;
};

export type DivisionPoolTeamInfo = {
  __typename?: 'DivisionPoolTeamInfo';
  heading?: Maybe<Scalars['String']['output']>;
  heading_sort_priority?: Maybe<Scalars['Float']['output']>;
  seed_current?: Maybe<Scalars['Float']['output']>;
  seed_original?: Maybe<Scalars['Float']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type DivisionStanding = {
  __typename?: 'DivisionStanding';
  division_id: Scalars['ID']['output'];
  division_standing_id: Scalars['ID']['output'];
  heading: Scalars['String']['output'];
  heading_priority: Scalars['Float']['output'];
  matches_lost: Scalars['Float']['output'];
  matches_won: Scalars['Float']['output'];
  points?: Maybe<Scalars['Float']['output']>;
  points_ratio: Scalars['Float']['output'];
  rank: Scalars['Float']['output'];
  seed?: Maybe<Scalars['Float']['output']>;
  seed_original?: Maybe<Scalars['Float']['output']>;
  sets_lost: Scalars['Float']['output'];
  sets_pct: Scalars['Float']['output'];
  sets_won: Scalars['Float']['output'];
  team_id: Scalars['ID']['output'];
};

export type Event = {
  __typename?: 'Event';
  address?: Maybe<Scalars['String']['output']>;
  allow_point_of_sales?: Maybe<Scalars['Boolean']['output']>;
  athletes: Array<EventAthlete>;
  city?: Maybe<Scalars['String']['output']>;
  clubs: Array<Club>;
  date_start?: Maybe<Scalars['String']['output']>;
  days?: Maybe<Array<Scalars['String']['output']>>;
  divisions: Array<EventDivision>;
  event_id?: Maybe<Scalars['ID']['output']>;
  event_notes?: Maybe<Scalars['String']['output']>;
  has_officials?: Maybe<Scalars['Boolean']['output']>;
  has_rosters?: Maybe<Scalars['Boolean']['output']>;
  hide_seeds?: Maybe<Scalars['Boolean']['output']>;
  id?: Maybe<Scalars['Float']['output']>;
  is_require_recipient_name_for_each_ticket?: Maybe<Scalars['Boolean']['output']>;
  is_with_prev_qual?: Maybe<Scalars['Boolean']['output']>;
  locations?: Maybe<Array<EventLocation>>;
  long_name?: Maybe<Scalars['String']['output']>;
  modified?: Maybe<Scalars['DateTime']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  sales_hub_point_of_sale_id?: Maybe<Scalars['String']['output']>;
  schedule_published?: Maybe<Scalars['Boolean']['output']>;
  small_logo?: Maybe<Scalars['String']['output']>;
  sport_sanctioning?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  teams: Array<TeamList>;
  teams_settings?: Maybe<EventTeamsSettings>;
  tickets_code?: Maybe<Scalars['String']['output']>;
  tickets_published?: Maybe<Scalars['Boolean']['output']>;
};

export type EventAthlete = {
  __typename?: 'EventAthlete';
  age?: Maybe<Scalars['Int']['output']>;
  club_name: Scalars['String']['output'];
  division_id?: Maybe<Scalars['Float']['output']>;
  first?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  jersey?: Maybe<Scalars['Float']['output']>;
  last?: Maybe<Scalars['String']['output']>;
  roster_team_id?: Maybe<Scalars['Float']['output']>;
  team_name?: Maybe<Scalars['String']['output']>;
  team_organization_code?: Maybe<Scalars['String']['output']>;
};

export type EventBracket = {
  __typename?: 'EventBracket';
  display_name?: Maybe<Scalars['String']['output']>;
  division_name?: Maybe<Scalars['String']['output']>;
  matches?: Maybe<Array<BracketMatch>>;
  pool?: Maybe<BracketPool>;
};

export type EventDivision = {
  __typename?: 'EventDivision';
  division_id?: Maybe<Scalars['Float']['output']>;
  event_id?: Maybe<Scalars['ID']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  short_name?: Maybe<Scalars['String']['output']>;
  teams_count?: Maybe<Scalars['Float']['output']>;
};

export type EventDivisionDetails = {
  __typename?: 'EventDivisionDetails';
  standing: Array<Standing>;
};

export type EventLocation = {
  __typename?: 'EventLocation';
  address?: Maybe<Scalars['String']['output']>;
  city?: Maybe<Scalars['String']['output']>;
  location_name?: Maybe<Scalars['String']['output']>;
  number?: Maybe<Scalars['Float']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  zip?: Maybe<Scalars['String']['output']>;
};

export type EventMedia = {
  __typename?: 'EventMedia';
  division_id: Scalars['ID']['output'];
  file_type: Scalars['String']['output'];
  media_id: Scalars['ID']['output'];
  path: Scalars['String']['output'];
};

export type EventOfficial = {
  __typename?: 'EventOfficial';
  additional_restrictions?: Maybe<Scalars['String']['output']>;
  departure_time?: Maybe<Scalars['String']['output']>;
  event_official_id: Scalars['ID']['output'];
  external: EventOfficialExternal;
  official_additional_role?: Maybe<Scalars['String']['output']>;
  official_id: Scalars['ID']['output'];
  schedule_name?: Maybe<Scalars['String']['output']>;
  schedules: Array<EventOfficialSchedule>;
};

export type EventOfficialExternal = {
  __typename?: 'EventOfficialExternal';
  official_info: OfficialShortInfo;
  user_info: UserShortInfo;
};

export type EventOfficialSchedule = {
  __typename?: 'EventOfficialSchedule';
  court_id?: Maybe<Scalars['ID']['output']>;
  division_id: Scalars['ID']['output'];
  event_official_group_id?: Maybe<Scalars['ID']['output']>;
  event_official_id: Scalars['ID']['output'];
  match?: Maybe<Match>;
  match_id: Scalars['ID']['output'];
  match_name: Scalars['String']['output'];
  match_start_time?: Maybe<Scalars['Float']['output']>;
  published: Scalars['Boolean']['output'];
  schedule_id: Scalars['ID']['output'];
};

export type EventPool = {
  __typename?: 'EventPool';
  display_name?: Maybe<Scalars['String']['output']>;
  display_name_short?: Maybe<Scalars['String']['output']>;
  division_id?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  event_id?: Maybe<Scalars['Float']['output']>;
  is_pool?: Maybe<Scalars['Boolean']['output']>;
  next?: Maybe<NextPrevPool>;
  pb_finishes?: Maybe<PoolFuture>;
  prev?: Maybe<NextPrevPool>;
  r_uuid?: Maybe<Scalars['String']['output']>;
  results?: Maybe<Array<PoolResults>>;
  settings?: Maybe<PoolOrBracketSettings>;
  standings?: Maybe<Array<PoolStandings>>;
  team_count?: Maybe<Scalars['Float']['output']>;
  upcoming_matches?: Maybe<Array<PoolUpcomingMatches>>;
  uuid?: Maybe<Scalars['String']['output']>;
};

export type EventTeamRoster = {
  __typename?: 'EventTeamRoster';
  club_name?: Maybe<Scalars['String']['output']>;
  event_id: Scalars['ID']['output'];
  event_name?: Maybe<Scalars['String']['output']>;
  organization_code?: Maybe<Scalars['String']['output']>;
  roster_athletes?: Maybe<Array<EventTeamRosterAthlete>>;
  roster_staff?: Maybe<Array<EventTeamRosterStaff>>;
  roster_team_id?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  team_name?: Maybe<Scalars['String']['output']>;
};

export type EventTeamRosterAthlete = {
  __typename?: 'EventTeamRosterAthlete';
  first?: Maybe<Scalars['String']['output']>;
  gradyear?: Maybe<Scalars['Int']['output']>;
  id: Scalars['ID']['output'];
  last?: Maybe<Scalars['String']['output']>;
  short_position?: Maybe<Scalars['String']['output']>;
  uniform?: Maybe<Scalars['Int']['output']>;
};

export type EventTeamRosterStaff = {
  __typename?: 'EventTeamRosterStaff';
  first?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  last?: Maybe<Scalars['String']['output']>;
  role_name?: Maybe<Scalars['String']['output']>;
  sort_order?: Maybe<Scalars['Int']['output']>;
};

export type EventTeamsSettings = {
  __typename?: 'EventTeamsSettings';
  baller_tv_available?: Maybe<Scalars['Boolean']['output']>;
  hide_standings?: Maybe<Scalars['Boolean']['output']>;
  manual_club_names?: Maybe<Scalars['Boolean']['output']>;
  manual_teams_addition?: Maybe<Scalars['Boolean']['output']>;
  sort_by?: Maybe<Scalars['String']['output']>;
};

export type EventUpcoming = {
  __typename?: 'EventUpcoming';
  court_name?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['String']['output']>;
  date_start_formatted?: Maybe<Scalars['String']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_id?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  footnote_play?: Maybe<Scalars['String']['output']>;
  footnote_team1?: Maybe<Scalars['String']['output']>;
  footnote_team2?: Maybe<Scalars['String']['output']>;
  is_pool?: Maybe<Scalars['Float']['output']>;
  match_id?: Maybe<Scalars['String']['output']>;
  match_type?: Maybe<Scalars['String']['output']>;
  opponent_organization_code?: Maybe<Scalars['String']['output']>;
  opponent_team_name?: Maybe<Scalars['String']['output']>;
  pb_name?: Maybe<Scalars['String']['output']>;
  pool_bracket_id?: Maybe<Scalars['String']['output']>;
  pool_name?: Maybe<Scalars['String']['output']>;
  results?: Maybe<Scalars['String']['output']>;
  round_name?: Maybe<Scalars['String']['output']>;
  settings?: Maybe<PoolOrBracketSettings>;
  unix_finished?: Maybe<Scalars['String']['output']>;
};

export type FeatureMatches = {
  __typename?: 'FeatureMatches';
  court_name?: Maybe<Scalars['String']['output']>;
  court_short_name?: Maybe<Scalars['String']['output']>;
  division_id?: Maybe<Scalars['String']['output']>;
  match_id?: Maybe<Scalars['String']['output']>;
  match_start?: Maybe<Scalars['String']['output']>;
  team1_id?: Maybe<Scalars['String']['output']>;
  team1_name?: Maybe<Scalars['String']['output']>;
  team2_id?: Maybe<Scalars['String']['output']>;
  team2_name?: Maybe<Scalars['String']['output']>;
};

export type FilterInfo = {
  __typename?: 'FilterInfo';
  filtered_items: Scalars['Float']['output'];
  is_filtered: Scalars['Boolean']['output'];
  total_items: Scalars['Float']['output'];
};

export type FilteredMatches = {
  __typename?: 'FilteredMatches';
  filter_info: FilterInfo;
  items: Array<Match>;
};

export type Match = {
  __typename?: 'Match';
  court_id: Scalars['ID']['output'];
  division_id: Scalars['ID']['output'];
  division_name?: Maybe<Scalars['String']['output']>;
  external: MatchExternal;
  finishes?: Maybe<MatchFinishes>;
  is_tb?: Maybe<Scalars['Float']['output']>;
  match_id: Scalars['ID']['output'];
  match_name?: Maybe<Scalars['String']['output']>;
  match_number?: Maybe<Scalars['Float']['output']>;
  opponent_id?: Maybe<Scalars['ID']['output']>;
  pool_bracket_id: Scalars['ID']['output'];
  ref_team_id?: Maybe<Scalars['ID']['output']>;
  results?: Maybe<MatchResults>;
  secs_end?: Maybe<Scalars['Float']['output']>;
  secs_finished?: Maybe<Scalars['Float']['output']>;
  secs_start?: Maybe<Scalars['Float']['output']>;
  source?: Maybe<MatchSource>;
  team_id?: Maybe<Scalars['ID']['output']>;
};

export type MatchExternal = {
  __typename?: 'MatchExternal';
  court_info: CourtShortInfo;
  has_finishes: Scalars['Boolean']['output'];
  opponent_display_name: Scalars['String']['output'];
  opponent_info?: Maybe<TeamShortInfo>;
  opponent_pb_seed?: Maybe<PoolOrBracketSeedItem>;
  pool_bracket_info: PoolOrBracketShortInfo;
  ref_team_display_name: Scalars['String']['output'];
  ref_team_info?: Maybe<TeamShortInfo>;
  ref_team_pb_seed?: Maybe<PoolOrBracketSeedItem>;
  team_display_name: Scalars['String']['output'];
  team_info?: Maybe<TeamShortInfo>;
  team_pb_seed?: Maybe<PoolOrBracketSeedItem>;
};

export type MatchFinishes = {
  __typename?: 'MatchFinishes';
  looser?: Maybe<TeamAdvancement>;
  winner?: Maybe<TeamAdvancement>;
};

export type MatchReference = {
  __typename?: 'MatchReference';
  court?: Maybe<Scalars['String']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  external?: Maybe<MatchReferenceExternal>;
  match_id?: Maybe<Scalars['String']['output']>;
  secs_start?: Maybe<Scalars['Float']['output']>;
  start_time_string?: Maybe<Scalars['String']['output']>;
  week_day?: Maybe<Scalars['String']['output']>;
};

export type MatchReferenceExternal = {
  __typename?: 'MatchReferenceExternal';
  pool_bracket_info?: Maybe<PoolOrBracketShortInfo>;
};

export type MatchResults = {
  __typename?: 'MatchResults';
  sets: Array<Scalars['String']['output']>;
  team1?: Maybe<MatchTeamResults>;
  team2?: Maybe<MatchTeamResults>;
  winner?: Maybe<Scalars['String']['output']>;
  winner_team?: Maybe<MatchTeamResults>;
};

export type MatchSource = {
  __typename?: 'MatchSource';
  opponent?: Maybe<MatchSourceItem>;
  ref?: Maybe<MatchSourceItem>;
  team?: Maybe<MatchSourceItem>;
};

export type MatchSourceItem = {
  __typename?: 'MatchSourceItem';
  id?: Maybe<Scalars['ID']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  overallSeed?: Maybe<Scalars['Float']['output']>;
  reseedRank?: Maybe<Scalars['Float']['output']>;
  seed?: Maybe<Scalars['Float']['output']>;
  type?: Maybe<Scalars['Float']['output']>;
};

export type MatchTeamResults = {
  __typename?: 'MatchTeamResults';
  roster_team_id?: Maybe<Scalars['String']['output']>;
  scores?: Maybe<Scalars['String']['output']>;
};

export type MatchesTimeRange = {
  __typename?: 'MatchesTimeRange';
  day: Scalars['String']['output'];
  division_id: Scalars['ID']['output'];
  end_time: Scalars['String']['output'];
  start_time: Scalars['String']['output'];
};

/** Generate a link to the next pool */
export type NextPrevPool = {
  __typename?: 'NextPrevPool';
  is_pool?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  uuid?: Maybe<Scalars['String']['output']>;
};

export type OfficialShortInfo = {
  __typename?: 'OfficialShortInfo';
  official_id: Scalars['ID']['output'];
  rank?: Maybe<Scalars['String']['output']>;
  user_id: Scalars['ID']['output'];
};

export type PageInfo = {
  __typename?: 'PageInfo';
  item_count: Scalars['Float']['output'];
  page: Scalars['Float']['output'];
  page_count: Scalars['Float']['output'];
  page_size: Scalars['Float']['output'];
};

export type PaginatedAthletes = {
  __typename?: 'PaginatedAthletes';
  items: Array<Athlete>;
  page_info: PageInfo;
};

export type PaginatedClubs = {
  __typename?: 'PaginatedClubs';
  items: Array<Club>;
  page_info: PageInfo;
};

export type PaginatedEvents = {
  __typename?: 'PaginatedEvents';
  items: Array<Event>;
  page_info: PageInfo;
};

export type PaginatedStaff = {
  __typename?: 'PaginatedStaff';
  items: Array<Staff>;
  page_info: PageInfo;
};

export type PaginatedTeams = {
  __typename?: 'PaginatedTeams';
  items: Array<Team>;
  page_info: PageInfo;
};

export type Pool = PoolOrBracketBase & {
  __typename?: 'Pool';
  consolation?: Maybe<Scalars['Float']['output']>;
  court_short_name?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['Float']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_id: Scalars['ID']['output'];
  external: PoolOrBracketExternal;
  group_id?: Maybe<Scalars['ID']['output']>;
  is_pool: Scalars['Float']['output'];
  match_count: Scalars['Float']['output'];
  matches: Array<Match>;
  name?: Maybe<Scalars['String']['output']>;
  pb_finishes: Array<TeamAdvancement>;
  pb_seeds: Array<PoolOrBracketSeedItem>;
  pb_stats: Array<PoolOrBracketStatItem>;
  round_id: Scalars['ID']['output'];
  settings?: Maybe<PoolOrBracketSettings>;
  short_name?: Maybe<Scalars['String']['output']>;
  sort_priority: Scalars['Float']['output'];
  team_count: Scalars['Float']['output'];
  team_pb_stat?: Maybe<PoolOrBracketStatItem>;
  teams: Array<Team>;
  uuid: Scalars['ID']['output'];
};


export type PoolTeamsArgs = {
  filterTeamsIds?: InputMaybe<Array<Scalars['ID']['input']>>;
};

export type PoolFuture = {
  __typename?: 'PoolFuture';
  team_ids: Array<Scalars['String']['output']>;
  teams: Array<TeamAdvancement>;
};

export type PoolOrBracket = Bracket | Pool;

export type PoolOrBracketBase = {
  consolation?: Maybe<Scalars['Float']['output']>;
  court_short_name?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['Float']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_id: Scalars['ID']['output'];
  external: PoolOrBracketExternal;
  group_id?: Maybe<Scalars['ID']['output']>;
  is_pool: Scalars['Float']['output'];
  match_count: Scalars['Float']['output'];
  matches: Array<Match>;
  name?: Maybe<Scalars['String']['output']>;
  pb_finishes: Array<TeamAdvancement>;
  pb_seeds: Array<PoolOrBracketSeedItem>;
  pb_stats: Array<PoolOrBracketStatItem>;
  round_id: Scalars['ID']['output'];
  settings?: Maybe<PoolOrBracketSettings>;
  short_name?: Maybe<Scalars['String']['output']>;
  sort_priority: Scalars['Float']['output'];
  team_count: Scalars['Float']['output'];
  team_pb_stat?: Maybe<PoolOrBracketStatItem>;
  teams: Array<Team>;
  uuid: Scalars['ID']['output'];
};


export type PoolOrBracketBaseTeamsArgs = {
  filterTeamsIds?: InputMaybe<Array<Scalars['ID']['input']>>;
};

export type PoolOrBracketExternal = {
  __typename?: 'PoolOrBracketExternal';
  courts_short_info: Array<CourtShortInfo>;
};

export type PoolOrBracketSeedItem = {
  __typename?: 'PoolOrBracketSeedItem';
  id?: Maybe<Scalars['ID']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  overallSeed?: Maybe<Scalars['Float']['output']>;
  reseedRank?: Maybe<Scalars['Float']['output']>;
  seed?: Maybe<Scalars['Float']['output']>;
  type?: Maybe<Scalars['Float']['output']>;
};

export type PoolOrBracketSettings = {
  __typename?: 'PoolOrBracketSettings';
  DoubleRR?: Maybe<Scalars['Boolean']['output']>;
  Duration?: Maybe<Scalars['Float']['output']>;
  FinalPoints?: Maybe<Scalars['Float']['output']>;
  NoWinner?: Maybe<Scalars['Boolean']['output']>;
  PlayAllSets?: Maybe<Scalars['Boolean']['output']>;
  SetCount?: Maybe<Scalars['Float']['output']>;
  WinningPoints?: Maybe<Scalars['Float']['output']>;
};

export type PoolOrBracketShortInfo = PoolOrBracketShortInfoInterface & {
  __typename?: 'PoolOrBracketShortInfo';
  is_pool: Scalars['Float']['output'];
  uuid: Scalars['ID']['output'];
};

export type PoolOrBracketShortInfoInterface = {
  is_pool: Scalars['Float']['output'];
  uuid: Scalars['ID']['output'];
};

export type PoolOrBracketStatItem = {
  __typename?: 'PoolOrBracketStatItem';
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_pct?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  points_lost?: Maybe<Scalars['Float']['output']>;
  points_ratio?: Maybe<Scalars['Float']['output']>;
  points_won?: Maybe<Scalars['Float']['output']>;
  rank?: Maybe<Scalars['Float']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
  team_id?: Maybe<Scalars['ID']['output']>;
};

export type PoolResults = {
  __typename?: 'PoolResults';
  court_name?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['String']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  match_id?: Maybe<Scalars['String']['output']>;
  ref_roster_id?: Maybe<Scalars['String']['output']>;
  ref_team_code?: Maybe<Scalars['String']['output']>;
  ref_team_name?: Maybe<Scalars['String']['output']>;
  results: PoolResultsResults;
  team1_code?: Maybe<Scalars['String']['output']>;
  team1_master_id?: Maybe<Scalars['Float']['output']>;
  team1_name?: Maybe<Scalars['String']['output']>;
  team1_roster_id?: Maybe<Scalars['Float']['output']>;
  team2_code?: Maybe<Scalars['String']['output']>;
  team2_master_id?: Maybe<Scalars['Float']['output']>;
  team2_name?: Maybe<Scalars['String']['output']>;
  team2_roster_id?: Maybe<Scalars['Float']['output']>;
};

export type PoolResultsResults = {
  __typename?: 'PoolResultsResults';
  set1?: Maybe<Scalars['String']['output']>;
  set2?: Maybe<Scalars['String']['output']>;
  set3?: Maybe<Scalars['String']['output']>;
  set4?: Maybe<Scalars['String']['output']>;
  set5?: Maybe<Scalars['String']['output']>;
  team1?: Maybe<PoolResultsResultsTeam>;
  team2?: Maybe<PoolResultsResultsTeam>;
};

export type PoolResultsResultsTeam = {
  __typename?: 'PoolResultsResultsTeam';
  heading?: Maybe<Scalars['String']['output']>;
  heading_sort?: Maybe<Scalars['Float']['output']>;
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_pct?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  overallSeed?: Maybe<Scalars['Float']['output']>;
  points_lost?: Maybe<Scalars['Float']['output']>;
  points_ratio?: Maybe<Scalars['Float']['output']>;
  points_won?: Maybe<Scalars['Float']['output']>;
  roster_team_id?: Maybe<Scalars['Float']['output']>;
  scores?: Maybe<Scalars['String']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type PoolStandings = {
  __typename?: 'PoolStandings';
  display_name?: Maybe<Scalars['String']['output']>;
  division_name?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  pb_stats: Array<PoolStandingsStats>;
};

export type PoolStandingsStats = {
  __typename?: 'PoolStandingsStats';
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_pct?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  points_lost?: Maybe<Scalars['Float']['output']>;
  points_ratio?: Maybe<Scalars['Float']['output']>;
  points_won?: Maybe<Scalars['Float']['output']>;
  rank?: Maybe<Scalars['String']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
  team_id?: Maybe<Scalars['String']['output']>;
};

export type PoolUpcomingMatches = {
  __typename?: 'PoolUpcomingMatches';
  court_name?: Maybe<Scalars['String']['output']>;
  date_start?: Maybe<Scalars['Float']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  footnote_play?: Maybe<Scalars['String']['output']>;
  footnote_team1?: Maybe<Scalars['String']['output']>;
  footnote_team2?: Maybe<Scalars['String']['output']>;
  match_id?: Maybe<Scalars['String']['output']>;
  ref_roster_id?: Maybe<Scalars['String']['output']>;
  ref_team_code?: Maybe<Scalars['String']['output']>;
  ref_team_name?: Maybe<Scalars['String']['output']>;
  results?: Maybe<Scalars['String']['output']>;
  team1_code?: Maybe<Scalars['String']['output']>;
  team1_master_id?: Maybe<Scalars['String']['output']>;
  team1_name?: Maybe<Scalars['String']['output']>;
  team1_roster_id?: Maybe<Scalars['String']['output']>;
  team2_code?: Maybe<Scalars['String']['output']>;
  team2_master_id?: Maybe<Scalars['String']['output']>;
  team2_name?: Maybe<Scalars['String']['output']>;
  team2_roster_id?: Maybe<Scalars['String']['output']>;
};

export type Query = {
  __typename?: 'Query';
  bracket?: Maybe<EventBracket>;
  courtMatches: CourtGrid;
  courts: Array<Court>;
  dayRangeMatches: FilteredMatches;
  divisionDetails?: Maybe<EventDivisionDetails>;
  divisionPoolBrackets: Array<PoolOrBracket>;
  divisionRounds: Array<Round>;
  divisionTeamsStanding: Array<Team>;
  divisions: Array<Division>;
  event?: Maybe<Event>;
  eventOfficials: Array<EventOfficial>;
  eventOfficialsSchedules: Array<EventOfficialSchedule>;
  favoriteTeams: Array<Team>;
  featureMatches: Array<FeatureMatches>;
  paginatedAthletes: PaginatedAthletes;
  paginatedClubs: PaginatedClubs;
  paginatedDivisionTeams: PaginatedTeams;
  paginatedEvents: PaginatedEvents;
  paginatedStaff: PaginatedStaff;
  pool: EventPool;
  poolIdByTeamId?: Maybe<EventPool>;
  pools: Array<DivisionPool>;
  qualifiedTeams: Array<Team>;
  relatedCourts: Array<Court>;
  roster: Array<EventTeamRoster>;
  teamSingle: TeamSingle;
  upcomingMatches?: Maybe<Array<UpcomingMatches>>;
};


export type QueryBracketArgs = {
  id: Scalars['ID']['input'];
  poolId: Scalars['String']['input'];
};


export type QueryCourtMatchesArgs = {
  day: Scalars['String']['input'];
  division: Scalars['String']['input'];
  hour: Scalars['String']['input'];
  hours: Scalars['String']['input'];
  id: Scalars['ID']['input'];
};


export type QueryCourtsArgs = {
  eventKey: Scalars['ID']['input'];
};


export type QueryDayRangeMatchesArgs = {
  after: Scalars['String']['input'];
  before: Scalars['String']['input'];
  divisionId?: InputMaybe<Scalars['ID']['input']>;
  eventKey: Scalars['ID']['input'];
};


export type QueryDivisionDetailsArgs = {
  divisionIds: Array<Scalars['ID']['input']>;
  id: Scalars['ID']['input'];
};


export type QueryDivisionPoolBracketsArgs = {
  divisionId: Scalars['ID']['input'];
  eventKey: Scalars['ID']['input'];
};


export type QueryDivisionRoundsArgs = {
  divisionId: Scalars['ID']['input'];
  eventKey: Scalars['ID']['input'];
};


export type QueryDivisionTeamsStandingArgs = {
  divisionId: Scalars['ID']['input'];
  eventKey: Scalars['ID']['input'];
};


export type QueryDivisionsArgs = {
  eventKey: Scalars['ID']['input'];
};


export type QueryEventArgs = {
  id: Scalars['ID']['input'];
};


export type QueryEventOfficialsArgs = {
  eventKey: Scalars['ID']['input'];
};


export type QueryEventOfficialsSchedulesArgs = {
  eventKey: Scalars['ID']['input'];
};


export type QueryFavoriteTeamsArgs = {
  eventKey: Scalars['ID']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
  teamsIds: Array<Scalars['ID']['input']>;
};


export type QueryFeatureMatchesArgs = {
  id: Scalars['ID']['input'];
};


export type QueryPaginatedAthletesArgs = {
  eventKey: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryPaginatedClubsArgs = {
  eventKey: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryPaginatedDivisionTeamsArgs = {
  divisionId: Scalars['ID']['input'];
  eventKey: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryPaginatedEventsArgs = {
  asc?: InputMaybe<Scalars['Boolean']['input']>;
  endAfter?: InputMaybe<Scalars['String']['input']>;
  endBefore?: InputMaybe<Scalars['String']['input']>;
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
  startAfter?: InputMaybe<Scalars['String']['input']>;
  startBefore?: InputMaybe<Scalars['String']['input']>;
  years?: InputMaybe<Array<Scalars['String']['input']>>;
};


export type QueryPaginatedStaffArgs = {
  eventKey: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
};


export type QueryPoolArgs = {
  id: Scalars['ID']['input'];
  poolId: Scalars['ID']['input'];
};


export type QueryPoolIdByTeamIdArgs = {
  id: Scalars['ID']['input'];
  poolId?: InputMaybe<Scalars['ID']['input']>;
  teamId: Scalars['ID']['input'];
};


export type QueryPoolsArgs = {
  divisionId: Scalars['ID']['input'];
  id: Scalars['ID']['input'];
};


export type QueryQualifiedTeamsArgs = {
  eventKey: Scalars['ID']['input'];
};


export type QueryRelatedCourtsArgs = {
  uniqKey?: InputMaybe<Scalars['String']['input']>;
};


export type QueryRosterArgs = {
  id: Scalars['ID']['input'];
};


export type QueryTeamSingleArgs = {
  id: Scalars['ID']['input'];
  teamId: Scalars['ID']['input'];
};


export type QueryUpcomingMatchesArgs = {
  id: Scalars['ID']['input'];
};

export type Round = {
  __typename?: 'Round';
  division_id: Scalars['ID']['output'];
  first_match_start?: Maybe<Scalars['Float']['output']>;
  last_match_start?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  pool_brackets: Array<PoolOrBracket>;
  short_name?: Maybe<Scalars['String']['output']>;
  sort_priority: Scalars['Float']['output'];
  uuid: Scalars['ID']['output'];
};

export type Staff = {
  __typename?: 'Staff';
  club_name: Scalars['String']['output'];
  first: Scalars['String']['output'];
  last: Scalars['String']['output'];
  organization_code?: Maybe<Scalars['String']['output']>;
  role_name?: Maybe<Scalars['String']['output']>;
  staff_id: Scalars['ID']['output'];
  state?: Maybe<Scalars['String']['output']>;
  team_id: Scalars['ID']['output'];
  team_name: Scalars['String']['output'];
};

export type Standing = {
  __typename?: 'Standing';
  heading_group: Scalars['String']['output'];
  teams: Array<TeamDetails>;
};

export type StandingInfo = {
  __typename?: 'StandingInfo';
  points?: Maybe<Scalars['Float']['output']>;
};

export type Team = {
  __typename?: 'Team';
  athletes: Array<Athlete>;
  club_id: Scalars['ID']['output'];
  division_id: Scalars['ID']['output'];
  division_name?: Maybe<Scalars['String']['output']>;
  division_standing?: Maybe<DivisionStanding>;
  external: TeamExternal;
  extra?: Maybe<TeamExtra>;
  finished_matches: Array<Match>;
  manual_club_name?: Maybe<Scalars['String']['output']>;
  master_team_id?: Maybe<Scalars['ID']['output']>;
  matches: Array<Match>;
  next_match?: Maybe<Match>;
  ongoing_pool_bracket?: Maybe<PoolOrBracket>;
  pool_bracket_stat?: Maybe<PoolOrBracketStatItem>;
  staff: Array<Staff>;
  team_code?: Maybe<Scalars['String']['output']>;
  team_id: Scalars['ID']['output'];
  team_name: Scalars['String']['output'];
  upcoming_matches: Array<Match>;
};


export type TeamFinished_MatchesArgs = {
  as_ref?: InputMaybe<Scalars['Boolean']['input']>;
  as_team?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  pb_only?: InputMaybe<Scalars['Boolean']['input']>;
};


export type TeamMatchesArgs = {
  as_ref?: InputMaybe<Scalars['Boolean']['input']>;
  as_team?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  pb_only?: InputMaybe<Scalars['Boolean']['input']>;
};


export type TeamUpcoming_MatchesArgs = {
  as_ref?: InputMaybe<Scalars['Boolean']['input']>;
  as_team?: InputMaybe<Scalars['Boolean']['input']>;
  limit?: InputMaybe<Scalars['Int']['input']>;
  pb_only?: InputMaybe<Scalars['Boolean']['input']>;
};

export type TeamAdvancement = {
  __typename?: 'TeamAdvancement';
  next_match?: Maybe<MatchReference>;
  next_ref?: Maybe<MatchReference>;
  team_id?: Maybe<Scalars['Float']['output']>;
  team_name?: Maybe<Scalars['String']['output']>;
};

export type TeamDetails = {
  __typename?: 'TeamDetails';
  division_id?: Maybe<Scalars['Float']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  info?: Maybe<StandingInfo>;
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  organization_code?: Maybe<Scalars['String']['output']>;
  points_ratio?: Maybe<Scalars['Float']['output']>;
  rank?: Maybe<Scalars['Float']['output']>;
  roster_team_id?: Maybe<Scalars['Float']['output']>;
  seed_current?: Maybe<Scalars['Float']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
  team_name?: Maybe<Scalars['String']['output']>;
};

export type TeamExternal = {
  __typename?: 'TeamExternal';
  club_info: ClubShortInfo;
};

export type TeamExtra = {
  __typename?: 'TeamExtra';
  earned_at?: Maybe<Scalars['String']['output']>;
  prev_qual?: Maybe<Scalars['Boolean']['output']>;
  prev_qual_age?: Maybe<Scalars['String']['output']>;
  prev_qual_division?: Maybe<Scalars['String']['output']>;
  show_accepted_bid?: Maybe<Scalars['Boolean']['output']>;
  show_previously_accepted_bid?: Maybe<Scalars['String']['output']>;
};

export type TeamList = {
  __typename?: 'TeamList';
  club_name?: Maybe<Scalars['String']['output']>;
  club_state?: Maybe<Scalars['String']['output']>;
  division_id?: Maybe<Scalars['Float']['output']>;
  division_name?: Maybe<Scalars['String']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  organization_code?: Maybe<Scalars['String']['output']>;
  roster_club_id?: Maybe<Scalars['Float']['output']>;
  roster_team_id?: Maybe<Scalars['Float']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  team_name?: Maybe<Scalars['String']['output']>;
};

export type TeamShortInfo = {
  __typename?: 'TeamShortInfo';
  master_team_id?: Maybe<Scalars['ID']['output']>;
  team_id: Scalars['ID']['output'];
  team_name: Scalars['String']['output'];
};

export type TeamSingle = {
  __typename?: 'TeamSingle';
  athletes?: Maybe<Array<TeamSingleAthlete>>;
  bracket_finishes?: Maybe<MatchFinishes>;
  club_name?: Maybe<Scalars['String']['output']>;
  division_id?: Maybe<Scalars['Float']['output']>;
  manual_club_name?: Maybe<Scalars['String']['output']>;
  master_team_id?: Maybe<Scalars['Float']['output']>;
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  organization_code?: Maybe<Scalars['String']['output']>;
  pb_info?: Maybe<Array<TeamSinglePbInfo>>;
  points_lost?: Maybe<Scalars['Float']['output']>;
  points_won?: Maybe<Scalars['Float']['output']>;
  results?: Maybe<Array<TeamSingleResults>>;
  roster_club_id?: Maybe<Scalars['Float']['output']>;
  roster_team_id?: Maybe<Scalars['Float']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
  staff?: Maybe<Array<TeamSingleStaff>>;
  state?: Maybe<Scalars['String']['output']>;
  team_name?: Maybe<Scalars['String']['output']>;
  upcoming?: Maybe<Array<EventUpcoming>>;
};

export type TeamSingleAthlete = {
  __typename?: 'TeamSingleAthlete';
  first?: Maybe<Scalars['String']['output']>;
  gradyear?: Maybe<Scalars['Float']['output']>;
  height?: Maybe<Scalars['String']['output']>;
  last?: Maybe<Scalars['String']['output']>;
  short_position?: Maybe<Scalars['String']['output']>;
  uniform?: Maybe<Scalars['Float']['output']>;
};

export type TeamSingleMatch = {
  __typename?: 'TeamSingleMatch';
  date_start?: Maybe<Scalars['Float']['output']>;
  date_start_formatted?: Maybe<Scalars['String']['output']>;
  display_name?: Maybe<Scalars['String']['output']>;
  division_id?: Maybe<Scalars['Float']['output']>;
  division_short_name?: Maybe<Scalars['String']['output']>;
  match_id?: Maybe<Scalars['String']['output']>;
  match_type?: Maybe<Scalars['String']['output']>;
  opponent_organization_code?: Maybe<Scalars['String']['output']>;
  opponent_team_id?: Maybe<Scalars['Float']['output']>;
  opponent_team_name?: Maybe<Scalars['String']['output']>;
  pool_bracket_id?: Maybe<Scalars['String']['output']>;
  results?: Maybe<TeamSingleMatchResults>;
  unix_finished?: Maybe<Scalars['Float']['output']>;
};

export type TeamSingleMatchResults = {
  __typename?: 'TeamSingleMatchResults';
  set1?: Maybe<Scalars['String']['output']>;
  set2?: Maybe<Scalars['String']['output']>;
  set3?: Maybe<Scalars['String']['output']>;
  set4?: Maybe<Scalars['String']['output']>;
  set5?: Maybe<Scalars['String']['output']>;
  team1: TeamSingleMatchResultsTeam;
  team2: TeamSingleMatchResultsTeam;
  winner?: Maybe<Scalars['String']['output']>;
};

export type TeamSingleMatchResultsTeam = {
  __typename?: 'TeamSingleMatchResultsTeam';
  heading?: Maybe<Scalars['String']['output']>;
  heading_sort?: Maybe<Scalars['Float']['output']>;
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_pct?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  overallSeed?: Maybe<Scalars['Float']['output']>;
  points_lost?: Maybe<Scalars['Float']['output']>;
  points_ratio?: Maybe<Scalars['Float']['output']>;
  points_won?: Maybe<Scalars['Float']['output']>;
  roster_team_id?: Maybe<Scalars['Float']['output']>;
  scores?: Maybe<Scalars['String']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type TeamSinglePbInfo = {
  __typename?: 'TeamSinglePbInfo';
  is_pool?: Maybe<Scalars['Float']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  pb_finishes?: Maybe<PoolFuture>;
  uuid?: Maybe<Scalars['String']['output']>;
};

export type TeamSingleResults = {
  __typename?: 'TeamSingleResults';
  is_pool?: Maybe<Scalars['Float']['output']>;
  matches: Array<TeamSingleMatch>;
  pb_name?: Maybe<Scalars['String']['output']>;
  pb_stats?: Maybe<Array<PoolStandingsStats>>;
  round_name?: Maybe<Scalars['String']['output']>;
  sort_priority?: Maybe<Scalars['Float']['output']>;
  uuid?: Maybe<Scalars['String']['output']>;
};

export type TeamSingleStaff = {
  __typename?: 'TeamSingleStaff';
  first?: Maybe<Scalars['String']['output']>;
  last?: Maybe<Scalars['String']['output']>;
  role_name?: Maybe<Scalars['String']['output']>;
  sort_order?: Maybe<Scalars['Float']['output']>;
};

export type UpcomingMatches = {
  __typename?: 'UpcomingMatches';
  club_code?: Maybe<Scalars['String']['output']>;
  club_id?: Maybe<Scalars['Float']['output']>;
  club_name?: Maybe<Scalars['String']['output']>;
  club_state?: Maybe<Scalars['String']['output']>;
  court_name?: Maybe<Scalars['String']['output']>;
  division_id?: Maybe<Scalars['Float']['output']>;
  division_name?: Maybe<Scalars['String']['output']>;
  matches_lost?: Maybe<Scalars['Float']['output']>;
  matches_won?: Maybe<Scalars['Float']['output']>;
  opponent_id?: Maybe<Scalars['Float']['output']>;
  opponent_name?: Maybe<Scalars['String']['output']>;
  secs_finished?: Maybe<Scalars['Float']['output']>;
  secs_start?: Maybe<Scalars['Float']['output']>;
  seed?: Maybe<Scalars['Float']['output']>;
  sets_lost?: Maybe<Scalars['Float']['output']>;
  sets_pct?: Maybe<Scalars['Float']['output']>;
  sets_won?: Maybe<Scalars['Float']['output']>;
  team_code?: Maybe<Scalars['String']['output']>;
  team_id?: Maybe<Scalars['Float']['output']>;
  team_name?: Maybe<Scalars['String']['output']>;
};

export type UserShortInfo = {
  __typename?: 'UserShortInfo';
  first?: Maybe<Scalars['String']['output']>;
  last?: Maybe<Scalars['String']['output']>;
  user_id: Scalars['ID']['output'];
};

export type EventWithRosterAndUpcomingMatchesQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type EventWithRosterAndUpcomingMatchesQuery = { __typename?: 'Query', upcomingMatches?: Array<{ __typename?: 'UpcomingMatches', division_id?: number | null, division_name?: string | null, club_id?: number | null, club_name?: string | null, club_state?: string | null, club_code?: string | null, team_code?: string | null, team_id?: number | null, team_name?: string | null, opponent_id?: number | null, opponent_name?: string | null, secs_start?: number | null, secs_finished?: number | null, court_name?: string | null, matches_won?: number | null, matches_lost?: number | null, sets_won?: number | null, sets_lost?: number | null, sets_pct?: number | null, seed?: number | null }> | null, roster: Array<{ __typename?: 'EventTeamRoster', event_id: string, event_name?: string | null, state?: string | null, club_name?: string | null, organization_code?: string | null, team_name?: string | null, roster_team_id?: string | null, roster_athletes?: Array<{ __typename?: 'EventTeamRosterAthlete', id: string, first?: string | null, last?: string | null, short_position?: string | null, uniform?: number | null, gradyear?: number | null }> | null, roster_staff?: Array<{ __typename?: 'EventTeamRosterStaff', id: string, first?: string | null, last?: string | null, role_name?: string | null, sort_order?: number | null }> | null }>, event?: { __typename?: 'Event', event_id?: string | null, hide_seeds?: boolean | null, name?: string | null, long_name?: string | null, days?: Array<string> | null, city?: string | null, state?: string | null, event_notes?: string | null, address?: string | null, is_with_prev_qual?: boolean | null, tickets_published?: boolean | null, tickets_code?: string | null, is_require_recipient_name_for_each_ticket?: boolean | null, locations?: Array<{ __typename?: 'EventLocation', location_name?: string | null }> | null, divisions: Array<{ __typename?: 'EventDivision', division_id?: number | null }>, clubs: Array<{ __typename?: 'Club', roster_club_id: string }>, athletes: Array<{ __typename?: 'EventAthlete', first?: string | null, last?: string | null }>, teams: Array<{ __typename?: 'TeamList', division_name?: string | null, division_short_name?: string | null, club_name?: string | null, team_name?: string | null, organization_code?: string | null, roster_team_id?: number | null, roster_club_id?: number | null, division_id?: number | null }>, teams_settings?: { __typename?: 'EventTeamsSettings', sort_by?: string | null, hide_standings?: boolean | null } | null } | null };

export type PaginatedAthletesQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
}>;


export type PaginatedAthletesQuery = { __typename?: 'Query', paginatedAthletes: { __typename?: 'PaginatedAthletes', items: Array<{ __typename?: 'Athlete', athlete_id: string, first: string, last: string, club_name: string, team_id: string, team_name: string, organization_code?: string | null, state?: string | null, short_position?: string | null, uniform?: string | null }>, page_info: { __typename?: 'PageInfo', page: number, page_size: number, page_count: number, item_count: number } } };

export type BracketQueryVariables = Exact<{
  id: Scalars['ID']['input'];
  poolId: Scalars['String']['input'];
}>;


export type BracketQuery = { __typename?: 'Query', bracket?: { __typename?: 'EventBracket', display_name?: string | null, division_name?: string | null, pool?: { __typename?: 'BracketPool', flow_chart?: string | null } | null, matches?: Array<{ __typename?: 'BracketMatch', team1_roster_id?: number | null, team2_roster_id?: number | null, team1_name?: string | null, team2_name?: string | null, match_number?: number | null, date_start?: number | null, court_name?: string | null, ref_name?: string | null, show_previously_accepted_bid_team1?: string | null, show_previously_accepted_bid_team2?: string | null, source?: { __typename?: 'BracketMatchSourceTeam', ref?: { __typename?: 'BracketMatchSourceTeamItem', id?: string | null, name?: string | null, seed?: number | null } | null, team1?: { __typename?: 'BracketMatchSourceTeamItem', id?: string | null, name?: string | null, seed?: number | null } | null, team2?: { __typename?: 'BracketMatchSourceTeamItem', id?: string | null, name?: string | null, seed?: number | null } | null } | null, results?: { __typename?: 'BracketMatchResults', set1?: string | null, set2?: string | null, set3?: string | null, set4?: string | null, set5?: string | null, winner?: string | null, team1?: { __typename?: 'BracketMatchResultsTeam', sets_won?: number | null, sets_lost?: number | null, points_won?: number | null, points_lost?: number | null, sets_pct?: number | null, matches_lost?: number | null, matches_won?: number | null, matches_pct?: number | null, scores?: string | null, roster_team_id?: number | null } | null, team2?: { __typename?: 'BracketMatchResultsTeam', sets_won?: number | null, sets_lost?: number | null, points_won?: number | null, points_lost?: number | null, sets_pct?: number | null, matches_lost?: number | null, matches_won?: number | null, matches_pct?: number | null, scores?: string | null, roster_team_id?: number | null } | null } | null }> | null } | null };

export type PaginatedClubsTeamsQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
}>;


export type PaginatedClubsTeamsQuery = { __typename?: 'Query', paginatedClubs: { __typename?: 'PaginatedClubs', items: Array<{ __typename?: 'Club', roster_club_id: string, club_name: string, state?: string | null, club_code: string, teams_count: number, teams: Array<{ __typename?: 'Team', team_id: string, club_id: string, team_name: string, division_id: string, division_name?: string | null, manual_club_name?: string | null, division_standing?: { __typename?: 'DivisionStanding', matches_won: number, matches_lost: number, sets_won: number, sets_lost: number, seed?: number | null, rank: number } | null, next_match?: { __typename?: 'Match', secs_start?: number | null, external: { __typename?: 'MatchExternal', opponent_display_name: string, court_info: { __typename?: 'CourtShortInfo', short_name?: string | null } } } | null }> }>, page_info: { __typename?: 'PageInfo', page: number, page_size: number, page_count: number, item_count: number } } };

export type CourtMatchesQueryVariables = Exact<{
  id: Scalars['ID']['input'];
  day: Scalars['String']['input'];
  hour: Scalars['String']['input'];
  hours: Scalars['String']['input'];
  division: Scalars['String']['input'];
}>;


export type CourtMatchesQuery = { __typename?: 'Query', courtMatches: { __typename?: 'CourtGrid', divisions?: Array<{ __typename?: 'CourtMatchesDivision', division_id?: number | null, division_name?: string | null, gender?: string | null, sort_order?: number | null, max_age?: number | null, level_sort_order?: number | null, level?: string | null }> | null, hours?: Array<{ __typename?: 'CourtMatchesTime', time?: number | null, time12?: string | null, default?: string | null }> | null, courts?: Array<{ __typename?: 'CourtMatchesCourt', court_id?: string | null, court_name?: string | null, event_id?: number | null, short_name?: string | null, matches?: Array<{ __typename?: 'CourtMatchesCourtMatch', match_id?: string | null, match_name?: string | null, division_id?: number | null, division_name?: string | null, division_short_name?: string | null, date_start?: number | null, date_end?: number | null, secs_finished?: number | null, team1_roster_id?: number | null, team2_roster_id?: number | null, team_1_name?: string | null, team_2_name?: string | null, team_ref_name?: string | null, color?: string | null, results?: { __typename?: 'CourtMatchesCourtMatchResults', winner?: string | null, team1?: { __typename?: 'CourtMatchesCourtMatchResultsTeam', scores?: string | null } | null, team2?: { __typename?: 'CourtMatchesCourtMatchResultsTeam', scores?: string | null } | null } | null }> | null }> | null } };

export type DivisionOverviewQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  divisionId: Scalars['ID']['input'];
}>;


export type DivisionOverviewQuery = { __typename?: 'Query', divisionPoolBrackets: Array<{ __typename?: 'Bracket', is_pool: number, uuid: string, round_id: string, sort_priority: number, short_name?: string | null, display_name?: string | null, date_start?: number | null, division_id: string, settings?: { __typename?: 'PoolOrBracketSettings', SetCount?: number | null, PlayAllSets?: boolean | null, WinningPoints?: number | null } | null, external: { __typename?: 'PoolOrBracketExternal', courts_short_info: Array<{ __typename?: 'CourtShortInfo', uuid: string, short_name?: string | null }> }, teams: Array<{ __typename?: 'Team', team_id: string, team_name: string, master_team_id?: string | null, extra?: { __typename?: 'TeamExtra', show_accepted_bid?: boolean | null, show_previously_accepted_bid?: string | null } | null, division_standing?: { __typename?: 'DivisionStanding', seed?: number | null, heading: string, heading_priority: number } | null, pool_bracket_stat?: { __typename?: 'PoolOrBracketStatItem', sets_pct?: number | null, sets_won?: number | null, sets_lost?: number | null, matches_won?: number | null, matches_lost?: number | null, points_ratio?: number | null } | null }>, matches: Array<{ __typename?: 'Match', team_id?: string | null, opponent_id?: string | null, ref_team_id?: string | null, match_id: string, match_name?: string | null, match_number?: number | null, secs_start?: number | null, secs_end?: number | null, secs_finished?: number | null, source?: { __typename?: 'MatchSource', team?: { __typename?: 'MatchSourceItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, opponent?: { __typename?: 'MatchSourceItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, ref?: { __typename?: 'MatchSourceItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null } | null, results?: { __typename?: 'MatchResults', sets: Array<string>, winner_team?: { __typename?: 'MatchTeamResults', scores?: string | null, roster_team_id?: string | null } | null } | null, finishes?: { __typename?: 'MatchFinishes', winner?: { __typename?: 'TeamAdvancement', team_name?: string | null, team_id?: number | null, next_match?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null, next_ref?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null } | null, looser?: { __typename?: 'TeamAdvancement', team_name?: string | null, team_id?: number | null, next_match?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null, next_ref?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null } | null } | null, external: { __typename?: 'MatchExternal', team_display_name: string, opponent_display_name: string, ref_team_display_name: string, team_pb_seed?: { __typename?: 'PoolOrBracketSeedItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, opponent_pb_seed?: { __typename?: 'PoolOrBracketSeedItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, court_info: { __typename?: 'CourtShortInfo', uuid: string, short_name?: string | null } } }>, pb_finishes: Array<{ __typename?: 'TeamAdvancement', team_name?: string | null, team_id?: number | null, next_match?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null, next_ref?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', uuid: string, is_pool: number } | null } | null } | null }> } | { __typename?: 'Pool', is_pool: number, uuid: string, round_id: string, sort_priority: number, short_name?: string | null, display_name?: string | null, date_start?: number | null, division_id: string, settings?: { __typename?: 'PoolOrBracketSettings', SetCount?: number | null, PlayAllSets?: boolean | null, WinningPoints?: number | null } | null, external: { __typename?: 'PoolOrBracketExternal', courts_short_info: Array<{ __typename?: 'CourtShortInfo', uuid: string, short_name?: string | null }> }, teams: Array<{ __typename?: 'Team', team_id: string, team_name: string, master_team_id?: string | null, extra?: { __typename?: 'TeamExtra', show_accepted_bid?: boolean | null, show_previously_accepted_bid?: string | null } | null, division_standing?: { __typename?: 'DivisionStanding', seed?: number | null, heading: string, heading_priority: number } | null, pool_bracket_stat?: { __typename?: 'PoolOrBracketStatItem', sets_pct?: number | null, sets_won?: number | null, sets_lost?: number | null, matches_won?: number | null, matches_lost?: number | null, points_ratio?: number | null } | null }>, matches: Array<{ __typename?: 'Match', team_id?: string | null, opponent_id?: string | null, ref_team_id?: string | null, match_id: string, match_name?: string | null, match_number?: number | null, secs_start?: number | null, secs_end?: number | null, secs_finished?: number | null, source?: { __typename?: 'MatchSource', team?: { __typename?: 'MatchSourceItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, opponent?: { __typename?: 'MatchSourceItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, ref?: { __typename?: 'MatchSourceItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null } | null, results?: { __typename?: 'MatchResults', sets: Array<string>, winner_team?: { __typename?: 'MatchTeamResults', scores?: string | null, roster_team_id?: string | null } | null } | null, finishes?: { __typename?: 'MatchFinishes', winner?: { __typename?: 'TeamAdvancement', team_name?: string | null, team_id?: number | null, next_match?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null, next_ref?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null } | null, looser?: { __typename?: 'TeamAdvancement', team_name?: string | null, team_id?: number | null, next_match?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null, next_ref?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null } | null } | null, external: { __typename?: 'MatchExternal', team_display_name: string, opponent_display_name: string, ref_team_display_name: string, team_pb_seed?: { __typename?: 'PoolOrBracketSeedItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, opponent_pb_seed?: { __typename?: 'PoolOrBracketSeedItem', id?: string | null, name?: string | null, type?: number | null, seed?: number | null, overallSeed?: number | null, reseedRank?: number | null } | null, court_info: { __typename?: 'CourtShortInfo', uuid: string, short_name?: string | null } } }>, pb_finishes: Array<{ __typename?: 'TeamAdvancement', team_name?: string | null, team_id?: number | null, next_match?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null, next_ref?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', uuid: string, is_pool: number } | null } | null } | null }> }> };

export type EventForDivisionPageQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type EventForDivisionPageQuery = { __typename?: 'Query', event?: { __typename?: 'Event', event_id?: string | null, hide_seeds?: boolean | null, name?: string | null, long_name?: string | null, days?: Array<string> | null, city?: string | null, state?: string | null, event_notes?: string | null, address?: string | null, is_with_prev_qual?: boolean | null, tickets_published?: boolean | null, is_require_recipient_name_for_each_ticket?: boolean | null, tickets_code?: string | null, locations?: Array<{ __typename?: 'EventLocation', location_name?: string | null }> | null, divisions: Array<{ __typename?: 'EventDivision', short_name?: string | null, division_id?: number | null }>, clubs: Array<{ __typename?: 'Club', roster_club_id: string }>, athletes: Array<{ __typename?: 'EventAthlete', first?: string | null, last?: string | null }>, teams: Array<{ __typename?: 'TeamList', roster_team_id?: number | null }>, teams_settings?: { __typename?: 'EventTeamsSettings', sort_by?: string | null, hide_standings?: boolean | null } | null } | null };

export type EventQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type EventQuery = { __typename?: 'Query', event?: { __typename?: 'Event', event_id?: string | null, hide_seeds?: boolean | null, name?: string | null, long_name?: string | null, days?: Array<string> | null, city?: string | null, state?: string | null, event_notes?: string | null, address?: string | null, is_with_prev_qual?: boolean | null, tickets_published?: boolean | null, is_require_recipient_name_for_each_ticket?: boolean | null, tickets_code?: string | null, locations?: Array<{ __typename?: 'EventLocation', location_name?: string | null }> | null, divisions: Array<{ __typename?: 'EventDivision', gender?: string | null, name?: string | null, event_id?: string | null, short_name?: string | null, teams_count?: number | null, division_id?: number | null }>, clubs: Array<{ __typename?: 'Club', club_name: string, teams_count: number, roster_club_id: string, state?: string | null, club_code: string }>, athletes: Array<{ __typename?: 'EventAthlete', age?: number | null, gender?: string | null, first?: string | null, last?: string | null, team_organization_code?: string | null, team_name?: string | null, jersey?: number | null, club_name: string, division_id?: number | null, roster_team_id?: number | null }>, teams: Array<{ __typename?: 'TeamList', division_name?: string | null, division_short_name?: string | null, club_name?: string | null, team_name?: string | null, organization_code?: string | null, roster_team_id?: number | null, roster_club_id?: number | null, division_id?: number | null, gender?: string | null }>, teams_settings?: { __typename?: 'EventTeamsSettings', sort_by?: string | null } | null } | null };

export type EventForDivisionsPageQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type EventForDivisionsPageQuery = { __typename?: 'Query', event?: { __typename?: 'Event', event_id?: string | null, hide_seeds?: boolean | null, name?: string | null, long_name?: string | null, days?: Array<string> | null, city?: string | null, state?: string | null, event_notes?: string | null, address?: string | null, is_with_prev_qual?: boolean | null, tickets_published?: boolean | null, is_require_recipient_name_for_each_ticket?: boolean | null, tickets_code?: string | null, locations?: Array<{ __typename?: 'EventLocation', location_name?: string | null }> | null, divisions: Array<{ __typename?: 'EventDivision', gender?: string | null, name?: string | null, event_id?: string | null, short_name?: string | null, teams_count?: number | null, division_id?: number | null }>, clubs: Array<{ __typename?: 'Club', roster_club_id: string }>, athletes: Array<{ __typename?: 'EventAthlete', first?: string | null, last?: string | null }>, teams: Array<{ __typename?: 'TeamList', roster_team_id?: number | null }>, teams_settings?: { __typename?: 'EventTeamsSettings', sort_by?: string | null } | null } | null };

export type DaysOfEventQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type DaysOfEventQuery = { __typename?: 'Query', event?: { __typename?: 'Event', days?: Array<string> | null } | null };

export type EventCountsQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
}>;


export type EventCountsQuery = { __typename?: 'Query', paginatedAthletes: { __typename?: 'PaginatedAthletes', page_info: { __typename?: 'PageInfo', item_count: number } }, paginatedStaff: { __typename?: 'PaginatedStaff', page_info: { __typename?: 'PageInfo', item_count: number } }, paginatedClubs: { __typename?: 'PaginatedClubs', page_info: { __typename?: 'PageInfo', item_count: number } } };

export type EventDetailsQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
}>;


export type EventDetailsQuery = { __typename?: 'Query', event?: { __typename?: 'Event', id?: number | null, long_name?: string | null, city?: string | null, state?: string | null, tickets_published?: boolean | null, schedule_published?: boolean | null, is_require_recipient_name_for_each_ticket?: boolean | null, tickets_code?: string | null, allow_point_of_sales?: boolean | null, sales_hub_point_of_sale_id?: string | null, hide_seeds?: boolean | null, days?: Array<string> | null, has_rosters?: boolean | null, is_with_prev_qual?: boolean | null, event_notes?: string | null, address?: string | null, sport_sanctioning?: string | null, locations?: Array<{ __typename?: 'EventLocation', location_name?: string | null }> | null, teams_settings?: { __typename?: 'EventTeamsSettings', baller_tv_available?: boolean | null, hide_standings?: boolean | null, sort_by?: string | null, manual_club_names?: boolean | null } | null } | null, divisions: Array<{ __typename?: 'Division', division_id: string, name: string, teams_count: number, short_name?: string | null, has_flow_chart?: boolean | null, media: Array<{ __typename?: 'EventMedia', media_id: string, division_id: string, file_type: string, path: string }>, matches_time_ranges: Array<{ __typename?: 'MatchesTimeRange', day: string, start_time: string, end_time: string }>, rounds: Array<{ __typename?: 'Round', uuid: string, division_id: string, sort_priority: number, name?: string | null, short_name?: string | null, first_match_start?: number | null, last_match_start?: number | null }> }> };

export type EventWithRosterQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type EventWithRosterQuery = { __typename?: 'Query', roster: Array<{ __typename?: 'EventTeamRoster', event_id: string, event_name?: string | null, state?: string | null, club_name?: string | null, organization_code?: string | null, team_name?: string | null, roster_team_id?: string | null, roster_athletes?: Array<{ __typename?: 'EventTeamRosterAthlete', id: string, first?: string | null, last?: string | null, short_position?: string | null, uniform?: number | null, gradyear?: number | null }> | null, roster_staff?: Array<{ __typename?: 'EventTeamRosterStaff', id: string, first?: string | null, last?: string | null, role_name?: string | null, sort_order?: number | null }> | null }>, event?: { __typename?: 'Event', event_id?: string | null, hide_seeds?: boolean | null, name?: string | null, long_name?: string | null, days?: Array<string> | null, city?: string | null, state?: string | null, event_notes?: string | null, address?: string | null, is_with_prev_qual?: boolean | null, tickets_published?: boolean | null, tickets_code?: string | null, is_require_recipient_name_for_each_ticket?: boolean | null, locations?: Array<{ __typename?: 'EventLocation', location_name?: string | null }> | null, divisions: Array<{ __typename?: 'EventDivision', gender?: string | null, name?: string | null, event_id?: string | null, short_name?: string | null, teams_count?: number | null, division_id?: number | null }>, clubs: Array<{ __typename?: 'Club', club_name: string, teams_count: number, roster_club_id: string, state?: string | null }>, athletes: Array<{ __typename?: 'EventAthlete', age?: number | null, gender?: string | null, first?: string | null, last?: string | null, team_organization_code?: string | null, team_name?: string | null, jersey?: number | null, club_name: string, division_id?: number | null, roster_team_id?: number | null }>, teams: Array<{ __typename?: 'TeamList', division_name?: string | null, division_short_name?: string | null, club_name?: string | null, team_name?: string | null, organization_code?: string | null, roster_team_id?: number | null, roster_club_id?: number | null, division_id?: number | null, gender?: string | null }>, teams_settings?: { __typename?: 'EventTeamsSettings', sort_by?: string | null, hide_standings?: boolean | null } | null } | null };

export type EventWithRosterForAboutPageQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type EventWithRosterForAboutPageQuery = { __typename?: 'Query', roster: Array<{ __typename?: 'EventTeamRoster', roster_athletes?: Array<{ __typename?: 'EventTeamRosterAthlete', id: string }> | null, roster_staff?: Array<{ __typename?: 'EventTeamRosterStaff', id: string }> | null }>, event?: { __typename?: 'Event', event_id?: string | null, has_rosters?: boolean | null, hide_seeds?: boolean | null, name?: string | null, long_name?: string | null, days?: Array<string> | null, city?: string | null, state?: string | null, event_notes?: string | null, address?: string | null, is_with_prev_qual?: boolean | null, tickets_published?: boolean | null, tickets_code?: string | null, is_require_recipient_name_for_each_ticket?: boolean | null, locations?: Array<{ __typename?: 'EventLocation', location_name?: string | null }> | null, divisions: Array<{ __typename?: 'EventDivision', division_id?: number | null }>, clubs: Array<{ __typename?: 'Club', roster_club_id: string }>, athletes: Array<{ __typename?: 'EventAthlete', first?: string | null, last?: string | null }>, teams: Array<{ __typename?: 'TeamList', roster_team_id?: number | null }>, teams_settings?: { __typename?: 'EventTeamsSettings', sort_by?: string | null, hide_standings?: boolean | null } | null } | null };

export type EventWithRosterForRosterPageQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type EventWithRosterForRosterPageQuery = { __typename?: 'Query', roster: Array<{ __typename?: 'EventTeamRoster', event_id: string, event_name?: string | null, state?: string | null, club_name?: string | null, organization_code?: string | null, team_name?: string | null, roster_team_id?: string | null, roster_athletes?: Array<{ __typename?: 'EventTeamRosterAthlete', id: string, first?: string | null, last?: string | null, short_position?: string | null, uniform?: number | null, gradyear?: number | null }> | null, roster_staff?: Array<{ __typename?: 'EventTeamRosterStaff', id: string, first?: string | null, last?: string | null, role_name?: string | null, sort_order?: number | null }> | null }>, event?: { __typename?: 'Event', event_id?: string | null, hide_seeds?: boolean | null, name?: string | null, long_name?: string | null, days?: Array<string> | null, city?: string | null, state?: string | null, event_notes?: string | null, address?: string | null, is_with_prev_qual?: boolean | null, tickets_published?: boolean | null, tickets_code?: string | null, is_require_recipient_name_for_each_ticket?: boolean | null, locations?: Array<{ __typename?: 'EventLocation', location_name?: string | null }> | null, divisions: Array<{ __typename?: 'EventDivision', division_id?: number | null }>, clubs: Array<{ __typename?: 'Club', roster_club_id: string }>, athletes: Array<{ __typename?: 'EventAthlete', first?: string | null, last?: string | null }>, teams: Array<{ __typename?: 'TeamList', roster_team_id?: number | null }>, teams_settings?: { __typename?: 'EventTeamsSettings', sort_by?: string | null, hide_standings?: boolean | null } | null } | null };

export type PaginatedEventsQueryVariables = Exact<{
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
  startBefore?: InputMaybe<Scalars['String']['input']>;
  startAfter?: InputMaybe<Scalars['String']['input']>;
  endBefore?: InputMaybe<Scalars['String']['input']>;
  endAfter?: InputMaybe<Scalars['String']['input']>;
  years?: InputMaybe<Array<Scalars['String']['input']> | Scalars['String']['input']>;
  asc?: InputMaybe<Scalars['Boolean']['input']>;
}>;


export type PaginatedEventsQuery = { __typename?: 'Query', paginatedEvents: { __typename?: 'PaginatedEvents', items: Array<{ __typename?: 'Event', event_id?: string | null, long_name?: string | null, city?: string | null, date_start?: string | null, state?: string | null, address?: string | null, schedule_published?: boolean | null, small_logo?: string | null }>, page_info: { __typename?: 'PageInfo', page: number, page_size: number, page_count: number, item_count: number } } };

export type FeatureMatchesQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type FeatureMatchesQuery = { __typename?: 'Query', featureMatches: Array<{ __typename?: 'FeatureMatches', match_id?: string | null, division_id?: string | null, team1_id?: string | null, team1_name?: string | null, team2_id?: string | null, team2_name?: string | null, court_name?: string | null, court_short_name?: string | null, match_start?: string | null }> };

export type PoolQueryVariables = Exact<{
  id: Scalars['ID']['input'];
  poolId: Scalars['ID']['input'];
}>;


export type PoolQuery = { __typename?: 'Query', pool: { __typename?: 'EventPool', division_short_name?: string | null, uuid?: string | null, display_name?: string | null, display_name_short?: string | null, team_count?: number | null, upcoming_matches?: Array<{ __typename?: 'PoolUpcomingMatches', court_name?: string | null, date_start?: number | null, display_name?: string | null, division_short_name?: string | null, footnote_play?: string | null, footnote_team1?: string | null, footnote_team2?: string | null, match_id?: string | null, ref_roster_id?: string | null, ref_team_code?: string | null, ref_team_name?: string | null, results?: string | null, team1_code?: string | null, team1_name?: string | null, team1_roster_id?: string | null, team2_code?: string | null, team2_name?: string | null, team2_roster_id?: string | null, team1_master_id?: string | null, team2_master_id?: string | null }> | null, results?: Array<{ __typename?: 'PoolResults', match_id?: string | null, team1_name?: string | null, team2_name?: string | null, team1_master_id?: number | null, team2_master_id?: number | null, ref_team_name?: string | null, date_start?: string | null, court_name?: string | null, results: { __typename?: 'PoolResultsResults', set1?: string | null, set2?: string | null, set3?: string | null, set4?: string | null, set5?: string | null, team1?: { __typename?: 'PoolResultsResultsTeam', scores?: string | null, matches_won?: number | null } | null, team2?: { __typename?: 'PoolResultsResultsTeam', scores?: string | null, matches_won?: number | null } | null } }> | null, standings?: Array<{ __typename?: 'PoolStandings', display_name?: string | null, pb_stats: Array<{ __typename?: 'PoolStandingsStats', team_id?: string | null, name?: string | null, rank?: string | null, matches_won?: number | null, matches_lost?: number | null, sets_won?: number | null, sets_lost?: number | null, sets_pct?: number | null, points_ratio?: number | null }> }> | null, pb_finishes?: { __typename?: 'PoolFuture', team_ids: Array<string>, teams: Array<{ __typename?: 'TeamAdvancement', team_name?: string | null, team_id?: number | null, next_match?: { __typename?: 'MatchReference', display_name?: string | null, week_day?: string | null, start_time_string?: string | null, court?: string | null, secs_start?: number | null, match_id?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null, next_ref?: { __typename?: 'MatchReference', display_name?: string | null, week_day?: string | null, start_time_string?: string | null, court?: string | null, secs_start?: number | null, match_id?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null }> } | null, next?: { __typename?: 'NextPrevPool', is_pool?: number | null, name?: string | null, uuid?: string | null } | null, prev?: { __typename?: 'NextPrevPool', is_pool?: number | null, name?: string | null, uuid?: string | null } | null } };

export type PoolByTeamIdQueryVariables = Exact<{
  id: Scalars['ID']['input'];
  teamId: Scalars['ID']['input'];
  poolId?: InputMaybe<Scalars['ID']['input']>;
}>;


export type PoolByTeamIdQuery = { __typename?: 'Query', teamSingle: { __typename?: 'TeamSingle', roster_team_id?: number | null, master_team_id?: number | null, division_id?: number | null, team_name?: string | null, matches_won?: number | null, matches_lost?: number | null, manual_club_name?: string | null, sets_won?: number | null, sets_lost?: number | null, organization_code?: string | null, points_won?: number | null, points_lost?: number | null, roster_club_id?: number | null, club_name?: string | null, state?: string | null, bracket_finishes?: { __typename?: 'MatchFinishes', winner?: { __typename?: 'TeamAdvancement', team_name?: string | null, team_id?: number | null, next_match?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null, next_ref?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null } | null, looser?: { __typename?: 'TeamAdvancement', team_name?: string | null, team_id?: number | null, next_match?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null, next_ref?: { __typename?: 'MatchReference', court?: string | null, match_id?: string | null, week_day?: string | null, secs_start?: number | null, display_name?: string | null, start_time_string?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null } | null } | null, pb_info?: Array<{ __typename?: 'TeamSinglePbInfo', uuid?: string | null, name?: string | null, is_pool?: number | null, pb_finishes?: { __typename?: 'PoolFuture', team_ids: Array<string>, teams: Array<{ __typename?: 'TeamAdvancement', team_name?: string | null, team_id?: number | null, next_match?: { __typename?: 'MatchReference', display_name?: string | null, week_day?: string | null, start_time_string?: string | null, court?: string | null, secs_start?: number | null, match_id?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null, next_ref?: { __typename?: 'MatchReference', display_name?: string | null, week_day?: string | null, start_time_string?: string | null, court?: string | null, secs_start?: number | null, match_id?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null }> } | null }> | null, upcoming?: Array<{ __typename?: 'EventUpcoming', match_type?: string | null, match_id?: string | null, unix_finished?: string | null, results?: string | null, display_name?: string | null, date_start_formatted?: string | null, date_start?: string | null, division_id?: string | null, division_short_name?: string | null, court_name?: string | null, pool_name?: string | null, pool_bracket_id?: string | null, is_pool?: number | null, opponent_team_name?: string | null, opponent_organization_code?: string | null, pb_name?: string | null, round_name?: string | null, footnote_play?: string | null, footnote_team1?: string | null, footnote_team2?: string | null, settings?: { __typename?: 'PoolOrBracketSettings', SetCount?: number | null, PlayAllSets?: boolean | null, WinningPoints?: number | null } | null }> | null, athletes?: Array<{ __typename?: 'TeamSingleAthlete', first?: string | null, last?: string | null, uniform?: number | null, short_position?: string | null, gradyear?: number | null, height?: string | null }> | null, staff?: Array<{ __typename?: 'TeamSingleStaff', first?: string | null, last?: string | null, sort_order?: number | null, role_name?: string | null }> | null, results?: Array<{ __typename?: 'TeamSingleResults', uuid?: string | null, is_pool?: number | null, pb_name?: string | null, round_name?: string | null, sort_priority?: number | null, pb_stats?: Array<{ __typename?: 'PoolStandingsStats', name?: string | null, rank?: string | null, team_id?: string | null, sets_pct?: number | null, sets_won?: number | null, sets_lost?: number | null, points_won?: number | null, matches_pct?: number | null, matches_won?: number | null, points_lost?: number | null, matches_lost?: number | null, points_ratio?: number | null }> | null, matches: Array<{ __typename?: 'TeamSingleMatch', date_start?: number | null, date_start_formatted?: string | null, display_name?: string | null, division_id?: number | null, division_short_name?: string | null, match_id?: string | null, match_type?: string | null, opponent_organization_code?: string | null, opponent_team_id?: number | null, opponent_team_name?: string | null, pool_bracket_id?: string | null, unix_finished?: number | null, results?: { __typename?: 'TeamSingleMatchResults', set1?: string | null, set2?: string | null, set3?: string | null, set4?: string | null, set5?: string | null, winner?: string | null, team1: { __typename?: 'TeamSingleMatchResultsTeam', heading?: string | null, heading_sort?: number | null, matches_lost?: number | null, matches_pct?: number | null, matches_won?: number | null, overallSeed?: number | null, points_lost?: number | null, points_ratio?: number | null, points_won?: number | null, roster_team_id?: number | null, scores?: string | null, sets_lost?: number | null, sets_pct?: number | null, sets_won?: number | null, title?: string | null }, team2: { __typename?: 'TeamSingleMatchResultsTeam', heading?: string | null, heading_sort?: number | null, matches_lost?: number | null, matches_pct?: number | null, matches_won?: number | null, overallSeed?: number | null, points_lost?: number | null, points_ratio?: number | null, points_won?: number | null, roster_team_id?: number | null, scores?: string | null, sets_lost?: number | null, sets_pct?: number | null, sets_won?: number | null, title?: string | null } } | null }> }> | null }, poolIdByTeamId?: { __typename?: 'EventPool', division_short_name?: string | null, uuid?: string | null, display_name?: string | null, display_name_short?: string | null, team_count?: number | null, division_id?: string | null, upcoming_matches?: Array<{ __typename?: 'PoolUpcomingMatches', court_name?: string | null, date_start?: number | null, display_name?: string | null, division_short_name?: string | null, footnote_play?: string | null, footnote_team1?: string | null, footnote_team2?: string | null, match_id?: string | null, ref_roster_id?: string | null, ref_team_code?: string | null, ref_team_name?: string | null, results?: string | null, team1_code?: string | null, team1_name?: string | null, team1_roster_id?: string | null, team2_code?: string | null, team2_name?: string | null, team2_roster_id?: string | null }> | null, settings?: { __typename?: 'PoolOrBracketSettings', PlayAllSets?: boolean | null, SetCount?: number | null, WinningPoints?: number | null } | null, results?: Array<{ __typename?: 'PoolResults', match_id?: string | null, team1_name?: string | null, team2_name?: string | null, ref_team_name?: string | null, date_start?: string | null, court_name?: string | null, results: { __typename?: 'PoolResultsResults', set1?: string | null, set2?: string | null, set3?: string | null, set4?: string | null, set5?: string | null, team1?: { __typename?: 'PoolResultsResultsTeam', scores?: string | null, matches_won?: number | null } | null, team2?: { __typename?: 'PoolResultsResultsTeam', scores?: string | null, matches_won?: number | null } | null } }> | null, standings?: Array<{ __typename?: 'PoolStandings', display_name?: string | null, pb_stats: Array<{ __typename?: 'PoolStandingsStats', team_id?: string | null, name?: string | null, rank?: string | null, matches_won?: number | null, matches_lost?: number | null, sets_won?: number | null, sets_lost?: number | null, sets_pct?: number | null, points_ratio?: number | null }> }> | null, pb_finishes?: { __typename?: 'PoolFuture', team_ids: Array<string>, teams: Array<{ __typename?: 'TeamAdvancement', team_name?: string | null, team_id?: number | null, next_match?: { __typename?: 'MatchReference', display_name?: string | null, week_day?: string | null, start_time_string?: string | null, court?: string | null, secs_start?: number | null, match_id?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null, next_ref?: { __typename?: 'MatchReference', display_name?: string | null, week_day?: string | null, start_time_string?: string | null, court?: string | null, secs_start?: number | null, match_id?: string | null, external?: { __typename?: 'MatchReferenceExternal', pool_bracket_info?: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } | null } | null } | null }> } | null, next?: { __typename?: 'NextPrevPool', is_pool?: number | null, name?: string | null, uuid?: string | null } | null, prev?: { __typename?: 'NextPrevPool', is_pool?: number | null, name?: string | null, uuid?: string | null } | null } | null };

export type PoolsQueryVariables = Exact<{
  id: Scalars['ID']['input'];
  divisionId: Scalars['ID']['input'];
}>;


export type PoolsQuery = { __typename?: 'Query', pools: Array<{ __typename?: 'DivisionPool', uuid?: string | null, r_uuid?: string | null, r_name?: string | null, pb_name?: string | null, display_name?: string | null, division_short_name?: string | null, date_start?: number | null, court_start?: string | null, is_pool?: number | null, settings?: { __typename?: 'PoolOrBracketSettings', PlayAllSets?: boolean | null, SetCount?: number | null, WinningPoints?: number | null } | null, teams?: Array<{ __typename?: 'DivisionPoolTeam', sets_pct?: number | null, opponent_team_name?: string | null, rank?: number | null, opponent_team_id?: number | null, matches_won?: string | null, matches_lost?: string | null, sets_won?: string | null, sets_lost?: string | null, opponent_organization_code?: string | null, points_ratio?: number | null, info: { __typename?: 'DivisionPoolTeamInfo', seed_current?: number | null } }> | null }> };

export type CourtsRangeMatchesQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  after: Scalars['String']['input'];
  before: Scalars['String']['input'];
  divisionId?: InputMaybe<Scalars['ID']['input']>;
  uniqKey: Scalars['String']['input'];
}>;


export type CourtsRangeMatchesQuery = { __typename?: 'Query', dayRangeMatches: { __typename?: 'FilteredMatches', items: Array<{ __typename?: 'Match', team_id?: string | null, opponent_id?: string | null, ref_team_id?: string | null, match_id: string, court_id: string, is_tb?: number | null, match_name?: string | null, division_id: string, division_name?: string | null, secs_start?: number | null, secs_end?: number | null, secs_finished?: number | null, results?: { __typename?: 'MatchResults', winner_team?: { __typename?: 'MatchTeamResults', scores?: string | null, roster_team_id?: string | null } | null } | null, external: { __typename?: 'MatchExternal', team_display_name: string, opponent_display_name: string, ref_team_display_name: string, pool_bracket_info: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string }, team_pb_seed?: { __typename?: 'PoolOrBracketSeedItem', id?: string | null } | null, opponent_pb_seed?: { __typename?: 'PoolOrBracketSeedItem', id?: string | null } | null } }>, filter_info: { __typename?: 'FilterInfo', is_filtered: boolean } }, relatedCourts: Array<{ __typename?: 'Court', uuid: string, name: string, short_name?: string | null }> };

export type RosterQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type RosterQuery = { __typename?: 'Query', roster: Array<{ __typename?: 'EventTeamRoster', event_id: string, event_name?: string | null, state?: string | null, club_name?: string | null, organization_code?: string | null, team_name?: string | null, roster_team_id?: string | null, roster_athletes?: Array<{ __typename?: 'EventTeamRosterAthlete', id: string, first?: string | null, last?: string | null, short_position?: string | null, uniform?: number | null, gradyear?: number | null }> | null, roster_staff?: Array<{ __typename?: 'EventTeamRosterStaff', id: string, first?: string | null, last?: string | null, role_name?: string | null, sort_order?: number | null }> | null }> };

export type DivisionRoundsPoolBracketsQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  divisionId: Scalars['ID']['input'];
}>;


export type DivisionRoundsPoolBracketsQuery = { __typename?: 'Query', divisionRounds: Array<{ __typename?: 'Round', uuid: string, short_name?: string | null, name?: string | null, sort_priority: number, first_match_start?: number | null, last_match_start?: number | null, pool_brackets: Array<{ __typename?: 'Bracket', uuid: string, short_name?: string | null, display_name?: string | null, is_pool: number, sort_priority: number, date_start?: number | null, settings?: { __typename?: 'PoolOrBracketSettings', SetCount?: number | null, PlayAllSets?: boolean | null, WinningPoints?: number | null } | null, teams: Array<{ __typename?: 'Team', team_id: string, team_name: string, extra?: { __typename?: 'TeamExtra', show_previously_accepted_bid?: string | null, show_accepted_bid?: boolean | null } | null, division_standing?: { __typename?: 'DivisionStanding', seed?: number | null } | null, pool_bracket_stat?: { __typename?: 'PoolOrBracketStatItem', sets_pct?: number | null, sets_won?: number | null, sets_lost?: number | null, matches_won?: number | null, matches_lost?: number | null, points_ratio?: number | null } | null }>, external: { __typename?: 'PoolOrBracketExternal', courts_short_info: Array<{ __typename?: 'CourtShortInfo', uuid: string, short_name?: string | null }> } } | { __typename?: 'Pool', uuid: string, short_name?: string | null, display_name?: string | null, is_pool: number, sort_priority: number, date_start?: number | null, settings?: { __typename?: 'PoolOrBracketSettings', SetCount?: number | null, PlayAllSets?: boolean | null, WinningPoints?: number | null } | null, teams: Array<{ __typename?: 'Team', team_id: string, team_name: string, extra?: { __typename?: 'TeamExtra', show_previously_accepted_bid?: string | null, show_accepted_bid?: boolean | null } | null, division_standing?: { __typename?: 'DivisionStanding', seed?: number | null } | null, pool_bracket_stat?: { __typename?: 'PoolOrBracketStatItem', sets_pct?: number | null, sets_won?: number | null, sets_lost?: number | null, matches_won?: number | null, matches_lost?: number | null, points_ratio?: number | null } | null }>, external: { __typename?: 'PoolOrBracketExternal', courts_short_info: Array<{ __typename?: 'CourtShortInfo', uuid: string, short_name?: string | null }> } }> }> };

export type PaginatedStaffQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
}>;


export type PaginatedStaffQuery = { __typename?: 'Query', paginatedStaff: { __typename?: 'PaginatedStaff', items: Array<{ __typename?: 'Staff', staff_id: string, first: string, last: string, club_name: string, team_id: string, team_name: string, role_name?: string | null, organization_code?: string | null, state?: string | null }>, page_info: { __typename?: 'PageInfo', page: number, page_size: number, page_count: number, item_count: number } } };

export type TeamSingleQueryVariables = Exact<{
  id: Scalars['ID']['input'];
  teamId: Scalars['ID']['input'];
}>;


export type TeamSingleQuery = { __typename?: 'Query', teamSingle: { __typename?: 'TeamSingle', roster_team_id?: number | null, master_team_id?: number | null, division_id?: number | null, team_name?: string | null, matches_won?: number | null, matches_lost?: number | null, manual_club_name?: string | null, sets_won?: number | null, sets_lost?: number | null, organization_code?: string | null, points_won?: number | null, points_lost?: number | null, roster_club_id?: number | null, club_name?: string | null, state?: string | null, upcoming?: Array<{ __typename?: 'EventUpcoming', match_type?: string | null, match_id?: string | null, unix_finished?: string | null, results?: string | null, display_name?: string | null, date_start_formatted?: string | null, date_start?: string | null, division_id?: string | null, division_short_name?: string | null, court_name?: string | null, pool_name?: string | null, pool_bracket_id?: string | null, is_pool?: number | null, opponent_team_name?: string | null, opponent_organization_code?: string | null, pb_name?: string | null, round_name?: string | null, footnote_play?: string | null, footnote_team1?: string | null, footnote_team2?: string | null, settings?: { __typename?: 'PoolOrBracketSettings', SetCount?: number | null, PlayAllSets?: boolean | null, WinningPoints?: number | null } | null }> | null, athletes?: Array<{ __typename?: 'TeamSingleAthlete', first?: string | null, last?: string | null, uniform?: number | null, short_position?: string | null, gradyear?: number | null, height?: string | null }> | null, staff?: Array<{ __typename?: 'TeamSingleStaff', first?: string | null, last?: string | null, sort_order?: number | null, role_name?: string | null }> | null, results?: Array<{ __typename?: 'TeamSingleResults', uuid?: string | null, is_pool?: number | null, pb_name?: string | null, round_name?: string | null, sort_priority?: number | null, pb_stats?: Array<{ __typename?: 'PoolStandingsStats', name?: string | null, rank?: string | null, team_id?: string | null, sets_pct?: number | null, sets_won?: number | null, sets_lost?: number | null, points_won?: number | null, matches_pct?: number | null, matches_won?: number | null, points_lost?: number | null, matches_lost?: number | null, points_ratio?: number | null }> | null, matches: Array<{ __typename?: 'TeamSingleMatch', date_start?: number | null, date_start_formatted?: string | null, display_name?: string | null, division_id?: number | null, division_short_name?: string | null, match_id?: string | null, match_type?: string | null, opponent_organization_code?: string | null, opponent_team_id?: number | null, opponent_team_name?: string | null, pool_bracket_id?: string | null, unix_finished?: number | null, results?: { __typename?: 'TeamSingleMatchResults', set1?: string | null, set2?: string | null, set3?: string | null, set4?: string | null, set5?: string | null, winner?: string | null, team1: { __typename?: 'TeamSingleMatchResultsTeam', heading?: string | null, heading_sort?: number | null, matches_lost?: number | null, matches_pct?: number | null, matches_won?: number | null, overallSeed?: number | null, points_lost?: number | null, points_ratio?: number | null, points_won?: number | null, roster_team_id?: number | null, scores?: string | null, sets_lost?: number | null, sets_pct?: number | null, sets_won?: number | null, title?: string | null }, team2: { __typename?: 'TeamSingleMatchResultsTeam', heading?: string | null, heading_sort?: number | null, matches_lost?: number | null, matches_pct?: number | null, matches_won?: number | null, overallSeed?: number | null, points_lost?: number | null, points_ratio?: number | null, points_won?: number | null, roster_team_id?: number | null, scores?: string | null, sets_lost?: number | null, sets_pct?: number | null, sets_won?: number | null, title?: string | null } } | null }> }> | null } };

export type DivisionDetailsQueryVariables = Exact<{
  id: Scalars['ID']['input'];
  divisionIds: Array<Scalars['ID']['input']> | Scalars['ID']['input'];
}>;


export type DivisionDetailsQuery = { __typename?: 'Query', divisionDetails?: { __typename?: 'EventDivisionDetails', standing: Array<{ __typename?: 'Standing', heading_group: string, teams: Array<{ __typename?: 'TeamDetails', rank?: number | null, roster_team_id?: number | null, division_short_name?: string | null, division_id?: number | null, seed_current?: number | null, team_name?: string | null, organization_code?: string | null, points_ratio?: number | null, matches_won?: number | null, matches_lost?: number | null, sets_won?: number | null, sets_lost?: number | null, sets_pct?: number | null, info?: { __typename?: 'StandingInfo', points?: number | null } | null }> }> } | null };

export type DivisionsQualifiedTeamsQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
}>;


export type DivisionsQualifiedTeamsQuery = { __typename?: 'Query', divisions: Array<{ __typename?: 'Division', division_id: string, name: string, qualified_teams: Array<{ __typename?: 'Team', team_id: string, team_name: string, extra?: { __typename?: 'TeamExtra', prev_qual_age?: string | null, prev_qual_division?: string | null, earned_at?: string | null } | null }> }> };

export type FavoriteTeamsStandingQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  teamsIds: Array<Scalars['ID']['input']> | Scalars['ID']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
}>;


export type FavoriteTeamsStandingQuery = { __typename?: 'Query', favoriteTeams: Array<{ __typename?: 'Team', club_id: string, team_id: string, team_name: string, team_code?: string | null, division_id: string, division_name?: string | null, next_match?: { __typename?: 'Match', secs_start?: number | null, external: { __typename?: 'MatchExternal', pool_bracket_info: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string }, court_info: { __typename?: 'CourtShortInfo', short_name?: string | null } } } | null, division_standing?: { __typename?: 'DivisionStanding', matches_won: number, matches_lost: number, sets_won: number, sets_lost: number, sets_pct: number, points_ratio: number, rank: number, seed?: number | null, heading: string, heading_priority: number } | null }> };

export type PaginatedDivisionTeamsQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  divisionId: Scalars['ID']['input'];
  page: Scalars['Float']['input'];
  pageSize: Scalars['Float']['input'];
  search?: InputMaybe<Scalars['String']['input']>;
}>;


export type PaginatedDivisionTeamsQuery = { __typename?: 'Query', paginatedDivisionTeams: { __typename?: 'PaginatedTeams', items: Array<{ __typename?: 'Team', team_id: string, team_name: string, division_id: string, next_match?: { __typename?: 'Match', secs_start?: number | null, external: { __typename?: 'MatchExternal', opponent_display_name: string, court_info: { __typename?: 'CourtShortInfo', short_name?: string | null }, pool_bracket_info: { __typename?: 'PoolOrBracketShortInfo', is_pool: number, uuid: string } } } | null, division_standing?: { __typename?: 'DivisionStanding', matches_won: number, matches_lost: number, sets_won: number, sets_lost: number, rank: number } | null }>, page_info: { __typename?: 'PageInfo', page: number, page_size: number, page_count: number, item_count: number } } };

export type DivisionTeamsStandingQueryVariables = Exact<{
  eswId: Scalars['ID']['input'];
  divisionId: Scalars['ID']['input'];
}>;


export type DivisionTeamsStandingQuery = { __typename?: 'Query', divisionTeamsStanding: Array<{ __typename?: 'Team', team_id: string, team_name: string, team_code?: string | null, extra?: { __typename?: 'TeamExtra', show_previously_accepted_bid?: string | null, show_accepted_bid?: boolean | null } | null, division_standing?: { __typename?: 'DivisionStanding', matches_won: number, matches_lost: number, sets_won: number, sets_lost: number, sets_pct: number, points_ratio: number, points?: number | null, rank: number, seed?: number | null, heading: string } | null }> };

export type UpcomingMatchesQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type UpcomingMatchesQuery = { __typename?: 'Query', upcomingMatches?: Array<{ __typename?: 'UpcomingMatches', division_id?: number | null, division_name?: string | null, club_id?: number | null, club_name?: string | null, club_state?: string | null, club_code?: string | null, team_code?: string | null, team_id?: number | null, team_name?: string | null, opponent_id?: number | null, opponent_name?: string | null, secs_start?: number | null, secs_finished?: number | null, court_name?: string | null, matches_won?: number | null, matches_lost?: number | null, sets_won?: number | null, sets_lost?: number | null, sets_pct?: number | null, seed?: number | null }> | null };


export const EventWithRosterAndUpcomingMatchesDocument = gql`
    query EventWithRosterAndUpcomingMatches($id: ID!) {
  upcomingMatches(id: $id) {
    division_id
    division_name
    club_id
    club_name
    club_state
    club_code
    team_code
    team_id
    team_name
    opponent_id
    opponent_name
    secs_start
    secs_finished
    court_name
    matches_won
    matches_lost
    sets_won
    sets_lost
    sets_pct
    seed
  }
  roster(id: $id) {
    event_id
    event_name
    state
    club_name
    organization_code
    team_name
    roster_team_id
    roster_athletes {
      id
      first
      last
      short_position
      uniform
      gradyear
    }
    roster_staff {
      id
      first
      last
      role_name
      sort_order
    }
  }
  event(id: $id) {
    event_id
    hide_seeds
    name
    long_name
    days
    city
    state
    event_notes
    address
    is_with_prev_qual
    tickets_published
    tickets_code
    is_require_recipient_name_for_each_ticket
    locations {
      location_name
    }
    divisions {
      division_id
    }
    clubs {
      roster_club_id
    }
    athletes {
      first
      last
    }
    teams {
      division_name
      division_short_name
      club_name
      team_name
      organization_code
      roster_team_id
      roster_club_id
      division_id
    }
    teams_settings {
      sort_by
      hide_standings
    }
  }
}
    `;

/**
 * __useEventWithRosterAndUpcomingMatchesQuery__
 *
 * To run a query within a React component, call `useEventWithRosterAndUpcomingMatchesQuery` and pass it any options that fit your needs.
 * When your component renders, `useEventWithRosterAndUpcomingMatchesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useEventWithRosterAndUpcomingMatchesQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useEventWithRosterAndUpcomingMatchesQuery(baseOptions: Apollo.QueryHookOptions<EventWithRosterAndUpcomingMatchesQuery, EventWithRosterAndUpcomingMatchesQueryVariables> & ({ variables: EventWithRosterAndUpcomingMatchesQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<EventWithRosterAndUpcomingMatchesQuery, EventWithRosterAndUpcomingMatchesQueryVariables>(EventWithRosterAndUpcomingMatchesDocument, options);
      }
export function useEventWithRosterAndUpcomingMatchesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<EventWithRosterAndUpcomingMatchesQuery, EventWithRosterAndUpcomingMatchesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<EventWithRosterAndUpcomingMatchesQuery, EventWithRosterAndUpcomingMatchesQueryVariables>(EventWithRosterAndUpcomingMatchesDocument, options);
        }
export function useEventWithRosterAndUpcomingMatchesSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<EventWithRosterAndUpcomingMatchesQuery, EventWithRosterAndUpcomingMatchesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<EventWithRosterAndUpcomingMatchesQuery, EventWithRosterAndUpcomingMatchesQueryVariables>(EventWithRosterAndUpcomingMatchesDocument, options);
        }
export type EventWithRosterAndUpcomingMatchesQueryHookResult = ReturnType<typeof useEventWithRosterAndUpcomingMatchesQuery>;
export type EventWithRosterAndUpcomingMatchesLazyQueryHookResult = ReturnType<typeof useEventWithRosterAndUpcomingMatchesLazyQuery>;
export type EventWithRosterAndUpcomingMatchesSuspenseQueryHookResult = ReturnType<typeof useEventWithRosterAndUpcomingMatchesSuspenseQuery>;
export type EventWithRosterAndUpcomingMatchesQueryResult = Apollo.QueryResult<EventWithRosterAndUpcomingMatchesQuery, EventWithRosterAndUpcomingMatchesQueryVariables>;
export const PaginatedAthletesDocument = gql`
    query PaginatedAthletes($eswId: ID!, $page: Float!, $pageSize: Float!, $search: String) {
  paginatedAthletes(
    eventKey: $eswId
    page: $page
    pageSize: $pageSize
    search: $search
  ) {
    items {
      athlete_id
      first
      last
      club_name
      team_id
      team_name
      organization_code
      state
      short_position
      uniform
    }
    page_info {
      page
      page_size
      page_count
      item_count
    }
  }
}
    `;

/**
 * __usePaginatedAthletesQuery__
 *
 * To run a query within a React component, call `usePaginatedAthletesQuery` and pass it any options that fit your needs.
 * When your component renders, `usePaginatedAthletesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePaginatedAthletesQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      page: // value for 'page'
 *      pageSize: // value for 'pageSize'
 *      search: // value for 'search'
 *   },
 * });
 */
export function usePaginatedAthletesQuery(baseOptions: Apollo.QueryHookOptions<PaginatedAthletesQuery, PaginatedAthletesQueryVariables> & ({ variables: PaginatedAthletesQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PaginatedAthletesQuery, PaginatedAthletesQueryVariables>(PaginatedAthletesDocument, options);
      }
export function usePaginatedAthletesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PaginatedAthletesQuery, PaginatedAthletesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PaginatedAthletesQuery, PaginatedAthletesQueryVariables>(PaginatedAthletesDocument, options);
        }
export function usePaginatedAthletesSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<PaginatedAthletesQuery, PaginatedAthletesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<PaginatedAthletesQuery, PaginatedAthletesQueryVariables>(PaginatedAthletesDocument, options);
        }
export type PaginatedAthletesQueryHookResult = ReturnType<typeof usePaginatedAthletesQuery>;
export type PaginatedAthletesLazyQueryHookResult = ReturnType<typeof usePaginatedAthletesLazyQuery>;
export type PaginatedAthletesSuspenseQueryHookResult = ReturnType<typeof usePaginatedAthletesSuspenseQuery>;
export type PaginatedAthletesQueryResult = Apollo.QueryResult<PaginatedAthletesQuery, PaginatedAthletesQueryVariables>;
export const BracketDocument = gql`
    query Bracket($id: ID!, $poolId: String!) {
  bracket(id: $id, poolId: $poolId) {
    display_name
    division_name
    pool {
      flow_chart
    }
    matches {
      team1_roster_id
      team2_roster_id
      team1_name
      team2_name
      match_number
      date_start
      court_name
      ref_name
      show_previously_accepted_bid_team1
      show_previously_accepted_bid_team2
      source {
        ref {
          id
          name
          seed
        }
        team1 {
          id
          name
          seed
        }
        team2 {
          id
          name
          seed
        }
      }
      results {
        set1
        set2
        set3
        set4
        set5
        winner
        team1 {
          sets_won
          sets_lost
          points_won
          points_lost
          sets_pct
          matches_lost
          matches_won
          matches_pct
          scores
          roster_team_id
        }
        team2 {
          sets_won
          sets_lost
          points_won
          points_lost
          sets_pct
          matches_lost
          matches_won
          matches_pct
          scores
          roster_team_id
        }
      }
    }
  }
}
    `;

/**
 * __useBracketQuery__
 *
 * To run a query within a React component, call `useBracketQuery` and pass it any options that fit your needs.
 * When your component renders, `useBracketQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useBracketQuery({
 *   variables: {
 *      id: // value for 'id'
 *      poolId: // value for 'poolId'
 *   },
 * });
 */
export function useBracketQuery(baseOptions: Apollo.QueryHookOptions<BracketQuery, BracketQueryVariables> & ({ variables: BracketQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<BracketQuery, BracketQueryVariables>(BracketDocument, options);
      }
export function useBracketLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<BracketQuery, BracketQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<BracketQuery, BracketQueryVariables>(BracketDocument, options);
        }
export function useBracketSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<BracketQuery, BracketQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<BracketQuery, BracketQueryVariables>(BracketDocument, options);
        }
export type BracketQueryHookResult = ReturnType<typeof useBracketQuery>;
export type BracketLazyQueryHookResult = ReturnType<typeof useBracketLazyQuery>;
export type BracketSuspenseQueryHookResult = ReturnType<typeof useBracketSuspenseQuery>;
export type BracketQueryResult = Apollo.QueryResult<BracketQuery, BracketQueryVariables>;
export const PaginatedClubsTeamsDocument = gql`
    query PaginatedClubsTeams($eswId: ID!, $page: Float!, $pageSize: Float!, $search: String) {
  paginatedClubs(
    eventKey: $eswId
    page: $page
    pageSize: $pageSize
    search: $search
  ) {
    items {
      roster_club_id
      club_name
      state
      club_code
      teams_count
      teams {
        team_id
        club_id
        team_name
        division_id
        division_name
        manual_club_name
        division_standing {
          matches_won
          matches_lost
          sets_won
          sets_lost
          seed
          rank
        }
        next_match {
          secs_start
          external {
            opponent_display_name
            court_info {
              short_name
            }
          }
        }
      }
    }
    page_info {
      page
      page_size
      page_count
      item_count
    }
  }
}
    `;

/**
 * __usePaginatedClubsTeamsQuery__
 *
 * To run a query within a React component, call `usePaginatedClubsTeamsQuery` and pass it any options that fit your needs.
 * When your component renders, `usePaginatedClubsTeamsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePaginatedClubsTeamsQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      page: // value for 'page'
 *      pageSize: // value for 'pageSize'
 *      search: // value for 'search'
 *   },
 * });
 */
export function usePaginatedClubsTeamsQuery(baseOptions: Apollo.QueryHookOptions<PaginatedClubsTeamsQuery, PaginatedClubsTeamsQueryVariables> & ({ variables: PaginatedClubsTeamsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PaginatedClubsTeamsQuery, PaginatedClubsTeamsQueryVariables>(PaginatedClubsTeamsDocument, options);
      }
export function usePaginatedClubsTeamsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PaginatedClubsTeamsQuery, PaginatedClubsTeamsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PaginatedClubsTeamsQuery, PaginatedClubsTeamsQueryVariables>(PaginatedClubsTeamsDocument, options);
        }
export function usePaginatedClubsTeamsSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<PaginatedClubsTeamsQuery, PaginatedClubsTeamsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<PaginatedClubsTeamsQuery, PaginatedClubsTeamsQueryVariables>(PaginatedClubsTeamsDocument, options);
        }
export type PaginatedClubsTeamsQueryHookResult = ReturnType<typeof usePaginatedClubsTeamsQuery>;
export type PaginatedClubsTeamsLazyQueryHookResult = ReturnType<typeof usePaginatedClubsTeamsLazyQuery>;
export type PaginatedClubsTeamsSuspenseQueryHookResult = ReturnType<typeof usePaginatedClubsTeamsSuspenseQuery>;
export type PaginatedClubsTeamsQueryResult = Apollo.QueryResult<PaginatedClubsTeamsQuery, PaginatedClubsTeamsQueryVariables>;
export const CourtMatchesDocument = gql`
    query CourtMatches($id: ID!, $day: String!, $hour: String!, $hours: String!, $division: String!) {
  courtMatches(
    id: $id
    day: $day
    hour: $hour
    hours: $hours
    division: $division
  ) {
    divisions {
      division_id
      division_name
      gender
      sort_order
      max_age
      level_sort_order
      level
    }
    hours {
      time
      time12
      default
    }
    courts {
      court_id
      court_name
      event_id
      short_name
      matches {
        match_id
        match_name
        division_id
        division_name
        division_short_name
        date_start
        date_end
        secs_finished
        team1_roster_id
        team2_roster_id
        team_1_name
        team_2_name
        team_ref_name
        color
        results {
          winner
          team1 {
            scores
          }
          team2 {
            scores
          }
        }
      }
    }
  }
}
    `;

/**
 * __useCourtMatchesQuery__
 *
 * To run a query within a React component, call `useCourtMatchesQuery` and pass it any options that fit your needs.
 * When your component renders, `useCourtMatchesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCourtMatchesQuery({
 *   variables: {
 *      id: // value for 'id'
 *      day: // value for 'day'
 *      hour: // value for 'hour'
 *      hours: // value for 'hours'
 *      division: // value for 'division'
 *   },
 * });
 */
export function useCourtMatchesQuery(baseOptions: Apollo.QueryHookOptions<CourtMatchesQuery, CourtMatchesQueryVariables> & ({ variables: CourtMatchesQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<CourtMatchesQuery, CourtMatchesQueryVariables>(CourtMatchesDocument, options);
      }
export function useCourtMatchesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<CourtMatchesQuery, CourtMatchesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<CourtMatchesQuery, CourtMatchesQueryVariables>(CourtMatchesDocument, options);
        }
export function useCourtMatchesSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<CourtMatchesQuery, CourtMatchesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<CourtMatchesQuery, CourtMatchesQueryVariables>(CourtMatchesDocument, options);
        }
export type CourtMatchesQueryHookResult = ReturnType<typeof useCourtMatchesQuery>;
export type CourtMatchesLazyQueryHookResult = ReturnType<typeof useCourtMatchesLazyQuery>;
export type CourtMatchesSuspenseQueryHookResult = ReturnType<typeof useCourtMatchesSuspenseQuery>;
export type CourtMatchesQueryResult = Apollo.QueryResult<CourtMatchesQuery, CourtMatchesQueryVariables>;
export const DivisionOverviewDocument = gql`
    query DivisionOverview($eswId: ID!, $divisionId: ID!) {
  divisionPoolBrackets(eventKey: $eswId, divisionId: $divisionId) {
    ... on Pool {
      is_pool
      uuid
      round_id
      sort_priority
      short_name
      display_name
      date_start
      division_id
      settings {
        SetCount
        PlayAllSets
        WinningPoints
      }
      external {
        courts_short_info {
          uuid
          short_name
        }
      }
      teams {
        team_id
        team_name
        master_team_id
        extra {
          show_accepted_bid
          show_previously_accepted_bid
        }
        division_standing {
          seed
          heading
          heading_priority
        }
        pool_bracket_stat {
          sets_pct
          sets_won
          sets_lost
          matches_won
          matches_lost
          points_ratio
        }
      }
      matches {
        team_id
        opponent_id
        ref_team_id
        match_id
        match_name
        match_number
        secs_start
        secs_end
        secs_finished
        source {
          team {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          opponent {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          ref {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
        }
        results {
          sets
          winner_team {
            scores
            roster_team_id
          }
        }
        finishes {
          winner {
            team_name
            team_id
            next_match {
              court
              match_id
              week_day
              secs_start
              display_name
              start_time_string
              external {
                pool_bracket_info {
                  is_pool
                  uuid
                }
              }
            }
            next_ref {
              court
              match_id
              week_day
              secs_start
              display_name
              start_time_string
              external {
                pool_bracket_info {
                  is_pool
                  uuid
                }
              }
            }
          }
          looser {
            team_name
            team_id
            next_match {
              court
              match_id
              week_day
              secs_start
              display_name
              start_time_string
              external {
                pool_bracket_info {
                  is_pool
                  uuid
                }
              }
            }
            next_ref {
              court
              match_id
              week_day
              secs_start
              display_name
              start_time_string
              external {
                pool_bracket_info {
                  is_pool
                  uuid
                }
              }
            }
          }
        }
        external {
          team_pb_seed {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          opponent_pb_seed {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          court_info {
            uuid
            short_name
          }
          team_display_name
          opponent_display_name
          ref_team_display_name
        }
      }
      pb_finishes {
        team_name
        team_id
        next_match {
          court
          match_id
          week_day
          secs_start
          display_name
          start_time_string
          external {
            pool_bracket_info {
              is_pool
              uuid
            }
          }
        }
        next_ref {
          court
          match_id
          week_day
          secs_start
          display_name
          start_time_string
          external {
            pool_bracket_info {
              uuid
              is_pool
            }
          }
        }
      }
    }
    ... on Bracket {
      is_pool
      uuid
      round_id
      sort_priority
      short_name
      display_name
      date_start
      division_id
      settings {
        SetCount
        PlayAllSets
        WinningPoints
      }
      external {
        courts_short_info {
          uuid
          short_name
        }
      }
      teams {
        team_id
        team_name
        master_team_id
        extra {
          show_accepted_bid
          show_previously_accepted_bid
        }
        division_standing {
          seed
          heading
          heading_priority
        }
        pool_bracket_stat {
          sets_pct
          sets_won
          sets_lost
          matches_won
          matches_lost
          points_ratio
        }
      }
      matches {
        team_id
        opponent_id
        ref_team_id
        match_id
        match_name
        match_number
        secs_start
        secs_end
        secs_finished
        source {
          team {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          opponent {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          ref {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
        }
        results {
          sets
          winner_team {
            scores
            roster_team_id
          }
        }
        finishes {
          winner {
            team_name
            team_id
            next_match {
              court
              match_id
              week_day
              secs_start
              display_name
              start_time_string
              external {
                pool_bracket_info {
                  is_pool
                  uuid
                }
              }
            }
            next_ref {
              court
              match_id
              week_day
              secs_start
              display_name
              start_time_string
              external {
                pool_bracket_info {
                  is_pool
                  uuid
                }
              }
            }
          }
          looser {
            team_name
            team_id
            next_match {
              court
              match_id
              week_day
              secs_start
              display_name
              start_time_string
              external {
                pool_bracket_info {
                  is_pool
                  uuid
                }
              }
            }
            next_ref {
              court
              match_id
              week_day
              secs_start
              display_name
              start_time_string
              external {
                pool_bracket_info {
                  is_pool
                  uuid
                }
              }
            }
          }
        }
        external {
          team_pb_seed {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          opponent_pb_seed {
            id
            name
            type
            seed
            overallSeed
            reseedRank
          }
          court_info {
            uuid
            short_name
          }
          team_display_name
          opponent_display_name
          ref_team_display_name
        }
      }
      pb_finishes {
        team_name
        team_id
        next_match {
          court
          match_id
          week_day
          secs_start
          display_name
          start_time_string
          external {
            pool_bracket_info {
              is_pool
              uuid
            }
          }
        }
        next_ref {
          court
          match_id
          week_day
          secs_start
          display_name
          start_time_string
          external {
            pool_bracket_info {
              uuid
              is_pool
            }
          }
        }
      }
    }
  }
}
    `;

/**
 * __useDivisionOverviewQuery__
 *
 * To run a query within a React component, call `useDivisionOverviewQuery` and pass it any options that fit your needs.
 * When your component renders, `useDivisionOverviewQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useDivisionOverviewQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      divisionId: // value for 'divisionId'
 *   },
 * });
 */
export function useDivisionOverviewQuery(baseOptions: Apollo.QueryHookOptions<DivisionOverviewQuery, DivisionOverviewQueryVariables> & ({ variables: DivisionOverviewQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<DivisionOverviewQuery, DivisionOverviewQueryVariables>(DivisionOverviewDocument, options);
      }
export function useDivisionOverviewLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<DivisionOverviewQuery, DivisionOverviewQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<DivisionOverviewQuery, DivisionOverviewQueryVariables>(DivisionOverviewDocument, options);
        }
export function useDivisionOverviewSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<DivisionOverviewQuery, DivisionOverviewQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<DivisionOverviewQuery, DivisionOverviewQueryVariables>(DivisionOverviewDocument, options);
        }
export type DivisionOverviewQueryHookResult = ReturnType<typeof useDivisionOverviewQuery>;
export type DivisionOverviewLazyQueryHookResult = ReturnType<typeof useDivisionOverviewLazyQuery>;
export type DivisionOverviewSuspenseQueryHookResult = ReturnType<typeof useDivisionOverviewSuspenseQuery>;
export type DivisionOverviewQueryResult = Apollo.QueryResult<DivisionOverviewQuery, DivisionOverviewQueryVariables>;
export const EventForDivisionPageDocument = gql`
    query EventForDivisionPage($id: ID!) {
  event(id: $id) {
    event_id
    hide_seeds
    name
    long_name
    days
    city
    state
    event_notes
    address
    is_with_prev_qual
    tickets_published
    is_require_recipient_name_for_each_ticket
    tickets_code
    locations {
      location_name
    }
    divisions {
      short_name
      division_id
    }
    clubs {
      roster_club_id
    }
    athletes {
      first
      last
    }
    teams {
      roster_team_id
    }
    teams_settings {
      sort_by
      hide_standings
    }
  }
}
    `;

/**
 * __useEventForDivisionPageQuery__
 *
 * To run a query within a React component, call `useEventForDivisionPageQuery` and pass it any options that fit your needs.
 * When your component renders, `useEventForDivisionPageQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useEventForDivisionPageQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useEventForDivisionPageQuery(baseOptions: Apollo.QueryHookOptions<EventForDivisionPageQuery, EventForDivisionPageQueryVariables> & ({ variables: EventForDivisionPageQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<EventForDivisionPageQuery, EventForDivisionPageQueryVariables>(EventForDivisionPageDocument, options);
      }
export function useEventForDivisionPageLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<EventForDivisionPageQuery, EventForDivisionPageQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<EventForDivisionPageQuery, EventForDivisionPageQueryVariables>(EventForDivisionPageDocument, options);
        }
export function useEventForDivisionPageSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<EventForDivisionPageQuery, EventForDivisionPageQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<EventForDivisionPageQuery, EventForDivisionPageQueryVariables>(EventForDivisionPageDocument, options);
        }
export type EventForDivisionPageQueryHookResult = ReturnType<typeof useEventForDivisionPageQuery>;
export type EventForDivisionPageLazyQueryHookResult = ReturnType<typeof useEventForDivisionPageLazyQuery>;
export type EventForDivisionPageSuspenseQueryHookResult = ReturnType<typeof useEventForDivisionPageSuspenseQuery>;
export type EventForDivisionPageQueryResult = Apollo.QueryResult<EventForDivisionPageQuery, EventForDivisionPageQueryVariables>;
export const EventDocument = gql`
    query Event($id: ID!) {
  event(id: $id) {
    event_id
    hide_seeds
    name
    long_name
    days
    city
    state
    event_notes
    address
    is_with_prev_qual
    tickets_published
    is_require_recipient_name_for_each_ticket
    tickets_code
    locations {
      location_name
    }
    divisions {
      gender
      name
      event_id
      short_name
      teams_count
      division_id
    }
    clubs {
      club_name
      teams_count
      roster_club_id
      state
      club_code
    }
    athletes {
      age
      gender
      first
      last
      team_organization_code
      team_name
      jersey
      club_name
      division_id
      roster_team_id
    }
    teams {
      division_name
      division_short_name
      club_name
      team_name
      organization_code
      roster_team_id
      roster_club_id
      division_id
      gender
    }
    teams_settings {
      sort_by
    }
  }
}
    `;

/**
 * __useEventQuery__
 *
 * To run a query within a React component, call `useEventQuery` and pass it any options that fit your needs.
 * When your component renders, `useEventQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useEventQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useEventQuery(baseOptions: Apollo.QueryHookOptions<EventQuery, EventQueryVariables> & ({ variables: EventQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<EventQuery, EventQueryVariables>(EventDocument, options);
      }
export function useEventLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<EventQuery, EventQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<EventQuery, EventQueryVariables>(EventDocument, options);
        }
export function useEventSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<EventQuery, EventQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<EventQuery, EventQueryVariables>(EventDocument, options);
        }
export type EventQueryHookResult = ReturnType<typeof useEventQuery>;
export type EventLazyQueryHookResult = ReturnType<typeof useEventLazyQuery>;
export type EventSuspenseQueryHookResult = ReturnType<typeof useEventSuspenseQuery>;
export type EventQueryResult = Apollo.QueryResult<EventQuery, EventQueryVariables>;
export const EventForDivisionsPageDocument = gql`
    query EventForDivisionsPage($id: ID!) {
  event(id: $id) {
    event_id
    hide_seeds
    name
    long_name
    days
    city
    state
    event_notes
    address
    is_with_prev_qual
    tickets_published
    is_require_recipient_name_for_each_ticket
    tickets_code
    locations {
      location_name
    }
    divisions {
      gender
      name
      event_id
      short_name
      teams_count
      division_id
    }
    clubs {
      roster_club_id
    }
    athletes {
      first
      last
    }
    teams {
      roster_team_id
    }
    teams_settings {
      sort_by
    }
  }
}
    `;

/**
 * __useEventForDivisionsPageQuery__
 *
 * To run a query within a React component, call `useEventForDivisionsPageQuery` and pass it any options that fit your needs.
 * When your component renders, `useEventForDivisionsPageQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useEventForDivisionsPageQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useEventForDivisionsPageQuery(baseOptions: Apollo.QueryHookOptions<EventForDivisionsPageQuery, EventForDivisionsPageQueryVariables> & ({ variables: EventForDivisionsPageQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<EventForDivisionsPageQuery, EventForDivisionsPageQueryVariables>(EventForDivisionsPageDocument, options);
      }
export function useEventForDivisionsPageLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<EventForDivisionsPageQuery, EventForDivisionsPageQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<EventForDivisionsPageQuery, EventForDivisionsPageQueryVariables>(EventForDivisionsPageDocument, options);
        }
export function useEventForDivisionsPageSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<EventForDivisionsPageQuery, EventForDivisionsPageQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<EventForDivisionsPageQuery, EventForDivisionsPageQueryVariables>(EventForDivisionsPageDocument, options);
        }
export type EventForDivisionsPageQueryHookResult = ReturnType<typeof useEventForDivisionsPageQuery>;
export type EventForDivisionsPageLazyQueryHookResult = ReturnType<typeof useEventForDivisionsPageLazyQuery>;
export type EventForDivisionsPageSuspenseQueryHookResult = ReturnType<typeof useEventForDivisionsPageSuspenseQuery>;
export type EventForDivisionsPageQueryResult = Apollo.QueryResult<EventForDivisionsPageQuery, EventForDivisionsPageQueryVariables>;
export const DaysOfEventDocument = gql`
    query DaysOfEvent($id: ID!) {
  event(id: $id) {
    days
  }
}
    `;

/**
 * __useDaysOfEventQuery__
 *
 * To run a query within a React component, call `useDaysOfEventQuery` and pass it any options that fit your needs.
 * When your component renders, `useDaysOfEventQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useDaysOfEventQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useDaysOfEventQuery(baseOptions: Apollo.QueryHookOptions<DaysOfEventQuery, DaysOfEventQueryVariables> & ({ variables: DaysOfEventQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<DaysOfEventQuery, DaysOfEventQueryVariables>(DaysOfEventDocument, options);
      }
export function useDaysOfEventLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<DaysOfEventQuery, DaysOfEventQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<DaysOfEventQuery, DaysOfEventQueryVariables>(DaysOfEventDocument, options);
        }
export function useDaysOfEventSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<DaysOfEventQuery, DaysOfEventQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<DaysOfEventQuery, DaysOfEventQueryVariables>(DaysOfEventDocument, options);
        }
export type DaysOfEventQueryHookResult = ReturnType<typeof useDaysOfEventQuery>;
export type DaysOfEventLazyQueryHookResult = ReturnType<typeof useDaysOfEventLazyQuery>;
export type DaysOfEventSuspenseQueryHookResult = ReturnType<typeof useDaysOfEventSuspenseQuery>;
export type DaysOfEventQueryResult = Apollo.QueryResult<DaysOfEventQuery, DaysOfEventQueryVariables>;
export const EventCountsDocument = gql`
    query EventCounts($eswId: ID!) {
  paginatedAthletes(eventKey: $eswId, page: 1, pageSize: 1) {
    page_info {
      item_count
    }
  }
  paginatedStaff(eventKey: $eswId, page: 1, pageSize: 1) {
    page_info {
      item_count
    }
  }
  paginatedClubs(eventKey: $eswId, page: 1, pageSize: 1) {
    page_info {
      item_count
    }
  }
}
    `;

/**
 * __useEventCountsQuery__
 *
 * To run a query within a React component, call `useEventCountsQuery` and pass it any options that fit your needs.
 * When your component renders, `useEventCountsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useEventCountsQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *   },
 * });
 */
export function useEventCountsQuery(baseOptions: Apollo.QueryHookOptions<EventCountsQuery, EventCountsQueryVariables> & ({ variables: EventCountsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<EventCountsQuery, EventCountsQueryVariables>(EventCountsDocument, options);
      }
export function useEventCountsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<EventCountsQuery, EventCountsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<EventCountsQuery, EventCountsQueryVariables>(EventCountsDocument, options);
        }
export function useEventCountsSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<EventCountsQuery, EventCountsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<EventCountsQuery, EventCountsQueryVariables>(EventCountsDocument, options);
        }
export type EventCountsQueryHookResult = ReturnType<typeof useEventCountsQuery>;
export type EventCountsLazyQueryHookResult = ReturnType<typeof useEventCountsLazyQuery>;
export type EventCountsSuspenseQueryHookResult = ReturnType<typeof useEventCountsSuspenseQuery>;
export type EventCountsQueryResult = Apollo.QueryResult<EventCountsQuery, EventCountsQueryVariables>;
export const EventDetailsDocument = gql`
    query EventDetails($eswId: ID!) {
  event(id: $eswId) {
    id
    long_name
    city
    state
    tickets_published
    schedule_published
    is_require_recipient_name_for_each_ticket
    tickets_code
    allow_point_of_sales
    sales_hub_point_of_sale_id
    hide_seeds
    days
    has_rosters
    is_with_prev_qual
    event_notes
    address
    sport_sanctioning
    locations {
      location_name
    }
    teams_settings {
      baller_tv_available
      hide_standings
      sort_by
      manual_club_names
    }
  }
  divisions(eventKey: $eswId) {
    division_id
    name
    teams_count
    short_name
    has_flow_chart
    media(filterFileTypes: ["flowchart"]) {
      media_id
      division_id
      file_type
      path
    }
    matches_time_ranges {
      day
      start_time
      end_time
    }
    rounds {
      uuid
      division_id
      sort_priority
      name
      short_name
      first_match_start
      last_match_start
    }
  }
}
    `;

/**
 * __useEventDetailsQuery__
 *
 * To run a query within a React component, call `useEventDetailsQuery` and pass it any options that fit your needs.
 * When your component renders, `useEventDetailsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useEventDetailsQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *   },
 * });
 */
export function useEventDetailsQuery(baseOptions: Apollo.QueryHookOptions<EventDetailsQuery, EventDetailsQueryVariables> & ({ variables: EventDetailsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<EventDetailsQuery, EventDetailsQueryVariables>(EventDetailsDocument, options);
      }
export function useEventDetailsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<EventDetailsQuery, EventDetailsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<EventDetailsQuery, EventDetailsQueryVariables>(EventDetailsDocument, options);
        }
export function useEventDetailsSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<EventDetailsQuery, EventDetailsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<EventDetailsQuery, EventDetailsQueryVariables>(EventDetailsDocument, options);
        }
export type EventDetailsQueryHookResult = ReturnType<typeof useEventDetailsQuery>;
export type EventDetailsLazyQueryHookResult = ReturnType<typeof useEventDetailsLazyQuery>;
export type EventDetailsSuspenseQueryHookResult = ReturnType<typeof useEventDetailsSuspenseQuery>;
export type EventDetailsQueryResult = Apollo.QueryResult<EventDetailsQuery, EventDetailsQueryVariables>;
export const EventWithRosterDocument = gql`
    query EventWithRoster($id: ID!) {
  roster(id: $id) {
    event_id
    event_name
    state
    club_name
    organization_code
    team_name
    roster_team_id
    roster_athletes {
      id
      first
      last
      short_position
      uniform
      gradyear
    }
    roster_staff {
      id
      first
      last
      role_name
      sort_order
    }
  }
  event(id: $id) {
    event_id
    hide_seeds
    name
    long_name
    days
    city
    state
    event_notes
    address
    is_with_prev_qual
    tickets_published
    tickets_code
    is_require_recipient_name_for_each_ticket
    locations {
      location_name
    }
    divisions {
      gender
      name
      event_id
      short_name
      teams_count
      division_id
    }
    clubs {
      club_name
      teams_count
      roster_club_id
      state
    }
    athletes {
      age
      gender
      first
      last
      team_organization_code
      team_name
      jersey
      club_name
      division_id
      roster_team_id
    }
    teams {
      division_name
      division_short_name
      club_name
      team_name
      organization_code
      roster_team_id
      roster_club_id
      division_id
      gender
    }
    teams_settings {
      sort_by
      hide_standings
    }
  }
}
    `;

/**
 * __useEventWithRosterQuery__
 *
 * To run a query within a React component, call `useEventWithRosterQuery` and pass it any options that fit your needs.
 * When your component renders, `useEventWithRosterQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useEventWithRosterQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useEventWithRosterQuery(baseOptions: Apollo.QueryHookOptions<EventWithRosterQuery, EventWithRosterQueryVariables> & ({ variables: EventWithRosterQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<EventWithRosterQuery, EventWithRosterQueryVariables>(EventWithRosterDocument, options);
      }
export function useEventWithRosterLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<EventWithRosterQuery, EventWithRosterQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<EventWithRosterQuery, EventWithRosterQueryVariables>(EventWithRosterDocument, options);
        }
export function useEventWithRosterSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<EventWithRosterQuery, EventWithRosterQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<EventWithRosterQuery, EventWithRosterQueryVariables>(EventWithRosterDocument, options);
        }
export type EventWithRosterQueryHookResult = ReturnType<typeof useEventWithRosterQuery>;
export type EventWithRosterLazyQueryHookResult = ReturnType<typeof useEventWithRosterLazyQuery>;
export type EventWithRosterSuspenseQueryHookResult = ReturnType<typeof useEventWithRosterSuspenseQuery>;
export type EventWithRosterQueryResult = Apollo.QueryResult<EventWithRosterQuery, EventWithRosterQueryVariables>;
export const EventWithRosterForAboutPageDocument = gql`
    query EventWithRosterForAboutPage($id: ID!) {
  roster(id: $id) {
    roster_athletes {
      id
    }
    roster_staff {
      id
    }
  }
  event(id: $id) {
    event_id
    has_rosters
    hide_seeds
    name
    long_name
    days
    city
    state
    event_notes
    address
    is_with_prev_qual
    tickets_published
    tickets_code
    is_require_recipient_name_for_each_ticket
    locations {
      location_name
    }
    divisions {
      division_id
    }
    clubs {
      roster_club_id
    }
    athletes {
      first
      last
    }
    teams {
      roster_team_id
    }
    teams_settings {
      sort_by
      hide_standings
    }
  }
}
    `;

/**
 * __useEventWithRosterForAboutPageQuery__
 *
 * To run a query within a React component, call `useEventWithRosterForAboutPageQuery` and pass it any options that fit your needs.
 * When your component renders, `useEventWithRosterForAboutPageQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useEventWithRosterForAboutPageQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useEventWithRosterForAboutPageQuery(baseOptions: Apollo.QueryHookOptions<EventWithRosterForAboutPageQuery, EventWithRosterForAboutPageQueryVariables> & ({ variables: EventWithRosterForAboutPageQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<EventWithRosterForAboutPageQuery, EventWithRosterForAboutPageQueryVariables>(EventWithRosterForAboutPageDocument, options);
      }
export function useEventWithRosterForAboutPageLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<EventWithRosterForAboutPageQuery, EventWithRosterForAboutPageQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<EventWithRosterForAboutPageQuery, EventWithRosterForAboutPageQueryVariables>(EventWithRosterForAboutPageDocument, options);
        }
export function useEventWithRosterForAboutPageSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<EventWithRosterForAboutPageQuery, EventWithRosterForAboutPageQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<EventWithRosterForAboutPageQuery, EventWithRosterForAboutPageQueryVariables>(EventWithRosterForAboutPageDocument, options);
        }
export type EventWithRosterForAboutPageQueryHookResult = ReturnType<typeof useEventWithRosterForAboutPageQuery>;
export type EventWithRosterForAboutPageLazyQueryHookResult = ReturnType<typeof useEventWithRosterForAboutPageLazyQuery>;
export type EventWithRosterForAboutPageSuspenseQueryHookResult = ReturnType<typeof useEventWithRosterForAboutPageSuspenseQuery>;
export type EventWithRosterForAboutPageQueryResult = Apollo.QueryResult<EventWithRosterForAboutPageQuery, EventWithRosterForAboutPageQueryVariables>;
export const EventWithRosterForRosterPageDocument = gql`
    query EventWithRosterForRosterPage($id: ID!) {
  roster(id: $id) {
    event_id
    event_name
    state
    club_name
    organization_code
    team_name
    roster_team_id
    roster_athletes {
      id
      first
      last
      short_position
      uniform
      gradyear
    }
    roster_staff {
      id
      first
      last
      role_name
      sort_order
    }
  }
  event(id: $id) {
    event_id
    hide_seeds
    name
    long_name
    days
    city
    state
    event_notes
    address
    is_with_prev_qual
    tickets_published
    tickets_code
    is_require_recipient_name_for_each_ticket
    locations {
      location_name
    }
    divisions {
      division_id
    }
    clubs {
      roster_club_id
    }
    athletes {
      first
      last
    }
    teams {
      roster_team_id
    }
    teams_settings {
      sort_by
      hide_standings
    }
  }
}
    `;

/**
 * __useEventWithRosterForRosterPageQuery__
 *
 * To run a query within a React component, call `useEventWithRosterForRosterPageQuery` and pass it any options that fit your needs.
 * When your component renders, `useEventWithRosterForRosterPageQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useEventWithRosterForRosterPageQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useEventWithRosterForRosterPageQuery(baseOptions: Apollo.QueryHookOptions<EventWithRosterForRosterPageQuery, EventWithRosterForRosterPageQueryVariables> & ({ variables: EventWithRosterForRosterPageQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<EventWithRosterForRosterPageQuery, EventWithRosterForRosterPageQueryVariables>(EventWithRosterForRosterPageDocument, options);
      }
export function useEventWithRosterForRosterPageLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<EventWithRosterForRosterPageQuery, EventWithRosterForRosterPageQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<EventWithRosterForRosterPageQuery, EventWithRosterForRosterPageQueryVariables>(EventWithRosterForRosterPageDocument, options);
        }
export function useEventWithRosterForRosterPageSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<EventWithRosterForRosterPageQuery, EventWithRosterForRosterPageQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<EventWithRosterForRosterPageQuery, EventWithRosterForRosterPageQueryVariables>(EventWithRosterForRosterPageDocument, options);
        }
export type EventWithRosterForRosterPageQueryHookResult = ReturnType<typeof useEventWithRosterForRosterPageQuery>;
export type EventWithRosterForRosterPageLazyQueryHookResult = ReturnType<typeof useEventWithRosterForRosterPageLazyQuery>;
export type EventWithRosterForRosterPageSuspenseQueryHookResult = ReturnType<typeof useEventWithRosterForRosterPageSuspenseQuery>;
export type EventWithRosterForRosterPageQueryResult = Apollo.QueryResult<EventWithRosterForRosterPageQuery, EventWithRosterForRosterPageQueryVariables>;
export const PaginatedEventsDocument = gql`
    query PaginatedEvents($page: Float!, $pageSize: Float!, $search: String, $startBefore: String, $startAfter: String, $endBefore: String, $endAfter: String, $years: [String!], $asc: Boolean) {
  paginatedEvents(
    page: $page
    pageSize: $pageSize
    search: $search
    startBefore: $startBefore
    startAfter: $startAfter
    endBefore: $endBefore
    endAfter: $endAfter
    years: $years
    asc: $asc
  ) {
    items {
      event_id
      long_name
      city
      date_start
      state
      address
      schedule_published
      small_logo
    }
    page_info {
      page
      page_size
      page_count
      item_count
    }
  }
}
    `;

/**
 * __usePaginatedEventsQuery__
 *
 * To run a query within a React component, call `usePaginatedEventsQuery` and pass it any options that fit your needs.
 * When your component renders, `usePaginatedEventsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePaginatedEventsQuery({
 *   variables: {
 *      page: // value for 'page'
 *      pageSize: // value for 'pageSize'
 *      search: // value for 'search'
 *      startBefore: // value for 'startBefore'
 *      startAfter: // value for 'startAfter'
 *      endBefore: // value for 'endBefore'
 *      endAfter: // value for 'endAfter'
 *      years: // value for 'years'
 *      asc: // value for 'asc'
 *   },
 * });
 */
export function usePaginatedEventsQuery(baseOptions: Apollo.QueryHookOptions<PaginatedEventsQuery, PaginatedEventsQueryVariables> & ({ variables: PaginatedEventsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PaginatedEventsQuery, PaginatedEventsQueryVariables>(PaginatedEventsDocument, options);
      }
export function usePaginatedEventsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PaginatedEventsQuery, PaginatedEventsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PaginatedEventsQuery, PaginatedEventsQueryVariables>(PaginatedEventsDocument, options);
        }
export function usePaginatedEventsSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<PaginatedEventsQuery, PaginatedEventsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<PaginatedEventsQuery, PaginatedEventsQueryVariables>(PaginatedEventsDocument, options);
        }
export type PaginatedEventsQueryHookResult = ReturnType<typeof usePaginatedEventsQuery>;
export type PaginatedEventsLazyQueryHookResult = ReturnType<typeof usePaginatedEventsLazyQuery>;
export type PaginatedEventsSuspenseQueryHookResult = ReturnType<typeof usePaginatedEventsSuspenseQuery>;
export type PaginatedEventsQueryResult = Apollo.QueryResult<PaginatedEventsQuery, PaginatedEventsQueryVariables>;
export const FeatureMatchesDocument = gql`
    query FeatureMatches($id: ID!) {
  featureMatches(id: $id) {
    match_id
    division_id
    team1_id
    team1_name
    team2_id
    team2_name
    court_name
    court_short_name
    match_start
  }
}
    `;

/**
 * __useFeatureMatchesQuery__
 *
 * To run a query within a React component, call `useFeatureMatchesQuery` and pass it any options that fit your needs.
 * When your component renders, `useFeatureMatchesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFeatureMatchesQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useFeatureMatchesQuery(baseOptions: Apollo.QueryHookOptions<FeatureMatchesQuery, FeatureMatchesQueryVariables> & ({ variables: FeatureMatchesQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<FeatureMatchesQuery, FeatureMatchesQueryVariables>(FeatureMatchesDocument, options);
      }
export function useFeatureMatchesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<FeatureMatchesQuery, FeatureMatchesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<FeatureMatchesQuery, FeatureMatchesQueryVariables>(FeatureMatchesDocument, options);
        }
export function useFeatureMatchesSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<FeatureMatchesQuery, FeatureMatchesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<FeatureMatchesQuery, FeatureMatchesQueryVariables>(FeatureMatchesDocument, options);
        }
export type FeatureMatchesQueryHookResult = ReturnType<typeof useFeatureMatchesQuery>;
export type FeatureMatchesLazyQueryHookResult = ReturnType<typeof useFeatureMatchesLazyQuery>;
export type FeatureMatchesSuspenseQueryHookResult = ReturnType<typeof useFeatureMatchesSuspenseQuery>;
export type FeatureMatchesQueryResult = Apollo.QueryResult<FeatureMatchesQuery, FeatureMatchesQueryVariables>;
export const PoolDocument = gql`
    query Pool($id: ID!, $poolId: ID!) {
  pool(id: $id, poolId: $poolId) {
    division_short_name
    uuid
    display_name
    display_name_short
    team_count
    division_short_name
    upcoming_matches {
      court_name
      date_start
      display_name
      division_short_name
      footnote_play
      footnote_team1
      footnote_team2
      match_id
      ref_roster_id
      ref_team_code
      ref_team_name
      results
      team1_code
      team1_name
      team1_roster_id
      team2_code
      team2_name
      team2_roster_id
      team1_master_id
      team2_master_id
    }
    results {
      match_id
      team1_name
      team2_name
      team1_master_id
      team2_master_id
      ref_team_name
      date_start
      court_name
      results {
        set1
        set2
        set3
        set4
        set5
        team1 {
          scores
          matches_won
        }
        team2 {
          scores
          matches_won
        }
      }
    }
    standings {
      display_name
      pb_stats {
        team_id
        name
        rank
        matches_won
        matches_lost
        sets_won
        sets_lost
        sets_pct
        points_ratio
      }
    }
    pb_finishes {
      team_ids
      teams {
        team_name
        team_id
        next_match {
          display_name
          week_day
          start_time_string
          court
          secs_start
          match_id
          external {
            pool_bracket_info {
              is_pool
              uuid
            }
          }
        }
        next_ref {
          display_name
          week_day
          start_time_string
          court
          secs_start
          match_id
          external {
            pool_bracket_info {
              is_pool
              uuid
            }
          }
        }
      }
    }
    next {
      is_pool
      name
      uuid
    }
    prev {
      is_pool
      name
      uuid
    }
  }
}
    `;

/**
 * __usePoolQuery__
 *
 * To run a query within a React component, call `usePoolQuery` and pass it any options that fit your needs.
 * When your component renders, `usePoolQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePoolQuery({
 *   variables: {
 *      id: // value for 'id'
 *      poolId: // value for 'poolId'
 *   },
 * });
 */
export function usePoolQuery(baseOptions: Apollo.QueryHookOptions<PoolQuery, PoolQueryVariables> & ({ variables: PoolQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PoolQuery, PoolQueryVariables>(PoolDocument, options);
      }
export function usePoolLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PoolQuery, PoolQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PoolQuery, PoolQueryVariables>(PoolDocument, options);
        }
export function usePoolSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<PoolQuery, PoolQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<PoolQuery, PoolQueryVariables>(PoolDocument, options);
        }
export type PoolQueryHookResult = ReturnType<typeof usePoolQuery>;
export type PoolLazyQueryHookResult = ReturnType<typeof usePoolLazyQuery>;
export type PoolSuspenseQueryHookResult = ReturnType<typeof usePoolSuspenseQuery>;
export type PoolQueryResult = Apollo.QueryResult<PoolQuery, PoolQueryVariables>;
export const PoolByTeamIdDocument = gql`
    query PoolByTeamId($id: ID!, $teamId: ID!, $poolId: ID) {
  teamSingle(id: $id, teamId: $teamId) {
    roster_team_id
    master_team_id
    division_id
    team_name
    matches_won
    matches_lost
    manual_club_name
    sets_won
    sets_lost
    organization_code
    points_won
    points_lost
    roster_club_id
    club_name
    state
    bracket_finishes {
      winner {
        team_name
        team_id
        next_match {
          court
          match_id
          week_day
          secs_start
          display_name
          start_time_string
          external {
            pool_bracket_info {
              is_pool
              uuid
            }
          }
        }
        next_ref {
          court
          match_id
          week_day
          secs_start
          display_name
          start_time_string
          external {
            pool_bracket_info {
              is_pool
              uuid
            }
          }
        }
      }
      looser {
        team_name
        team_id
        next_match {
          court
          match_id
          week_day
          secs_start
          display_name
          start_time_string
          external {
            pool_bracket_info {
              is_pool
              uuid
            }
          }
        }
        next_ref {
          court
          match_id
          week_day
          secs_start
          display_name
          start_time_string
          external {
            pool_bracket_info {
              is_pool
              uuid
            }
          }
        }
      }
    }
    pb_info {
      uuid
      name
      is_pool
      pb_finishes {
        team_ids
        teams {
          team_name
          team_id
          next_match {
            display_name
            week_day
            start_time_string
            court
            secs_start
            match_id
            external {
              pool_bracket_info {
                is_pool
                uuid
              }
            }
          }
          next_ref {
            display_name
            week_day
            start_time_string
            court
            secs_start
            match_id
            external {
              pool_bracket_info {
                is_pool
                uuid
              }
            }
          }
        }
      }
    }
    upcoming {
      match_type
      match_id
      unix_finished
      results
      display_name
      date_start_formatted
      date_start
      division_id
      division_short_name
      court_name
      pool_name
      pool_bracket_id
      is_pool
      opponent_team_name
      opponent_organization_code
      pb_name
      round_name
      footnote_play
      footnote_team1
      footnote_team2
      settings {
        SetCount
        PlayAllSets
        WinningPoints
      }
    }
    athletes {
      first
      last
      uniform
      short_position
      gradyear
      height
    }
    staff {
      first
      last
      sort_order
      role_name
    }
    results {
      uuid
      is_pool
      pb_name
      round_name
      sort_priority
      pb_stats {
        name
        rank
        team_id
        sets_pct
        sets_won
        sets_lost
        points_won
        matches_pct
        matches_won
        points_lost
        matches_lost
        points_ratio
      }
      matches {
        date_start
        date_start_formatted
        display_name
        division_id
        division_short_name
        match_id
        match_type
        opponent_organization_code
        opponent_team_id
        opponent_team_name
        pool_bracket_id
        unix_finished
        results {
          set1
          set2
          set3
          set4
          set5
          winner
          team1 {
            heading
            heading_sort
            matches_lost
            matches_pct
            matches_won
            overallSeed
            points_lost
            points_ratio
            points_won
            roster_team_id
            scores
            sets_lost
            sets_pct
            sets_won
            title
          }
          team2 {
            heading
            heading_sort
            matches_lost
            matches_pct
            matches_won
            overallSeed
            points_lost
            points_ratio
            points_won
            roster_team_id
            scores
            sets_lost
            sets_pct
            sets_won
            title
          }
        }
      }
    }
  }
  poolIdByTeamId(id: $id, teamId: $teamId, poolId: $poolId) {
    division_short_name
    uuid
    display_name
    display_name_short
    team_count
    division_short_name
    division_id
    upcoming_matches {
      court_name
      date_start
      display_name
      division_short_name
      footnote_play
      footnote_team1
      footnote_team2
      match_id
      ref_roster_id
      ref_team_code
      ref_team_name
      results
      team1_code
      team1_name
      team1_roster_id
      team2_code
      team2_name
      team2_roster_id
    }
    settings {
      PlayAllSets
      SetCount
      WinningPoints
    }
    results {
      match_id
      team1_name
      team2_name
      ref_team_name
      date_start
      court_name
      results {
        set1
        set2
        set3
        set4
        set5
        team1 {
          scores
          matches_won
        }
        team2 {
          scores
          matches_won
        }
      }
    }
    standings {
      display_name
      pb_stats {
        team_id
        name
        rank
        matches_won
        matches_lost
        sets_won
        sets_lost
        sets_pct
        points_ratio
      }
    }
    pb_finishes {
      team_ids
      teams {
        team_name
        team_id
        next_match {
          display_name
          week_day
          start_time_string
          court
          secs_start
          match_id
          external {
            pool_bracket_info {
              is_pool
              uuid
            }
          }
        }
        next_ref {
          display_name
          week_day
          start_time_string
          court
          secs_start
          match_id
          external {
            pool_bracket_info {
              is_pool
              uuid
            }
          }
        }
      }
    }
    next {
      is_pool
      name
      uuid
    }
    prev {
      is_pool
      name
      uuid
    }
  }
}
    `;

/**
 * __usePoolByTeamIdQuery__
 *
 * To run a query within a React component, call `usePoolByTeamIdQuery` and pass it any options that fit your needs.
 * When your component renders, `usePoolByTeamIdQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePoolByTeamIdQuery({
 *   variables: {
 *      id: // value for 'id'
 *      teamId: // value for 'teamId'
 *      poolId: // value for 'poolId'
 *   },
 * });
 */
export function usePoolByTeamIdQuery(baseOptions: Apollo.QueryHookOptions<PoolByTeamIdQuery, PoolByTeamIdQueryVariables> & ({ variables: PoolByTeamIdQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PoolByTeamIdQuery, PoolByTeamIdQueryVariables>(PoolByTeamIdDocument, options);
      }
export function usePoolByTeamIdLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PoolByTeamIdQuery, PoolByTeamIdQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PoolByTeamIdQuery, PoolByTeamIdQueryVariables>(PoolByTeamIdDocument, options);
        }
export function usePoolByTeamIdSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<PoolByTeamIdQuery, PoolByTeamIdQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<PoolByTeamIdQuery, PoolByTeamIdQueryVariables>(PoolByTeamIdDocument, options);
        }
export type PoolByTeamIdQueryHookResult = ReturnType<typeof usePoolByTeamIdQuery>;
export type PoolByTeamIdLazyQueryHookResult = ReturnType<typeof usePoolByTeamIdLazyQuery>;
export type PoolByTeamIdSuspenseQueryHookResult = ReturnType<typeof usePoolByTeamIdSuspenseQuery>;
export type PoolByTeamIdQueryResult = Apollo.QueryResult<PoolByTeamIdQuery, PoolByTeamIdQueryVariables>;
export const PoolsDocument = gql`
    query Pools($id: ID!, $divisionId: ID!) {
  pools(id: $id, divisionId: $divisionId) {
    uuid
    r_uuid
    r_name
    pb_name
    display_name
    division_short_name
    date_start
    court_start
    is_pool
    settings {
      PlayAllSets
      SetCount
      WinningPoints
    }
    teams {
      sets_pct
      opponent_team_name
      rank
      opponent_team_id
      matches_won
      matches_lost
      sets_won
      sets_lost
      opponent_organization_code
      points_ratio
      info {
        seed_current
      }
    }
  }
}
    `;

/**
 * __usePoolsQuery__
 *
 * To run a query within a React component, call `usePoolsQuery` and pass it any options that fit your needs.
 * When your component renders, `usePoolsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePoolsQuery({
 *   variables: {
 *      id: // value for 'id'
 *      divisionId: // value for 'divisionId'
 *   },
 * });
 */
export function usePoolsQuery(baseOptions: Apollo.QueryHookOptions<PoolsQuery, PoolsQueryVariables> & ({ variables: PoolsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PoolsQuery, PoolsQueryVariables>(PoolsDocument, options);
      }
export function usePoolsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PoolsQuery, PoolsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PoolsQuery, PoolsQueryVariables>(PoolsDocument, options);
        }
export function usePoolsSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<PoolsQuery, PoolsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<PoolsQuery, PoolsQueryVariables>(PoolsDocument, options);
        }
export type PoolsQueryHookResult = ReturnType<typeof usePoolsQuery>;
export type PoolsLazyQueryHookResult = ReturnType<typeof usePoolsLazyQuery>;
export type PoolsSuspenseQueryHookResult = ReturnType<typeof usePoolsSuspenseQuery>;
export type PoolsQueryResult = Apollo.QueryResult<PoolsQuery, PoolsQueryVariables>;
export const CourtsRangeMatchesDocument = gql`
    query CourtsRangeMatches($eswId: ID!, $after: String!, $before: String!, $divisionId: ID, $uniqKey: String!) {
  dayRangeMatches(
    eventKey: $eswId
    after: $after
    before: $before
    divisionId: $divisionId
  ) {
    items {
      team_id
      opponent_id
      ref_team_id
      match_id
      court_id
      is_tb
      match_name
      division_id
      division_name
      secs_start
      secs_end
      secs_finished
      results {
        winner_team {
          scores
          roster_team_id
        }
      }
      external {
        team_display_name
        opponent_display_name
        ref_team_display_name
        pool_bracket_info {
          is_pool
          uuid
        }
        team_pb_seed {
          id
        }
        opponent_pb_seed {
          id
        }
      }
    }
    filter_info {
      is_filtered
    }
  }
  relatedCourts(uniqKey: $uniqKey) {
    uuid
    name
    short_name
  }
}
    `;

/**
 * __useCourtsRangeMatchesQuery__
 *
 * To run a query within a React component, call `useCourtsRangeMatchesQuery` and pass it any options that fit your needs.
 * When your component renders, `useCourtsRangeMatchesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useCourtsRangeMatchesQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      after: // value for 'after'
 *      before: // value for 'before'
 *      divisionId: // value for 'divisionId'
 *      uniqKey: // value for 'uniqKey'
 *   },
 * });
 */
export function useCourtsRangeMatchesQuery(baseOptions: Apollo.QueryHookOptions<CourtsRangeMatchesQuery, CourtsRangeMatchesQueryVariables> & ({ variables: CourtsRangeMatchesQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<CourtsRangeMatchesQuery, CourtsRangeMatchesQueryVariables>(CourtsRangeMatchesDocument, options);
      }
export function useCourtsRangeMatchesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<CourtsRangeMatchesQuery, CourtsRangeMatchesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<CourtsRangeMatchesQuery, CourtsRangeMatchesQueryVariables>(CourtsRangeMatchesDocument, options);
        }
export function useCourtsRangeMatchesSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<CourtsRangeMatchesQuery, CourtsRangeMatchesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<CourtsRangeMatchesQuery, CourtsRangeMatchesQueryVariables>(CourtsRangeMatchesDocument, options);
        }
export type CourtsRangeMatchesQueryHookResult = ReturnType<typeof useCourtsRangeMatchesQuery>;
export type CourtsRangeMatchesLazyQueryHookResult = ReturnType<typeof useCourtsRangeMatchesLazyQuery>;
export type CourtsRangeMatchesSuspenseQueryHookResult = ReturnType<typeof useCourtsRangeMatchesSuspenseQuery>;
export type CourtsRangeMatchesQueryResult = Apollo.QueryResult<CourtsRangeMatchesQuery, CourtsRangeMatchesQueryVariables>;
export const RosterDocument = gql`
    query Roster($id: ID!) {
  roster(id: $id) {
    event_id
    event_name
    state
    club_name
    organization_code
    team_name
    roster_team_id
    roster_athletes {
      id
      first
      last
      short_position
      uniform
      gradyear
    }
    roster_staff {
      id
      first
      last
      role_name
      sort_order
    }
  }
}
    `;

/**
 * __useRosterQuery__
 *
 * To run a query within a React component, call `useRosterQuery` and pass it any options that fit your needs.
 * When your component renders, `useRosterQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useRosterQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useRosterQuery(baseOptions: Apollo.QueryHookOptions<RosterQuery, RosterQueryVariables> & ({ variables: RosterQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<RosterQuery, RosterQueryVariables>(RosterDocument, options);
      }
export function useRosterLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<RosterQuery, RosterQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<RosterQuery, RosterQueryVariables>(RosterDocument, options);
        }
export function useRosterSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<RosterQuery, RosterQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<RosterQuery, RosterQueryVariables>(RosterDocument, options);
        }
export type RosterQueryHookResult = ReturnType<typeof useRosterQuery>;
export type RosterLazyQueryHookResult = ReturnType<typeof useRosterLazyQuery>;
export type RosterSuspenseQueryHookResult = ReturnType<typeof useRosterSuspenseQuery>;
export type RosterQueryResult = Apollo.QueryResult<RosterQuery, RosterQueryVariables>;
export const DivisionRoundsPoolBracketsDocument = gql`
    query DivisionRoundsPoolBrackets($eswId: ID!, $divisionId: ID!) {
  divisionRounds(eventKey: $eswId, divisionId: $divisionId) {
    uuid
    short_name
    name
    sort_priority
    first_match_start
    last_match_start
    pool_brackets {
      ... on Pool {
        uuid
        short_name
        display_name
        is_pool
        sort_priority
        date_start
        settings {
          SetCount
          PlayAllSets
          WinningPoints
        }
        teams {
          team_id
          team_name
          extra {
            show_previously_accepted_bid
            show_accepted_bid
          }
          division_standing {
            seed
          }
          pool_bracket_stat {
            sets_pct
            sets_won
            sets_lost
            matches_won
            matches_lost
            points_ratio
          }
        }
        external {
          courts_short_info {
            uuid
            short_name
          }
        }
      }
      ... on Bracket {
        uuid
        short_name
        display_name
        is_pool
        sort_priority
        date_start
        settings {
          SetCount
          PlayAllSets
          WinningPoints
        }
        teams {
          team_id
          team_name
          extra {
            show_previously_accepted_bid
            show_accepted_bid
          }
          division_standing {
            seed
          }
          pool_bracket_stat {
            sets_pct
            sets_won
            sets_lost
            matches_won
            matches_lost
            points_ratio
          }
        }
        external {
          courts_short_info {
            uuid
            short_name
          }
        }
      }
    }
  }
}
    `;

/**
 * __useDivisionRoundsPoolBracketsQuery__
 *
 * To run a query within a React component, call `useDivisionRoundsPoolBracketsQuery` and pass it any options that fit your needs.
 * When your component renders, `useDivisionRoundsPoolBracketsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useDivisionRoundsPoolBracketsQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      divisionId: // value for 'divisionId'
 *   },
 * });
 */
export function useDivisionRoundsPoolBracketsQuery(baseOptions: Apollo.QueryHookOptions<DivisionRoundsPoolBracketsQuery, DivisionRoundsPoolBracketsQueryVariables> & ({ variables: DivisionRoundsPoolBracketsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<DivisionRoundsPoolBracketsQuery, DivisionRoundsPoolBracketsQueryVariables>(DivisionRoundsPoolBracketsDocument, options);
      }
export function useDivisionRoundsPoolBracketsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<DivisionRoundsPoolBracketsQuery, DivisionRoundsPoolBracketsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<DivisionRoundsPoolBracketsQuery, DivisionRoundsPoolBracketsQueryVariables>(DivisionRoundsPoolBracketsDocument, options);
        }
export function useDivisionRoundsPoolBracketsSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<DivisionRoundsPoolBracketsQuery, DivisionRoundsPoolBracketsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<DivisionRoundsPoolBracketsQuery, DivisionRoundsPoolBracketsQueryVariables>(DivisionRoundsPoolBracketsDocument, options);
        }
export type DivisionRoundsPoolBracketsQueryHookResult = ReturnType<typeof useDivisionRoundsPoolBracketsQuery>;
export type DivisionRoundsPoolBracketsLazyQueryHookResult = ReturnType<typeof useDivisionRoundsPoolBracketsLazyQuery>;
export type DivisionRoundsPoolBracketsSuspenseQueryHookResult = ReturnType<typeof useDivisionRoundsPoolBracketsSuspenseQuery>;
export type DivisionRoundsPoolBracketsQueryResult = Apollo.QueryResult<DivisionRoundsPoolBracketsQuery, DivisionRoundsPoolBracketsQueryVariables>;
export const PaginatedStaffDocument = gql`
    query PaginatedStaff($eswId: ID!, $page: Float!, $pageSize: Float!, $search: String) {
  paginatedStaff(
    eventKey: $eswId
    page: $page
    pageSize: $pageSize
    search: $search
  ) {
    items {
      staff_id
      first
      last
      club_name
      team_id
      team_name
      role_name
      organization_code
      state
    }
    page_info {
      page
      page_size
      page_count
      item_count
    }
  }
}
    `;

/**
 * __usePaginatedStaffQuery__
 *
 * To run a query within a React component, call `usePaginatedStaffQuery` and pass it any options that fit your needs.
 * When your component renders, `usePaginatedStaffQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePaginatedStaffQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      page: // value for 'page'
 *      pageSize: // value for 'pageSize'
 *      search: // value for 'search'
 *   },
 * });
 */
export function usePaginatedStaffQuery(baseOptions: Apollo.QueryHookOptions<PaginatedStaffQuery, PaginatedStaffQueryVariables> & ({ variables: PaginatedStaffQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PaginatedStaffQuery, PaginatedStaffQueryVariables>(PaginatedStaffDocument, options);
      }
export function usePaginatedStaffLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PaginatedStaffQuery, PaginatedStaffQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PaginatedStaffQuery, PaginatedStaffQueryVariables>(PaginatedStaffDocument, options);
        }
export function usePaginatedStaffSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<PaginatedStaffQuery, PaginatedStaffQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<PaginatedStaffQuery, PaginatedStaffQueryVariables>(PaginatedStaffDocument, options);
        }
export type PaginatedStaffQueryHookResult = ReturnType<typeof usePaginatedStaffQuery>;
export type PaginatedStaffLazyQueryHookResult = ReturnType<typeof usePaginatedStaffLazyQuery>;
export type PaginatedStaffSuspenseQueryHookResult = ReturnType<typeof usePaginatedStaffSuspenseQuery>;
export type PaginatedStaffQueryResult = Apollo.QueryResult<PaginatedStaffQuery, PaginatedStaffQueryVariables>;
export const TeamSingleDocument = gql`
    query TeamSingle($id: ID!, $teamId: ID!) {
  teamSingle(id: $id, teamId: $teamId) {
    roster_team_id
    master_team_id
    division_id
    team_name
    matches_won
    matches_lost
    manual_club_name
    sets_won
    sets_lost
    organization_code
    points_won
    points_lost
    roster_club_id
    club_name
    state
    upcoming {
      match_type
      match_id
      unix_finished
      results
      display_name
      date_start_formatted
      date_start
      division_id
      division_short_name
      court_name
      pool_name
      pool_bracket_id
      is_pool
      opponent_team_name
      opponent_organization_code
      pb_name
      round_name
      footnote_play
      footnote_team1
      footnote_team2
      settings {
        SetCount
        PlayAllSets
        WinningPoints
      }
    }
    athletes {
      first
      last
      uniform
      short_position
      gradyear
      height
    }
    staff {
      first
      last
      sort_order
      role_name
    }
    results {
      uuid
      is_pool
      pb_name
      round_name
      sort_priority
      pb_stats {
        name
        rank
        team_id
        sets_pct
        sets_won
        sets_lost
        points_won
        matches_pct
        matches_won
        points_lost
        matches_lost
        points_ratio
      }
      matches {
        date_start
        date_start_formatted
        display_name
        division_id
        division_short_name
        match_id
        match_type
        opponent_organization_code
        opponent_team_id
        opponent_team_name
        pool_bracket_id
        unix_finished
        results {
          set1
          set2
          set3
          set4
          set5
          winner
          team1 {
            heading
            heading_sort
            matches_lost
            matches_pct
            matches_won
            overallSeed
            points_lost
            points_ratio
            points_won
            roster_team_id
            scores
            sets_lost
            sets_pct
            sets_won
            title
          }
          team2 {
            heading
            heading_sort
            matches_lost
            matches_pct
            matches_won
            overallSeed
            points_lost
            points_ratio
            points_won
            roster_team_id
            scores
            sets_lost
            sets_pct
            sets_won
            title
          }
        }
      }
    }
  }
}
    `;

/**
 * __useTeamSingleQuery__
 *
 * To run a query within a React component, call `useTeamSingleQuery` and pass it any options that fit your needs.
 * When your component renders, `useTeamSingleQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useTeamSingleQuery({
 *   variables: {
 *      id: // value for 'id'
 *      teamId: // value for 'teamId'
 *   },
 * });
 */
export function useTeamSingleQuery(baseOptions: Apollo.QueryHookOptions<TeamSingleQuery, TeamSingleQueryVariables> & ({ variables: TeamSingleQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<TeamSingleQuery, TeamSingleQueryVariables>(TeamSingleDocument, options);
      }
export function useTeamSingleLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<TeamSingleQuery, TeamSingleQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<TeamSingleQuery, TeamSingleQueryVariables>(TeamSingleDocument, options);
        }
export function useTeamSingleSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<TeamSingleQuery, TeamSingleQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<TeamSingleQuery, TeamSingleQueryVariables>(TeamSingleDocument, options);
        }
export type TeamSingleQueryHookResult = ReturnType<typeof useTeamSingleQuery>;
export type TeamSingleLazyQueryHookResult = ReturnType<typeof useTeamSingleLazyQuery>;
export type TeamSingleSuspenseQueryHookResult = ReturnType<typeof useTeamSingleSuspenseQuery>;
export type TeamSingleQueryResult = Apollo.QueryResult<TeamSingleQuery, TeamSingleQueryVariables>;
export const DivisionDetailsDocument = gql`
    query DivisionDetails($id: ID!, $divisionIds: [ID!]!) {
  divisionDetails(id: $id, divisionIds: $divisionIds) {
    standing {
      heading_group
      teams {
        rank
        roster_team_id
        division_short_name
        division_id
        seed_current
        team_name
        organization_code
        points_ratio
        matches_won
        matches_lost
        sets_won
        sets_lost
        sets_pct
        info {
          points
        }
      }
    }
  }
}
    `;

/**
 * __useDivisionDetailsQuery__
 *
 * To run a query within a React component, call `useDivisionDetailsQuery` and pass it any options that fit your needs.
 * When your component renders, `useDivisionDetailsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useDivisionDetailsQuery({
 *   variables: {
 *      id: // value for 'id'
 *      divisionIds: // value for 'divisionIds'
 *   },
 * });
 */
export function useDivisionDetailsQuery(baseOptions: Apollo.QueryHookOptions<DivisionDetailsQuery, DivisionDetailsQueryVariables> & ({ variables: DivisionDetailsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<DivisionDetailsQuery, DivisionDetailsQueryVariables>(DivisionDetailsDocument, options);
      }
export function useDivisionDetailsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<DivisionDetailsQuery, DivisionDetailsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<DivisionDetailsQuery, DivisionDetailsQueryVariables>(DivisionDetailsDocument, options);
        }
export function useDivisionDetailsSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<DivisionDetailsQuery, DivisionDetailsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<DivisionDetailsQuery, DivisionDetailsQueryVariables>(DivisionDetailsDocument, options);
        }
export type DivisionDetailsQueryHookResult = ReturnType<typeof useDivisionDetailsQuery>;
export type DivisionDetailsLazyQueryHookResult = ReturnType<typeof useDivisionDetailsLazyQuery>;
export type DivisionDetailsSuspenseQueryHookResult = ReturnType<typeof useDivisionDetailsSuspenseQuery>;
export type DivisionDetailsQueryResult = Apollo.QueryResult<DivisionDetailsQuery, DivisionDetailsQueryVariables>;
export const DivisionsQualifiedTeamsDocument = gql`
    query DivisionsQualifiedTeams($eswId: ID!) {
  divisions(eventKey: $eswId) {
    division_id
    name
    qualified_teams {
      team_id
      team_name
      extra {
        prev_qual_age
        prev_qual_division
        earned_at
      }
    }
  }
}
    `;

/**
 * __useDivisionsQualifiedTeamsQuery__
 *
 * To run a query within a React component, call `useDivisionsQualifiedTeamsQuery` and pass it any options that fit your needs.
 * When your component renders, `useDivisionsQualifiedTeamsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useDivisionsQualifiedTeamsQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *   },
 * });
 */
export function useDivisionsQualifiedTeamsQuery(baseOptions: Apollo.QueryHookOptions<DivisionsQualifiedTeamsQuery, DivisionsQualifiedTeamsQueryVariables> & ({ variables: DivisionsQualifiedTeamsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<DivisionsQualifiedTeamsQuery, DivisionsQualifiedTeamsQueryVariables>(DivisionsQualifiedTeamsDocument, options);
      }
export function useDivisionsQualifiedTeamsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<DivisionsQualifiedTeamsQuery, DivisionsQualifiedTeamsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<DivisionsQualifiedTeamsQuery, DivisionsQualifiedTeamsQueryVariables>(DivisionsQualifiedTeamsDocument, options);
        }
export function useDivisionsQualifiedTeamsSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<DivisionsQualifiedTeamsQuery, DivisionsQualifiedTeamsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<DivisionsQualifiedTeamsQuery, DivisionsQualifiedTeamsQueryVariables>(DivisionsQualifiedTeamsDocument, options);
        }
export type DivisionsQualifiedTeamsQueryHookResult = ReturnType<typeof useDivisionsQualifiedTeamsQuery>;
export type DivisionsQualifiedTeamsLazyQueryHookResult = ReturnType<typeof useDivisionsQualifiedTeamsLazyQuery>;
export type DivisionsQualifiedTeamsSuspenseQueryHookResult = ReturnType<typeof useDivisionsQualifiedTeamsSuspenseQuery>;
export type DivisionsQualifiedTeamsQueryResult = Apollo.QueryResult<DivisionsQualifiedTeamsQuery, DivisionsQualifiedTeamsQueryVariables>;
export const FavoriteTeamsStandingDocument = gql`
    query FavoriteTeamsStanding($eswId: ID!, $teamsIds: [ID!]!, $search: String) {
  favoriteTeams(eventKey: $eswId, teamsIds: $teamsIds, search: $search) {
    club_id
    team_id
    team_name
    team_code
    division_id
    division_name
    next_match {
      secs_start
      external {
        pool_bracket_info {
          is_pool
          uuid
        }
        court_info {
          short_name
        }
      }
    }
    division_standing {
      matches_won
      matches_lost
      sets_won
      sets_lost
      sets_pct
      points_ratio
      rank
      seed
      heading
      heading_priority
    }
  }
}
    `;

/**
 * __useFavoriteTeamsStandingQuery__
 *
 * To run a query within a React component, call `useFavoriteTeamsStandingQuery` and pass it any options that fit your needs.
 * When your component renders, `useFavoriteTeamsStandingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useFavoriteTeamsStandingQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      teamsIds: // value for 'teamsIds'
 *      search: // value for 'search'
 *   },
 * });
 */
export function useFavoriteTeamsStandingQuery(baseOptions: Apollo.QueryHookOptions<FavoriteTeamsStandingQuery, FavoriteTeamsStandingQueryVariables> & ({ variables: FavoriteTeamsStandingQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<FavoriteTeamsStandingQuery, FavoriteTeamsStandingQueryVariables>(FavoriteTeamsStandingDocument, options);
      }
export function useFavoriteTeamsStandingLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<FavoriteTeamsStandingQuery, FavoriteTeamsStandingQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<FavoriteTeamsStandingQuery, FavoriteTeamsStandingQueryVariables>(FavoriteTeamsStandingDocument, options);
        }
export function useFavoriteTeamsStandingSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<FavoriteTeamsStandingQuery, FavoriteTeamsStandingQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<FavoriteTeamsStandingQuery, FavoriteTeamsStandingQueryVariables>(FavoriteTeamsStandingDocument, options);
        }
export type FavoriteTeamsStandingQueryHookResult = ReturnType<typeof useFavoriteTeamsStandingQuery>;
export type FavoriteTeamsStandingLazyQueryHookResult = ReturnType<typeof useFavoriteTeamsStandingLazyQuery>;
export type FavoriteTeamsStandingSuspenseQueryHookResult = ReturnType<typeof useFavoriteTeamsStandingSuspenseQuery>;
export type FavoriteTeamsStandingQueryResult = Apollo.QueryResult<FavoriteTeamsStandingQuery, FavoriteTeamsStandingQueryVariables>;
export const PaginatedDivisionTeamsDocument = gql`
    query PaginatedDivisionTeams($eswId: ID!, $divisionId: ID!, $page: Float!, $pageSize: Float!, $search: String) {
  paginatedDivisionTeams(
    eventKey: $eswId
    divisionId: $divisionId
    page: $page
    pageSize: $pageSize
    search: $search
  ) {
    items {
      team_id
      team_name
      division_id
      next_match {
        secs_start
        external {
          opponent_display_name
          court_info {
            short_name
          }
          pool_bracket_info {
            is_pool
            uuid
          }
        }
      }
      division_standing {
        matches_won
        matches_lost
        sets_won
        sets_lost
        rank
      }
    }
    page_info {
      page
      page_size
      page_count
      item_count
    }
  }
}
    `;

/**
 * __usePaginatedDivisionTeamsQuery__
 *
 * To run a query within a React component, call `usePaginatedDivisionTeamsQuery` and pass it any options that fit your needs.
 * When your component renders, `usePaginatedDivisionTeamsQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = usePaginatedDivisionTeamsQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      divisionId: // value for 'divisionId'
 *      page: // value for 'page'
 *      pageSize: // value for 'pageSize'
 *      search: // value for 'search'
 *   },
 * });
 */
export function usePaginatedDivisionTeamsQuery(baseOptions: Apollo.QueryHookOptions<PaginatedDivisionTeamsQuery, PaginatedDivisionTeamsQueryVariables> & ({ variables: PaginatedDivisionTeamsQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<PaginatedDivisionTeamsQuery, PaginatedDivisionTeamsQueryVariables>(PaginatedDivisionTeamsDocument, options);
      }
export function usePaginatedDivisionTeamsLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<PaginatedDivisionTeamsQuery, PaginatedDivisionTeamsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<PaginatedDivisionTeamsQuery, PaginatedDivisionTeamsQueryVariables>(PaginatedDivisionTeamsDocument, options);
        }
export function usePaginatedDivisionTeamsSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<PaginatedDivisionTeamsQuery, PaginatedDivisionTeamsQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<PaginatedDivisionTeamsQuery, PaginatedDivisionTeamsQueryVariables>(PaginatedDivisionTeamsDocument, options);
        }
export type PaginatedDivisionTeamsQueryHookResult = ReturnType<typeof usePaginatedDivisionTeamsQuery>;
export type PaginatedDivisionTeamsLazyQueryHookResult = ReturnType<typeof usePaginatedDivisionTeamsLazyQuery>;
export type PaginatedDivisionTeamsSuspenseQueryHookResult = ReturnType<typeof usePaginatedDivisionTeamsSuspenseQuery>;
export type PaginatedDivisionTeamsQueryResult = Apollo.QueryResult<PaginatedDivisionTeamsQuery, PaginatedDivisionTeamsQueryVariables>;
export const DivisionTeamsStandingDocument = gql`
    query DivisionTeamsStanding($eswId: ID!, $divisionId: ID!) {
  divisionTeamsStanding(eventKey: $eswId, divisionId: $divisionId) {
    team_id
    team_name
    team_code
    extra {
      show_previously_accepted_bid
      show_accepted_bid
    }
    division_standing {
      matches_won
      matches_lost
      sets_won
      sets_lost
      sets_pct
      points_ratio
      points
      rank
      seed
      heading
    }
  }
}
    `;

/**
 * __useDivisionTeamsStandingQuery__
 *
 * To run a query within a React component, call `useDivisionTeamsStandingQuery` and pass it any options that fit your needs.
 * When your component renders, `useDivisionTeamsStandingQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useDivisionTeamsStandingQuery({
 *   variables: {
 *      eswId: // value for 'eswId'
 *      divisionId: // value for 'divisionId'
 *   },
 * });
 */
export function useDivisionTeamsStandingQuery(baseOptions: Apollo.QueryHookOptions<DivisionTeamsStandingQuery, DivisionTeamsStandingQueryVariables> & ({ variables: DivisionTeamsStandingQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<DivisionTeamsStandingQuery, DivisionTeamsStandingQueryVariables>(DivisionTeamsStandingDocument, options);
      }
export function useDivisionTeamsStandingLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<DivisionTeamsStandingQuery, DivisionTeamsStandingQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<DivisionTeamsStandingQuery, DivisionTeamsStandingQueryVariables>(DivisionTeamsStandingDocument, options);
        }
export function useDivisionTeamsStandingSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<DivisionTeamsStandingQuery, DivisionTeamsStandingQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<DivisionTeamsStandingQuery, DivisionTeamsStandingQueryVariables>(DivisionTeamsStandingDocument, options);
        }
export type DivisionTeamsStandingQueryHookResult = ReturnType<typeof useDivisionTeamsStandingQuery>;
export type DivisionTeamsStandingLazyQueryHookResult = ReturnType<typeof useDivisionTeamsStandingLazyQuery>;
export type DivisionTeamsStandingSuspenseQueryHookResult = ReturnType<typeof useDivisionTeamsStandingSuspenseQuery>;
export type DivisionTeamsStandingQueryResult = Apollo.QueryResult<DivisionTeamsStandingQuery, DivisionTeamsStandingQueryVariables>;
export const UpcomingMatchesDocument = gql`
    query UpcomingMatches($id: ID!) {
  upcomingMatches(id: $id) {
    division_id
    division_name
    club_id
    club_name
    club_state
    club_code
    team_code
    team_id
    team_name
    opponent_id
    opponent_name
    secs_start
    secs_finished
    court_name
    matches_won
    matches_lost
    sets_won
    sets_lost
    sets_pct
    seed
  }
}
    `;

/**
 * __useUpcomingMatchesQuery__
 *
 * To run a query within a React component, call `useUpcomingMatchesQuery` and pass it any options that fit your needs.
 * When your component renders, `useUpcomingMatchesQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useUpcomingMatchesQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useUpcomingMatchesQuery(baseOptions: Apollo.QueryHookOptions<UpcomingMatchesQuery, UpcomingMatchesQueryVariables> & ({ variables: UpcomingMatchesQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return Apollo.useQuery<UpcomingMatchesQuery, UpcomingMatchesQueryVariables>(UpcomingMatchesDocument, options);
      }
export function useUpcomingMatchesLazyQuery(baseOptions?: Apollo.LazyQueryHookOptions<UpcomingMatchesQuery, UpcomingMatchesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useLazyQuery<UpcomingMatchesQuery, UpcomingMatchesQueryVariables>(UpcomingMatchesDocument, options);
        }
export function useUpcomingMatchesSuspenseQuery(baseOptions?: Apollo.SuspenseQueryHookOptions<UpcomingMatchesQuery, UpcomingMatchesQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return Apollo.useSuspenseQuery<UpcomingMatchesQuery, UpcomingMatchesQueryVariables>(UpcomingMatchesDocument, options);
        }
export type UpcomingMatchesQueryHookResult = ReturnType<typeof useUpcomingMatchesQuery>;
export type UpcomingMatchesLazyQueryHookResult = ReturnType<typeof useUpcomingMatchesLazyQuery>;
export type UpcomingMatchesSuspenseQueryHookResult = ReturnType<typeof useUpcomingMatchesSuspenseQuery>;
export type UpcomingMatchesQueryResult = Apollo.QueryResult<UpcomingMatchesQuery, UpcomingMatchesQueryVariables>;