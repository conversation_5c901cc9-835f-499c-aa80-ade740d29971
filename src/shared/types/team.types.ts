import {
	DivisionTeamsStandingQuery,
	FavoriteTeamsStandingQuery,
	PaginatedDivisionTeamsQuery,
} from '@generated/graphql';

export type FavoriteTeam = FavoriteTeamsStandingQuery['favoriteTeams'][number];

export type PaginatedDivisionTeam =
	PaginatedDivisionTeamsQuery['paginatedDivisionTeams']['items'][number];

export type DivisionTeamStanding = DivisionTeamsStandingQuery['divisionTeamsStanding'][number];
