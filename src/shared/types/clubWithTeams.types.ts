export type ClubsWithTeamsTeam = {
	club_id: number;
	team_code: string;
	club_name: string;
	club_state: string;
	club_code: string;
	division_id: number;
	division_name: string;
	team_id: number;
	team_name: string;
	opponent_id: number;
	opponent_name: string;
	secs_start: number;
	secs_finished: number;
	court_name: string;
	matches_won: number;
	matches_lost: number;
	sets_won: number;
	sets_lost: number;
	sets_pct: number;
	seed: number;
};

export type ClubsWithTeamsObject = Record<
	number,
	{
		club_id: number;
		club_name: string;
		club_state: string;
		club_code: string;
		teams: ClubsWithTeamsTeam[];
	}
>;
export type ClubsWithTeamsArray = {
	club_id: number;
	club_name: string;
	club_state: string;
	club_code: string;
	teams: ClubsWithTeamsTeam[];
};
