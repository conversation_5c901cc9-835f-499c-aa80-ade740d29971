import { WatchQueryFetchPolicy } from '@apollo/client';
import { AnchorModal } from '@components/AnchorModal';
import { useCallback, useState } from 'react';
import { StringParam, useQueryParams } from 'use-query-params';

import { DivisionPool, usePoolByTeamIdLazyQuery, usePoolLazyQuery } from '@/generated/graphql';

import { useEventDetails } from './useEventDetails';

export const usePoolModal = (fetchPolicy?: WatchQueryFetchPolicy) => {
	const { eswId } = useEventDetails();
	const [modalTeamId, setModalTeamId] = useState<string | null>(null);
	const [getPoolByTeamId, { data, loading }] = usePoolByTeamIdLazyQuery();

	const [getPoolData, { data: poolData }] = usePoolLazyQuery();

	const [, setQueryParams] = useQueryParams({
		divisionId: StringParam,
		modalTeam: StringParam,
	});

	const openPoolModal = useCallback(
		(teamId: string, poolId?: string) => {
			if (Number(teamId) === 0 && poolId) {
				getPoolData({
					variables: {
						id: eswId,
						poolId,
					},
					onCompleted: () => {
						setModalTeamId(teamId);
					},
				});
				return;
			}

			getPoolByTeamId({
				variables: {
					id: eswId,
					teamId,
					poolId,
				},
				fetchPolicy,
				onCompleted: () => {
					setModalTeamId(teamId);
				},
			});
		},
		[getPoolByTeamId, eswId, fetchPolicy, getPoolData],
	);

	let poolModalElement;

	if (modalTeamId && poolData) {
		poolModalElement = (
			<AnchorModal
				key={modalTeamId}
				isPool={true}
				teamPoolData={{ ...poolData, teamSingle: {} }}
				close={() => {
					setModalTeamId(null);
					setQueryParams({ divisionId: null, modalTeam: null });
				}}
				poolOfTeam={poolData?.pool as DivisionPool}
			/>
		);
	}

	if (modalTeamId && data) {
		poolModalElement = (
			<AnchorModal
				key={modalTeamId}
				isPool={true}
				teamPoolData={data}
				close={() => {
					setModalTeamId(null);
					setQueryParams({ divisionId: null, modalTeam: null });
				}}
				poolOfTeam={data.poolIdByTeamId as DivisionPool}
			/>
		);
	}

	return {
		poolModalElement,
		poolDataLoading: loading,
		openPoolModal,
	};
};
