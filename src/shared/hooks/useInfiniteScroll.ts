import { useEffect, useState } from 'react';

const SCROLL_DISTANCE = 450;

type PropsT<T> = {
	list: T[];
	size?: number;
};

export const useInfiniteScroll = <T>({ list, size = 20 }: PropsT<T>) => {
	const [data, setData] = useState<T[]>([]);
	const [pageSize] = useState(size);
	const [currentPage, setCurrentPage] = useState(1);
	const handleScroll = () => {
		if (
			window.innerHeight + document.documentElement.scrollTop + SCROLL_DISTANCE <
			document.documentElement.offsetHeight
		) {
			return;
		}

		setCurrentPage((prevPage) => prevPage + 1);
	};

	useEffect(() => {
		window.addEventListener('scroll', handleScroll);
		return () => window.removeEventListener('scroll', handleScroll);
	}, []);

	useEffect(() => {
		setData(list.slice(0, pageSize * currentPage));
	}, [list, currentPage, pageSize]);

	return {
		data,
	};
};
