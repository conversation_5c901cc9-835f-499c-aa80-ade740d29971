import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { useCurrentSize } from './useCurrentSize';

type PropsT = {
	page:
		| 'favorites'
		| 'favoritesTabs'
		| 'clubsAndTeams'
		| 'roster'
		| 'rosterTabs'
		| 'divisions'
		| 'courtGrid'
		| 'allEvents'
		| 'qualified'
		| 'about'
		| 'division';
};
const OFFSETS = {
	about: 40,
	favorites: 10,
	qualified: 10,
	favoritesTabs: 10,
	clubsAndTeams: 10,
	roster: 10,
	division: 10,
	rosterTabs: 10,
	divisions: 10,
	courtGrid: 10,
	allEvents: 10,
} as const;

export const useDesktopSticky = ({ page }: PropsT) => {
	const { breakPont } = useCurrentSize();
	const [isDesktopFixed, setIsDesktopFixed] = useState(false);
	const location = useLocation();
	useEffect(() => {
		const handleScroll = () => {
			const newScrollTop = window.scrollY || document.documentElement.scrollTop;
			const bodyHeight = document.body.clientHeight;
			const screenHeight = window.innerHeight;
			if (bodyHeight - screenHeight < 305 && location.pathname.includes('about')) {
				//* Prevent scroll for page that have small height
				//* 305px it's height of all fixed elements like header and searchbar
				return;
			}
			if (breakPont !== 'small') {
				if (newScrollTop >= OFFSETS[page]) {
					setIsDesktopFixed(true);
				} else {
					setIsDesktopFixed(false);
				}
			}
		};

		window.addEventListener('scroll', handleScroll);

		return () => {
			window.removeEventListener('scroll', handleScroll);
		};
	}, [breakPont, location.pathname, page]);

	return { isDesktopFixed };
};
