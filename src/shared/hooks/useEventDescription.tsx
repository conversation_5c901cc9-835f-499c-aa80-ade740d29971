import { eventSignal } from '@/signals/eventSignal';
import calendarIcon from '@assets/calendar-small-icon.svg';
import pointIcon from '@assets/point-icon.svg';
import * as DateFns from 'date-fns';

import { useCurrentSize } from './useCurrentSize';

type RetType = () => JSX.Element;

export const FORMAT = 'MMM d';

export const useEventDescription = (): RetType => {
	const event = eventSignal.value;
	const { breakPont } = useCurrentSize();

	if (!event?.days?.length) return () => <></>;

	const { days } = event;
	const startDate = DateFns.parseISO(days?.at(0) || '');

	const formattedStartDate = DateFns.format(startDate, FORMAT);
	const finishedDate = DateFns.parseISO(days?.[days.length - 1] || '');

	const startMonth = DateFns.getMonth(startDate);
	const endMonth = DateFns.getMonth(finishedDate);
	const formattedFinishedDate = DateFns.format(
		finishedDate,
		`${startMonth === endMonth ? 'd' : FORMAT}, yyyy`,
	);

	if (startDate.getDate() === finishedDate.getDate()) {
		return () => (
			<>
				{breakPont !== 'small' && <img src={calendarIcon} alt="" />} {formattedStartDate}
				{breakPont === 'small' && ','}
				{breakPont !== 'small' && <img src={pointIcon} alt="" />} {event?.city},{event?.state}
			</>
		);
	}

	return () => (
		<>
			{breakPont !== 'small' && <img src={calendarIcon} alt="" />} {formattedStartDate} -{' '}
			{formattedFinishedDate}
			{breakPont === 'small' && ','}
			{breakPont !== 'small' && <img src={pointIcon} alt="" />} {event?.city},{event?.state}
		</>
	);
};
