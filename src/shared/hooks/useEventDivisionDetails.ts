import { EventDivisionDetails, useEventDetails } from '@/shared/hooks/useEventDetails';
import { useMemo } from 'react';

export type { EventDivisionDetails };

export const useEventDivisionDetails = (divisionId?: string | null) => {
	const { divisions, loading } = useEventDetails();

	const division = useMemo<EventDivisionDetails | null>(() => {
		if (!divisionId || loading || !divisions) return null;
		return divisions.find((division) => division.division_id === divisionId) ?? null;
	}, [divisionId, divisions, loading]);

	return { division, loading };
};
