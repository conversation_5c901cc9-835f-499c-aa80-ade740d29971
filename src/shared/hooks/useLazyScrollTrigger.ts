import { useCallback, useContext, useEffect, useRef } from 'react';

import { LazyScrollTriggerContext } from '../contexts/lazyScrollTrigger.context';

const SCROLL_DISTANCE = 450;

const getScrollMetrics = (elementRef: React.RefObject<HTMLElement> | null) => {
	const scrollTop = elementRef?.current ? elementRef.current.scrollTop : window.scrollY;
	const clientHeight = elementRef?.current ? elementRef.current.clientHeight : window.innerHeight;
	const scrollHeight = elementRef?.current
		? elementRef.current.scrollHeight
		: document.documentElement.offsetHeight;

	return { scrollTop, clientHeight, scrollHeight };
};

export const useLazyScrollTrigger = (
	onBottomReach: () => void,
	elementRef: React.RefObject<HTMLElement> | null,
	debounceTimeout: number = 100,
) => {
	const { isDisabled } = useContext(LazyScrollTriggerContext);
	const debounceTimeoutRef = useRef<number | null>(null);

	useEffect(() => {
		return () => {
			debounceTimeoutRef.current && clearTimeout(debounceTimeoutRef.current);
		};
	}, []);

	const onScroll = useCallback(() => {
		if (debounceTimeoutRef.current) return;
		const { scrollTop, clientHeight, scrollHeight } = getScrollMetrics(elementRef);
		if (scrollTop + clientHeight + SCROLL_DISTANCE < scrollHeight) return;

		debounceTimeoutRef.current = window.setTimeout(() => {
			debounceTimeoutRef.current = null;
			if (getScrollMetrics(elementRef).scrollHeight > scrollHeight) {
				onScroll();
			}
		}, debounceTimeout);

		onBottomReach();
	}, [elementRef, debounceTimeout, onBottomReach]);

	useEffect(() => {
		if (isDisabled) return;
		const target = elementRef?.current || window;
		target.addEventListener('scroll', onScroll);
		return () => target.removeEventListener('scroll', onScroll);
	}, [elementRef, onScroll, isDisabled]);
};
