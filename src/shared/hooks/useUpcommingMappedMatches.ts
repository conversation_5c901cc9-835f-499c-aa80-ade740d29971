import { uniqBy } from 'lodash';
import { useState } from 'react';

import { useUpcomingMatchesQuery } from '@/generated/graphql';

import { ClubsWithTeamsArray } from '../types/clubWithTeams.types';

type PropsT = {
	eventId: string;
	divisionId?: number;
};
export const useUpcomingMappedMatches = ({ eventId, divisionId }: PropsT) => {
	const [upcomingMatches, setUpcomingMatches] = useState<ClubsWithTeamsArray[]>([]);
	const { loading } = useUpcomingMatchesQuery({
		variables: {
			id: eventId,
		},
		onCompleted: (data) => {
			const clubsMap = new Map();

			data.upcomingMatches?.forEach((match) => {
				if (!match.club_id) return;

				let club = clubsMap.get(match.club_id);
				if (!club) {
					club = {
						club_id: match.club_id,
						club_name: match.club_name!,
						club_state: match.club_state!,
						club_code: match.club_code!,
						teams: [],
					};
					clubsMap.set(match.club_id, club);
				}

				club.teams.push({
					club_id: match.club_id!,
					club_name: match.club_name!,
					club_state: match.club_state!,
					club_code: match.club_code!,
					division_id: match.division_id!,
					division_name: match.division_name!,
					team_code: match.team_code,
					team_id: match.team_id,
					team_name: match.team_name,
					opponent_id: match.opponent_id,
					opponent_name: match.opponent_name,
					secs_start: match.secs_start,
					secs_finished: match.secs_finished,
					court_name: match.court_name,
					matches_won: match.matches_won,
					matches_lost: match.matches_lost,
					sets_won: match.sets_won,
					sets_lost: match.sets_lost,
					sets_pct: match.sets_pct,
					seed: match.seed,
				});
			});

			const clubsWithTeams = Array.from(clubsMap.values());
			setUpcomingMatches(clubsWithTeams);
		},
	});

	const getDivisionTeams = (divisionId?: number) => {
		return uniqBy(
			upcomingMatches
				.map((match) => match.teams)
				.flat()
				.filter((team) => team.division_id === divisionId),
			'team_id',
		);
	};

	return {
		loading,
		upcomingMatches,
		divisionTeams: getDivisionTeams(divisionId),
	};
};
