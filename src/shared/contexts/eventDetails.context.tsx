import { EventDetailsQuery } from '@generated/graphql.tsx';
import { createContext } from 'react';

export type EventDetails = EventDetailsQuery['event'];
export type EventDivisionDetails = EventDetailsQuery['divisions'][number];
export type EventDetailsContextValue = {
	eswId: string;
	event: EventDetails | null;
	divisions: EventDivisionDetails[] | null;
	loading: boolean;
};

export const EventDetailsContext = createContext<EventDetailsContextValue>(null!);
