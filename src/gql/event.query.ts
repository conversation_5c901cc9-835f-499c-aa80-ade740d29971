import { gql } from '@apollo/client';

export const GET_EVENT = gql`
	query Event($id: ID!) {
		event(id: $id) {
			event_id
			hide_seeds
			name
			long_name
			days
			city
			state
			event_notes
			address
			is_with_prev_qual
			tickets_published
			is_require_recipient_name_for_each_ticket
			tickets_code
			locations {
				location_name
			}
			divisions {
				gender
				name
				event_id
				short_name
				teams_count
				division_id
			}
			clubs {
				club_name
				teams_count
				roster_club_id
				state
				club_code
			}
			athletes {
				age
				gender
				first
				last
				team_organization_code
				team_name
				jersey
				club_name
				division_id
				roster_team_id
			}
			teams {
				division_name
				division_short_name
				club_name
				# club_state
				team_name
				organization_code
				roster_team_id
				roster_club_id
				division_id
				gender
			}
			teams_settings {
				sort_by
			}
		}
	}
`;

export const GET_EVENT_FOR_DIVISIONS_PAGE = gql`
	query EventForDivisionsPage($id: ID!) {
		event(id: $id) {
			event_id
			hide_seeds
			name
			long_name
			days
			city
			state
			event_notes
			address
			is_with_prev_qual
			tickets_published
			is_require_recipient_name_for_each_ticket
			tickets_code
			locations {
				location_name
			}
			divisions {
				gender
				name
				event_id
				short_name
				teams_count
				division_id
			}
			clubs {
				roster_club_id
			}
			athletes {
				first
				last
			}
			teams {
				roster_team_id
			}
			teams_settings {
				sort_by
			}
		}
	}
`;

export const GET_DAYS_OF_EVENT = gql`
	query DaysOfEvent($id: ID!) {
		event(id: $id) {
			days
		}
	}
`;
