import { gql } from '@apollo/client';

export const GET_EVENT_GET_EVENT_WITH_ROSTER_GET_UPCOMING_MATCHES = gql`
	query EventWithRosterAndUpcomingMatches($id: ID!) {
		upcomingMatches(id: $id) {
			division_id
			division_name
			club_id
			club_name
			club_state
			club_code
			team_code
			team_id
			team_name
			opponent_id
			opponent_name
			secs_start
			secs_finished
			court_name
			matches_won
			matches_lost
			sets_won
			sets_lost
			sets_pct
			seed
		}
		roster(id: $id) {
			event_id
			event_name
			state
			club_name
			organization_code
			team_name
			roster_team_id
			roster_athletes {
				id
				first
				last
				short_position
				uniform
				gradyear
			}
			roster_staff {
				id
				first
				last
				role_name
				sort_order
			}
		}
		event(id: $id) {
			event_id
			hide_seeds
			name
			long_name
			days
			city
			state
			event_notes
			address
			is_with_prev_qual
			tickets_published
			tickets_code
			is_require_recipient_name_for_each_ticket
			locations {
				location_name
			}
			divisions {
				division_id
			}
			clubs {
				roster_club_id
			}
			athletes {
				first
				last
			}
			teams {
				division_name
				division_short_name
				club_name
				# club_state
				team_name
				organization_code
				roster_team_id
				roster_club_id
				division_id
			}
			teams_settings {
				sort_by
				hide_standings
			}
		}
	}
`;
