import { gql } from '@apollo/client';

export const GET_BRACKET = gql`
	query Bracket($id: ID!, $poolId: String!) {
		bracket(id: $id, poolId: $poolId) {
			display_name
			division_name
			pool {
				flow_chart
			}
			matches {
				team1_roster_id
				team2_roster_id
				team1_name
				team2_name
				match_number
				date_start
				court_name
				ref_name
				show_previously_accepted_bid_team1
				show_previously_accepted_bid_team2
				source {
					ref {
						id
						name
						seed
					}
					team1 {
						id
						name
						seed
					}
					team2 {
						id
						name
						seed
					}
				}
				results {
					set1
					set2
					set3
					set4
					set5
					winner
					team1 {
						sets_won
						sets_lost
						points_won
						points_lost
						sets_pct
						matches_lost
						matches_won
						matches_pct
						scores
						roster_team_id
					}
					team2 {
						sets_won
						sets_lost
						points_won
						points_lost
						sets_pct
						matches_lost
						matches_won
						matches_pct
						scores
						roster_team_id
					}
				}
			}
		}
	}
`;
