import { gql } from '@apollo/client';

export const GET_DIVISION_ROUNDS_POOL_BRACKETS = gql`
	query DivisionRoundsPoolBrackets($eswId: ID!, $divisionId: ID!) {
		divisionRounds(eventKey: $eswId, divisionId: $divisionId) {
			uuid
			short_name
			name
			sort_priority
			first_match_start
			last_match_start
			pool_brackets {
				... on Pool {
					uuid
					short_name
					display_name
					is_pool
					sort_priority
					date_start
					settings {
						SetCount
						PlayAllSets
						WinningPoints
					}
					teams {
						team_id
						team_name
						extra {
							show_previously_accepted_bid
							show_accepted_bid
						}
						division_standing {
							seed
						}
						pool_bracket_stat {
							sets_pct
							sets_won
							sets_lost
							matches_won
							matches_lost
							points_ratio
						}
					}
					external {
						courts_short_info {
							uuid
							short_name
						}
					}
				}
				... on Bracket {
					uuid
					short_name
					display_name
					is_pool
					sort_priority
					date_start
					settings {
						SetCount
						PlayAllSets
						WinningPoints
					}
					teams {
						team_id
						team_name
						extra {
							show_previously_accepted_bid
							show_accepted_bid
						}
						division_standing {
							seed
						}
						pool_bracket_stat {
							sets_pct
							sets_won
							sets_lost
							matches_won
							matches_lost
							points_ratio
						}
					}
					external {
						courts_short_info {
							uuid
							short_name
						}
					}
				}
			}
		}
	}
`;
