import { gql } from '@apollo/client';

export const GET_POOL = gql`
	query Pool($id: ID!, $poolId: ID!) {
		pool(id: $id, poolId: $poolId) {
			division_short_name
			uuid
			display_name
			display_name_short
			team_count
			division_short_name
			upcoming_matches {
				court_name
				date_start
				display_name
				division_short_name
				footnote_play
				footnote_team1
				footnote_team2
				match_id
				ref_roster_id
				ref_team_code
				ref_team_name
				results
				team1_code
				team1_name
				team1_roster_id
				team2_code
				team2_name
				team2_roster_id
				team1_master_id
				team2_master_id
			}
			results {
				match_id
				team1_name
				team2_name
				team1_master_id
				team2_master_id
				ref_team_name
				date_start
				court_name
				results {
					set1
					set2
					set3
					set4
					set5
					team1 {
						scores
						matches_won
					}
					team2 {
						scores
						matches_won
					}
				}
			}
			standings {
				display_name
				pb_stats {
					team_id
					name
					rank
					matches_won
					matches_lost
					sets_won
					sets_lost
					sets_pct
					points_ratio
				}
			}
			pb_finishes {
				team_ids
				teams {
					team_name
					team_id
					next_match {
						display_name
						week_day
						start_time_string
						court
						secs_start
						match_id
						external {
							pool_bracket_info {
								is_pool
								uuid
							}
						}
					}
					next_ref {
						display_name
						week_day
						start_time_string
						court
						secs_start
						match_id
						external {
							pool_bracket_info {
								is_pool
								uuid
							}
						}
					}
				}
			}
			next {
				is_pool
				name
				uuid
			}
			prev {
				is_pool
				name
				uuid
			}
		}
	}
`;
