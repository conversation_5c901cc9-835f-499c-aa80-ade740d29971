import { gql } from '@apollo/client';

export const GET_EVENT_FOR_DIVISION_PAGE = gql`
	query EventForDivisionPage($id: ID!) {
		event(id: $id) {
			event_id
			hide_seeds
			name
			long_name
			days
			city
			state
			event_notes
			address
			is_with_prev_qual
			tickets_published
			is_require_recipient_name_for_each_ticket
			tickets_code
			locations {
				location_name
			}
			divisions {
				short_name
				division_id
			}
			clubs {
				roster_club_id
			}
			athletes {
				first
				last
			}
			teams {
				roster_team_id
			}
			teams_settings {
				sort_by
				hide_standings
			}
		}
	}
`;
