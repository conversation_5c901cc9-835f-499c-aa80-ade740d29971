import { gql } from '@apollo/client';

export const GET_TEAM_DETAILS = gql`
	query DivisionDetails($id: ID!, $divisionIds: [ID!]!) {
		divisionDetails(id: $id, divisionIds: $divisionIds) {
			standing {
				heading_group
				teams {
					rank
					roster_team_id
					division_short_name
					division_id
					seed_current
					team_name
					organization_code
					points_ratio
					matches_won
					matches_lost
					sets_won
					sets_lost
					sets_pct
					info {
						points
					}
				}
			}
		}
	}
`;
