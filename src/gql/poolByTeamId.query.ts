import { gql } from '@apollo/client';

export const GET_POOL_BY_TEAM_ID = gql`
	query PoolByTeamId($id: ID!, $teamId: ID!, $poolId: ID) {
		teamSingle(id: $id, teamId: $teamId) {
			roster_team_id
			master_team_id
			division_id
			team_name
			matches_won
			matches_lost
			manual_club_name
			sets_won
			sets_lost
			organization_code
			points_won
			points_lost
			roster_club_id
			club_name
			state
			bracket_finishes {
				winner {
					team_name
					team_id
					next_match {
						court
						match_id
						week_day
						secs_start
						display_name
						start_time_string
						external {
							pool_bracket_info {
								is_pool
								uuid
							}
						}
					}
					next_ref {
						court
						match_id
						week_day
						secs_start
						display_name
						start_time_string
						external {
							pool_bracket_info {
								is_pool
								uuid
							}
						}
					}
				}
				looser {
					team_name
					team_id
					next_match {
						court
						match_id
						week_day
						secs_start
						display_name
						start_time_string
						external {
							pool_bracket_info {
								is_pool
								uuid
							}
						}
					}
					next_ref {
						court
						match_id
						week_day
						secs_start
						display_name
						start_time_string
						external {
							pool_bracket_info {
								is_pool
								uuid
							}
						}
					}
				}
			}
			pb_info {
				uuid
				name
				is_pool
				pb_finishes {
					team_ids
					teams {
						team_name
						team_id
						next_match {
							display_name
							week_day
							start_time_string
							court
							secs_start
							match_id
							external {
								pool_bracket_info {
									is_pool
									uuid
								}
							}
						}
						next_ref {
							display_name
							week_day
							start_time_string
							court
							secs_start
							match_id
							external {
								pool_bracket_info {
									is_pool
									uuid
								}
							}
						}
					}
				}
			}
			upcoming {
				match_type
				match_id
				unix_finished
				results
				display_name
				date_start_formatted
				date_start
				division_id
				division_short_name
				court_name
				pool_name
				pool_bracket_id
				is_pool
				opponent_team_name
				opponent_organization_code
				pb_name
				round_name
				footnote_play
				footnote_team1
				footnote_team2
				settings {
					SetCount
					PlayAllSets
					WinningPoints
				}
			}
			athletes {
				first
				last
				uniform
				short_position
				gradyear
				height
			}
			staff {
				first
				last
				sort_order
				role_name
			}
			results {
				uuid
				is_pool
				pb_name
				round_name
				sort_priority
				pb_stats {
					name
					rank
					team_id
					sets_pct
					sets_won
					sets_lost
					points_won
					matches_pct
					matches_won
					points_lost
					matches_lost
					points_ratio
				}
				matches {
					date_start
					date_start_formatted
					display_name
					division_id
					division_short_name
					match_id
					match_type
					opponent_organization_code
					opponent_team_id
					opponent_team_name
					pool_bracket_id
					unix_finished
					results {
						set1
						set2
						set3
						set4
						set5
						winner
						team1 {
							heading
							heading_sort
							matches_lost
							matches_pct
							matches_won
							overallSeed
							points_lost
							points_ratio
							points_won
							roster_team_id
							scores
							sets_lost
							sets_pct
							sets_won
							title
						}
						team2 {
							heading
							heading_sort
							matches_lost
							matches_pct
							matches_won
							overallSeed
							points_lost
							points_ratio
							points_won
							roster_team_id
							scores
							sets_lost
							sets_pct
							sets_won
							title
						}
					}
				}
			}
		}
		poolIdByTeamId(id: $id, teamId: $teamId, poolId: $poolId) {
			division_short_name
			uuid
			display_name
			display_name_short
			team_count
			division_short_name
			division_id
			upcoming_matches {
				court_name
				date_start
				display_name
				division_short_name
				footnote_play
				footnote_team1
				footnote_team2
				match_id
				ref_roster_id
				ref_team_code
				ref_team_name
				results
				team1_code
				team1_name
				team1_roster_id
				team2_code
				team2_name
				team2_roster_id
			}
			settings {
				PlayAllSets
				SetCount
				WinningPoints
			}
			results {
				match_id
				team1_name
				team2_name
				ref_team_name
				date_start
				court_name
				results {
					set1
					set2
					set3
					set4
					set5
					team1 {
						scores
						matches_won
					}
					team2 {
						scores
						matches_won
					}
				}
			}
			standings {
				display_name
				pb_stats {
					team_id
					name
					rank
					matches_won
					matches_lost
					sets_won
					sets_lost
					sets_pct
					points_ratio
				}
			}
			pb_finishes {
				team_ids
				teams {
					team_name
					team_id
					next_match {
						display_name
						week_day
						start_time_string
						court
						secs_start
						match_id
						external {
							pool_bracket_info {
								is_pool
								uuid
							}
						}
					}
					next_ref {
						display_name
						week_day
						start_time_string
						court
						secs_start
						match_id
						external {
							pool_bracket_info {
								is_pool
								uuid
							}
						}
					}
				}
			}
			next {
				is_pool
				name
				uuid
			}
			prev {
				is_pool
				name
				uuid
			}
		}
	}
`;
