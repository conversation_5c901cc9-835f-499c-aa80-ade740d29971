import { gql } from '@apollo/client';

export const GET_PAGINATED_EVENTS = gql`
	query PaginatedEvents(
		$page: Float!
		$pageSize: Float!
		$search: String
		$startBefore: String
		$startAfter: String
		$endBefore: String
		$endAfter: String
		$years: [String!]
		$asc: Boolean
	) {
		paginatedEvents(
			page: $page
			pageSize: $pageSize
			search: $search
			startBefore: $startBefore
			startAfter: $startAfter
			endBefore: $endBefore
			endAfter: $endAfter
			years: $years
			asc: $asc
		) {
			items {
				event_id
				long_name
				city
				date_start
				state
				address
				schedule_published
				small_logo
			}
			page_info {
				page
				page_size
				page_count
				item_count
			}
		}
	}
`;
