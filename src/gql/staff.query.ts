import { gql } from '@apollo/client';

export const GET_PAGINATED_STAFF = gql`
	query PaginatedStaff($eswId: ID!, $page: Float!, $pageSize: Float!, $search: String) {
		paginatedStaff(eventKey: $eswId, page: $page, pageSize: $pageSize, search: $search) {
			items {
				staff_id
				first
				last
				club_name
				team_id
				team_name
				role_name
				organization_code
				state
			}
			page_info {
				page
				page_size
				page_count
				item_count
			}
		}
	}
`;
