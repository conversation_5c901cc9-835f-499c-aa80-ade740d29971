import { create } from 'zustand';

type Store = {
	favoritesCount: number | null;
	clubsCount: number | null;
	rosterCount: number | null;
	divisionsCount: number | null;
};

type Actions = {
	setCount: (_counts: Partial<Store>) => void;
};

export const useSearchResultCounterStore = create<Store & Actions>((set) => ({
	favoritesCount: null,
	clubsCount: null,
	rosterCount: null,
	divisionsCount: null,
	setCount: (data) => set({ ...data }),
}));
