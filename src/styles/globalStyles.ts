import { createGlobalStyle } from 'styled-components';

import { ThemeT } from './theme';

export const GlobalStyle = createGlobalStyle<ThemeT>`	
	* {
		box-sizing: border-box;
		font-family: 'Public Sans';
		margin: 0;
		padding: 0;
	}
	html, body {
		background: #fff;
	}
	p, h1, h2, h3, h4, h5, h6, a, span, li, strong, b {
		color: ${(props) => props.theme.colors.main};
		
	}
	select {
		-webkit-appearance:none;
		-moz-appearance:none;
		appearance:none;
	}
	ul, li {
		list-style: none;
	}
	.block-scroll{
		overflow: hidden;
	}
	.MuiModal-backdrop {
		background: none !important;
	}
	.MuiDrawer-paper {
		border-radius: 12px !important;
		box-shadow: 0px 8px 16px 0px #00000029 !important;
		border: 1px solid #919EAB3D !important;
		margin: 39px 16px 0 0 !important;
		max-width: 239px !important;
		height: auto !important;
		overflow: hidden !important;
	}
	.sticky {
		position: fixed;
		top: 62px;
		left: 0;
		padding-top: 12px;
		z-index: 2;
		height: 200px;
		@media screen and (max-width: ${(props) => props.theme.breakpoints.medium}) {
		}
	}
	.sticky-hide {
		display: none;
	}
	#modal {
		
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			width: 780px;
			position: fixed;
			height: calc(100vh - 215px);
			left: 50%;
			top: 142px;
			transform: translate(-50%, 0);
			z-index: 99999;
			#modal {
				top: 0;
			}
			&.zoom {
				width: 100%;
				height: 100%;
				top: 0;
				left: 0;
				transform: none;
			}
		}
		@media screen and (max-width: ${(props) => props.theme.breakpoints.medium}) {
			width: calc(100vw - 32px);
		}
	}
	#overlay {
		background:rgba(217, 217, 217, 0.7);
		width: 100%;
		height: 100%;
		position: fixed;
		left: 0;
		z-index: 100;
		top: 0px;
	}
	#portal {
		position: relative;
		z-index: 999;
	}
`;
