import { Link } from 'react-router-dom';
import styled from 'styled-components';

import TabList from '@mui/lab/TabList';
import { IconButton } from '@mui/material';
import Tab from '@mui/material/Tab';

import { Tabs__ItemsWrapper, Tabs__ItemsWrapperTabs } from '@/components/Tabs/styled';

import { Roster__TabContentList, Roster__Wrapper } from '../Roster/styled';

type AccordionWrapperT = {
	$isSmall?: boolean;
};

export const ClubsTab_Wrapper = styled.div`
	padding: 0 16px;
`;
export const RosterTab_Wrapper = styled.div`
	padding: 0 16px;
`;
export const DivisionTab_Wrapper = styled.div`
	padding: 0 16px;
`;
export const FavoritesTab_Wrapper = styled.div`
	padding: 0 16px;
`;

export const SearchResult__ItemLink = styled(Link)`
	text-decoration: none;
	display: flex;
	justify-content: space-between;
`;

export const SearchResult__TabWrapper = styled.div<{ $isBest?: boolean }>`
	/* Tab content wrapper */

	.MuiTabPanel-root {
		padding: 0 !important;
	}
	/* Selected tab color */
	.Mui-selected {
		color: #212b36 !important;
	}
	/* Selected tab's line */
	.MuiTabs-indicator {
		opacity: ${(props) => (props.$isBest ? 0 : 1)};
		bottom: 0;
		background: ${(props) => props.theme.colors.blue};
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			top: 41px;
		}
	}
	/* tabs */
	.MuiButtonBase-root {
		padding: 0 2px 5px 2px !important;
		font-size: 14px;
		line-height: 18px;
		font-family: 'Public Sans';
		text-transform: capitalize;
		color: ${(props) => props.theme.colors.light};
		overflow: unset;
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			font-size: 14px;
			padding: 0 2px 0px 2px !important;
		}
	}
	.Mui-selected {
		font-weight: 700;
	}
	.MuiTabPanel-root {
		padding: 0 10px;
	}
`;
export const SearchResult__TabList = styled(TabList)<{
	$isDesktopFixed?: boolean;
	$isMobileFixed?: boolean;
}>`
	margin-bottom: 12px;
	width: 100%;
	background: #fff;
	height: 48px;
	z-index: 9;
	div {
		justify-content: space-around !important;
		width: 100% !important;
	}
	position: fixed;
	bottom: 0;
	margin: 0;
	padding: 0 8px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		position: fixed;
		left: 50%;
		transform: translateX(-50%);
		top: 192px;
		width: 615px;
		/* z-index: 100; */
		z-index: 100;
		gap: 10px;
		height: 65px !important;
		padding: 10px 0;

		div {
			gap: 33px;
			justify-content: left !important;
		}
	}
`;
export const SearchResult__Tab = styled(Tab)<{
	$count?: number;
	$isActive?: boolean;
	$length?: number;
	$isBestLayout?: boolean;
}>`
	padding: 0 !important;
	min-width: auto !important;
	position: relative;
	&:last-child {
		display: none;
		pointer-events: none;
		&:span {
			opacity: 0;
		}
		opacity: 0;
	}
	&:after {
		content: '${(props) => props.$count}';
		position: absolute;
		top: 5px;
		right: ${(props) => (props.$length === 1 ? '-3px' : '-8px')};
		background: ${(props) => (props.$isActive ? props.theme.colors.blue : 'transparent')};
		color: ${(props) => (props.$isActive ? '#fff' : props.theme.colors.blue)};
		font-size: 10px;
		font-weight: bold;
		font-weight: 700;
		line-height: 10px;
		display: flex;
		align-items: center;
		justify-content: center;
		min-width: ${(props) => (props.$length !== 1 ? '12px' : 'auto')};
		min-height: ${(props) => (props.$length !== 1 ? '12px' : 'auto')};
		padding: 1px 3px;
		border-radius: 10px;
		@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
			top: 2px;
		}
	}
`;
export const SearchResult__TabPanelWrapper = styled.div<{
	$isDesktopFixed?: boolean;
	$isMobileFixed?: boolean;
}>`
	padding-top: ${(props) => (props.$isMobileFixed ? '60px' : '16px')};
	${RosterTab_Wrapper} {
		padding-bottom: 60px;
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		}
	}
	${Tabs__ItemsWrapperTabs} {
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			margin-top: 50px;
		}
	}
	${Roster__Wrapper} {
		${Tabs__ItemsWrapper} {
			bottom: 48px;
		}
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			padding: 140px 40px;
		}
	}
	${Roster__TabContentList} {
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			padding-right: 30px;
		}
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		margin-top: ${(props) => (props.$isDesktopFixed ? '0' : '-96px')};
		padding: 0;
	}
`;
export const SearchResult__AccordionContainer = styled.div`
	display: flex;
	justify-content: space-between;
`;
export const SearchResult__AccordionWrapper = styled.div<AccordionWrapperT>`
	margin: 0 0 16px;
	height: 100%;
	padding: ${(props) => (props.$isSmall ? '0 16px 0 8px' : '0 0 0 16px')};
	box-sizing: border-box !important;
	text-align: ${(props) => (props.$isSmall ? 'right' : 'left')};
	width: ${(props) => (props.$isSmall ? '142px' : 'calc(100% - 142px)')};

	/* Accordion item in tab */
	.MuiAccordion-root {
		box-shadow: none;
		border-radius: 8px;
		border: 1px solid #f4f6f8;
	}
	.MuiAccordionSummary-content {
		flex-direction: column;
	}
`;
export const SearchResult__SeeAllButton = styled.p`
	font-size: 16px;
	color: blue;
	text-align: center;
	margin: 5px 0 10px;
`;
export const SearchResult__AccordionDescriptionWrapper = styled.div`
	padding: 3px 0 0 47px;
`;
export const SearchResult__AccordionTitle = styled.p``;
export const SearchResult__AccordionSubTitle = styled.p`
	color: ${(props) => props.theme.colors.light};
`;

// * Favorite Button
export const SearchResult__FavoriteIconButton = styled(IconButton)`
	position: absolute !important;
	left: 12px;
	top: 15px;
`;

// * Favorite List
export const SearchResult__FavoriteList = styled.ul`
	padding: 0 16px;
`;
export const SearchResult__FavoriteItem = styled.li`
	position: relative;
	display: flex;
	justify-content: space-between;
	margin: 0 0 16px;
`;

export const SearchResult__FavoriteItemLeftBox = styled.div`
	border-radius: 8px;
	border: 1px solid #f4f6f8;
	width: calc(100% - 126px);
	padding: 16px 16px 16px 50px;
	p {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
`;
export const SearchResult__FavoriteItemRightBox = styled.div`
	border-radius: 8px;
	border: 1px solid #f4f6f8;
	text-align: right;
	width: 117px;
	padding: 16px;
	p {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
`;

export const StyledBestWrapper = styled.div<{
	$browserHeight: number;
}>`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 85px 24px 24px 24px;
		width: 680px;
		min-width: 648px;
		margin: auto;
		border-radius: 4px;
		background: #fff;
		min-height: ${(props) => props.$browserHeight - 304}px;
		box-shadow:
			0px 12px 24px -4px rgba(145, 158, 171, 0.12),
			0px 0px 2px 0px rgba(145, 158, 171, 0.2);
		position: fixed;
		top: 62px;
		left: 50%;
		margin: -48px 0 0 -340px;
		top: 240px;
		overflow-y: auto;
		z-index: 99;
		height: calc(100vh - 246px);
	}
`;
