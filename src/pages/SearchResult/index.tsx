import { useAnalytics } from '@/shared/hooks/useAnalytics';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';

import TabContext from '@mui/lab/TabContext';
import TabPanel from '@mui/lab/TabPanel';

import { SearchBar } from '@/components/SearchBar';

import { useStickySearch } from '../Schedule/hooks/useStickySearch';
import { ClubsTab } from './components/ClubsTab';
import { DivisionsTab } from './components/DivisionsTab';
import { FavoritesTab } from './components/FavoritesTab';
import { RosterTab } from './components/RosterTab';
import { useSearchResult } from './hooks/useSearchResult';
import {
	SearchResult__Tab,
	SearchResult__TabList,
	SearchResult__TabPanelWrapper,
	SearchResult__TabWrapper,
	StyledBestWrapper,
} from './styled';

type Props = {
	activeTab: string;
};

const SearchResult = ({ activeTab }: Props) => {
	const { browserHeight } = useCurrentSize();
	const {
		search,
		onChangeSearch,
		onChangeTab,
		favoritesCount,
		clubsCount,
		rosterCount,
		divisionsCount,
	} = useSearchResult({ activeTab });

	useAnalytics('Search Result', search);
	const { isDesktopFixed } = useDesktopSticky({ page: 'divisions' });
	const { isFixed: isFixedSearchBar } = useStickySearch();

	const change = (search: string) => {
		onChangeSearch(search);
	};

	return (
		<>
			<SearchBar search={search} onChangeSearch={change} />
			<SearchResult__TabWrapper $isBest={activeTab === 'best'}>
				<TabContext value={activeTab}>
					<SearchResult__TabList
						onChange={onChangeTab}
						$isDesktopFixed={isDesktopFixed}
						$isMobileFixed={isFixedSearchBar}
					>
						<SearchResult__Tab
							label="Favorites"
							value="favorites"
							$count={favoritesCount || 0}
							$length={+`${`${favoritesCount}`.length}`}
							$isActive={activeTab === 'favorites'}
						/>
						<SearchResult__Tab
							label="Clubs & Teams"
							value="clubs"
							$count={clubsCount || 0}
							$isActive={activeTab === 'clubs'}
							$length={+`${`${clubsCount}`.length}`}
						/>
						<SearchResult__Tab
							label="Roster"
							value="roster"
							$count={rosterCount || 0}
							$length={+`${`${rosterCount}`.length}`}
							$isActive={activeTab === 'roster'}
						/>
						<SearchResult__Tab
							label="Divisions"
							value="divisions"
							$count={divisionsCount || 0}
							$length={+`${`${divisionsCount}`.length}`}
							$isActive={activeTab === 'divisions'}
						/>
						<SearchResult__Tab label="Best" value="best" />
					</SearchResult__TabList>
					<SearchResult__TabPanelWrapper
						$isDesktopFixed={isDesktopFixed}
						$isMobileFixed={isFixedSearchBar}
					>
						<TabPanel value="favorites" keepMounted={true}>
							<FavoritesTab />
						</TabPanel>
						<TabPanel value="clubs" keepMounted={true}>
							<ClubsTab selected={activeTab === 'clubs'} />
						</TabPanel>
						<TabPanel value="roster" keepMounted={true}>
							<RosterTab selected={activeTab === 'roster'} />
						</TabPanel>
						<TabPanel value="divisions" keepMounted={true}>
							<DivisionsTab />
						</TabPanel>
						<TabPanel value="best" keepMounted={true}>
							<StyledBestWrapper $browserHeight={browserHeight} />
						</TabPanel>
					</SearchResult__TabPanelWrapper>
				</TabContext>
			</SearchResult__TabWrapper>
		</>
	);
};

export default SearchResult;
