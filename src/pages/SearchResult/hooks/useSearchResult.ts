import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useSearchResultCounterStore } from '@/store/searchResultCounter.store';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { StringParam, useQueryParams } from 'use-query-params';

type Props = {
	activeTab: string;
};
export const useSearchResult = ({ activeTab }: Props) => {
	const { eswId, event } = useEventDetails();
	const navigate = useNavigate();
	const [queryParams, setQueryParams] = useQueryParams({ search: StringParam });
	const [isDefinedActiveTab, setIsDefinedActiveTab] = useState(false);
	const { favoritesCount, clubsCount, rosterCount, divisionsCount, setCount } =
		useSearchResultCounterStore();

	const [search, setSearch] = useState(queryParams.search || '');
	const onChangeSearch = useCallback(
		(value: string) => {
			setSearch(value);
			setQueryParams({ search: value.trim() });
		},
		[setQueryParams],
	);

	const onChangeTab = (_e: React.SyntheticEvent, tabName: string) => {
		navigate(
			`/events/${eswId}/search-result/${tabName}?search=${encodeURIComponent(search.trim())}`,
		);
	};

	useEffect(() => {
		if (!event) {
			return;
		}
		const { has_rosters } = event;

		if (activeTab === 'best') {
			const isAllLoaded = [
				favoritesCount,
				clubsCount,
				has_rosters ? rosterCount : 0,
				divisionsCount,
			].every((el) => el !== null);

			if (isAllLoaded) {
				const val = {
					...(has_rosters ? { roster: rosterCount } : {}),
					divisions: divisionsCount,
					clubs: clubsCount,
					favorites: favoritesCount,
				};

				const activeTab = (Object.keys(val) as (keyof typeof val)[]).reduce((a, b) =>
					val[a]! > val[b]! ? a : b,
				);

				if (!isDefinedActiveTab) {
					setIsDefinedActiveTab(true);

					navigate(
						`/events/${eswId}/search-result/${activeTab}?search=${encodeURIComponent(search)}`,
						{ replace: true },
					);
				}
			}
		}
	}, [
		favoritesCount,
		clubsCount,
		rosterCount,
		divisionsCount,
		isDefinedActiveTab,
		navigate,
		eswId,
		search,
		activeTab,
		setCount,
		onChangeSearch,
		event,
	]);

	return {
		search,
		onChangeSearch,
		onChangeTab,
		favoritesCount,
		clubsCount,
		rosterCount,
		divisionsCount,
	};
};
