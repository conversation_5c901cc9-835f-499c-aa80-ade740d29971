import { LazyScrollTriggerControl } from '@components/LazyScrollTriggerControl';

import Roster from '@/pages/Roster';

import { RosterTab_Wrapper } from '../../styled';

type Props = {
	selected: boolean;
};

export const RosterTab = ({ selected }: Props) => {
	return (
		<LazyScrollTriggerControl isDisabled={!selected}>
			<RosterTab_Wrapper>
				<Roster isTab />
			</RosterTab_Wrapper>
		</LazyScrollTriggerControl>
	);
};
