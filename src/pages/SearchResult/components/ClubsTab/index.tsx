import { LazyScrollTriggerControl } from '@components/LazyScrollTriggerControl';

import ClubsAndTeams from '@/pages/ClubsAndTeams';

import { ClubsTab_Wrapper } from '../../styled';

type Props = {
	selected: boolean;
};

export const ClubsTab = ({ selected }: Props) => {
	return (
		<LazyScrollTriggerControl isDisabled={!selected}>
			<ClubsTab_Wrapper>
				<ClubsAndTeams isTab />
			</ClubsTab_Wrapper>
		</LazyScrollTriggerControl>
	);
};
