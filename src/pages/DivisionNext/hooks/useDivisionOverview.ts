import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useEventDivisionRoundsDetails } from '@/shared/hooks/useEventDivisionRoundsDetails';
import { useCallback, useMemo } from 'react';

import { DivisionOverviewQuery, useDivisionOverviewQuery } from '@/generated/graphql';

type DivisionPoolBracket = DivisionOverviewQuery['divisionPoolBrackets'][number];

export const useDivisionOverview = (divisionId?: string) => {
	const { eswId, loading: eventLoading } = useEventDetails();
	const { data, loading: dataLoading } = useDivisionOverviewQuery({
		variables: {
			eswId,
			divisionId: divisionId!,
		},
		skip: !divisionId,
	});

	const { rounds, roundsMap, groupedRounds, getRoundGroupKey, getGroupedRoundByRoundId } =
		useEventDivisionRoundsDetails(divisionId);

	const groupedPoolBracketsMap = useMemo<Map<string, DivisionPoolBracket[]>>(() => {
		const groupedPoolBracketsMap = new Map<string, DivisionPoolBracket[]>();
		// Waiting for the correct data to be loaded
		if (!data?.divisionPoolBrackets) return groupedPoolBracketsMap;

		const { divisionPoolBrackets } = data;
		rounds.forEach((round) => {
			const roundId = round.uuid;
			const roundPoolBrackets = divisionPoolBrackets.filter(({ round_id }) => round_id === roundId);
			const groupKey = getRoundGroupKey(round);
			const poolBrackets = groupedPoolBracketsMap.get(groupKey);
			if (!poolBrackets) {
				groupedPoolBracketsMap.set(groupKey, roundPoolBrackets);
			} else {
				groupedPoolBracketsMap.set(groupKey, [...poolBrackets, ...roundPoolBrackets]);
			}
		});
		return groupedPoolBracketsMap;
	}, [data, rounds, getRoundGroupKey]);

	const getPoolBracketsByRoundId = useCallback(
		(roundId: string): DivisionPoolBracket[] => {
			const round = roundsMap.get(roundId);
			if (!round) return [];
			const groupKey = getRoundGroupKey(round);
			return groupedPoolBracketsMap.get(groupKey) ?? [];
		},
		[groupedPoolBracketsMap, roundsMap, getRoundGroupKey],
	);

	return {
		loading: dataLoading || eventLoading,
		groupedRounds,
		getPoolBracketsByRoundId,
		getGroupedRoundByRoundId,
	};
};
