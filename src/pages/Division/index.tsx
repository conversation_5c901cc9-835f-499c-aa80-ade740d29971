import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import TabContext from '@mui/lab/TabContext';
import TabPanel from '@mui/lab/TabPanel';
import Tab from '@mui/material/Tab';

import { FlowChartTab } from './components/FlowChartTab';
import { PoolBracketsTab } from './components/PoolBracketsTab';
import { StandingsTab } from './components/StandingsTab';
import { TeamsTab } from './components/TeamsTab';
import { POOL_TABS } from './constants';
import { useDivisionTeamsStanding } from './hooks/useDivisionTeamsStanding';
import {
	DivisionTabs__ContentWrapper,
	DivisionTabs__ItemsWrapper,
	DivisionTabs__ItemsWrapperTabs,
	DivisionTabs__Wrapper,
	defaultTabPanelStyles,
	grayBackgroundTabPanelStyles,
} from './styled';

type Props = {
	activeTab: POOL_TABS;
};

const Division = ({ activeTab }: Props) => {
	const { eswId, event, divisions } = useEventDetails();
	const { divisionId } = useParams();
	const navigate = useNavigate();

	const onChangeTab = (_e: React.SyntheticEvent, tabName: string) => {
		navigate(`/events/${eswId}/divisions/${divisionId}/${tabName}`);
	};

	const { breakPont } = useCurrentSize();
	const isSmall = breakPont === 'small';
	const { isDesktopFixed } = useDesktopSticky({ page: 'favoritesTabs' });

	const { hasRanks } = useDivisionTeamsStanding(divisionId!);
	// Show standings if there are ranks or if the event is not set to hide standings
	const showStandings = hasRanks || !(event?.teams_settings?.hide_standings || false);

	useEffect(() => {
		window.scrollTo(0, 0);
	}, [activeTab]);

	const isShowFlowChartTab = divisions?.some((division) => {
		return !!division.media.length && division.has_flow_chart;
	});

	const desktopTabs = (
		<>
			{!isSmall ? (
				<DivisionTabs__ItemsWrapper $isDesktopFixed={isDesktopFixed}>
					<DivisionTabs__ItemsWrapperTabs value={activeTab} onChange={onChangeTab}>
						<Tab label="Teams" value={POOL_TABS.TEAMS} />
						<Tab label="Pools/Brackets" value={POOL_TABS.POOLS} />
						{showStandings && <Tab label="Standings" value={POOL_TABS.STANDINGS} />}
						{isShowFlowChartTab && <Tab label="Flow chart" value={POOL_TABS.FLOW_CHART} />}
					</DivisionTabs__ItemsWrapperTabs>
				</DivisionTabs__ItemsWrapper>
			) : null}
		</>
	);
	return (
		<DivisionTabs__Wrapper>
			<TabContext value={activeTab}>
				<DivisionTabs__ContentWrapper>
					<TabPanel value={POOL_TABS.TEAMS} sx={defaultTabPanelStyles}>
						<TeamsTab divisionId={divisionId!}>{desktopTabs}</TeamsTab>
					</TabPanel>
					<TabPanel value={POOL_TABS.POOLS} sx={defaultTabPanelStyles}>
						<PoolBracketsTab divisionId={divisionId!}>{desktopTabs}</PoolBracketsTab>
					</TabPanel>
					{showStandings && (
						<TabPanel value={POOL_TABS.STANDINGS} sx={grayBackgroundTabPanelStyles}>
							<StandingsTab divisionId={divisionId!}>{desktopTabs}</StandingsTab>
						</TabPanel>
					)}
					{isShowFlowChartTab && (
						<TabPanel value={POOL_TABS.FLOW_CHART} sx={defaultTabPanelStyles}>
							<FlowChartTab divisionId={divisionId!}>{desktopTabs}</FlowChartTab>
						</TabPanel>
					)}
				</DivisionTabs__ContentWrapper>
				{isSmall && (
					<DivisionTabs__ItemsWrapper>
						<DivisionTabs__ItemsWrapperTabs
							value={activeTab}
							onChange={onChangeTab}
							variant="scrollable"
						>
							<Tab label="Teams" value={POOL_TABS.TEAMS} />
							<Tab label="Pools/Brackets" value={POOL_TABS.POOLS} />
							{showStandings && <Tab label="Standings" value={POOL_TABS.STANDINGS} />}
							{isShowFlowChartTab && <Tab label="Flow chart" value={POOL_TABS.FLOW_CHART} />}
						</DivisionTabs__ItemsWrapperTabs>
					</DivisionTabs__ItemsWrapper>
				)}
			</TabContext>
		</DivisionTabs__Wrapper>
	);
};

export default Division;
