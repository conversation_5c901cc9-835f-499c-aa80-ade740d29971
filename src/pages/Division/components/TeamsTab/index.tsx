import { useAnalytics } from '@/shared/hooks/useAnalytics';
import { useBracketModal } from '@/shared/hooks/useBracketModal';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useLazyScrollTrigger } from '@/shared/hooks/useLazyScrollTrigger';
import { usePoolModal } from '@/shared/hooks/usePoolModal';
import { useTeamModal } from '@/shared/hooks/useTeamModal';
import { useRef } from 'react';
import { StringParam, useQueryParams } from 'use-query-params';

import { StyledNotFound } from '@/styles/shared';

import { NotFound } from '@/components/NotFound';
import { Spinner } from '@/components/Spinner';
import { StandingResult } from '@/components/StandingResult';

import { addShortCourtNamePrefix } from '@/utils/prefixer';
import { getFormateTime } from '@/utils/time';

import {
	ClubsAndTeams__TeamCourt,
	ClubsAndTeams__TeamDescription,
	ClubsAndTeams__TeamDescriptionInfoSection,
	ClubsAndTeams__TeamItem,
} from '@/pages/ClubsAndTeams/styled';
import { useStickySearch } from '@/pages/Schedule/hooks/useStickySearch';

import { POOL_TABS } from '../../constants';
import { DivisionSearchBar } from '../DivisionSearchBar';
import { useDivisionTeams } from './hooks/useDivisionTeams';
import {
	DivisionTabs__TeamsList,
	DivisionTabs__TeamsWrapper,
	DivisionTabs__TeamsWrapperInner,
} from './styled';

type Props = {
	divisionId: string;
	children: React.ReactNode;
};

export const TeamsTab = ({ divisionId, children }: Props) => {
	const { loading: eventLoading } = useEventDetails();

	const { teamModalElement, openTeamModal, teamDataLoading } = useTeamModal();
	const { poolModalElement, openPoolModal, poolDataLoading } = usePoolModal();
	const { bracketModalElement, openBracketModal, bracketDataLoading } =
		useBracketModal(openPoolModal);

	const {
		teams,
		search,
		onChangeSearch,
		loading: teamsLoading,
		fetchNext,
	} = useDivisionTeams(divisionId);
	useAnalytics('Division (Teams)', search);

	const { isFixed: isFixedSearchBar } = useStickySearch();
	const { browserHeight, breakPont } = useCurrentSize();
	const isMobile = breakPont === 'small';
	const { isDesktopFixed } = useDesktopSticky({ page: 'division' });
	const { isDesktopFixed: isDesktopFixedTab } = useDesktopSticky({ page: 'favoritesTabs' });

	const scrollRef = useRef<HTMLDivElement>(null);
	useLazyScrollTrigger(fetchNext, isMobile ? null : scrollRef);

	const loading =
		teamsLoading || teamDataLoading || poolDataLoading || bracketDataLoading || eventLoading;

	const [, setQueryParams] = useQueryParams({
		divisionId: StringParam,
	});

	return (
		<>
			{teamsLoading && <Spinner />}
			<DivisionSearchBar
				divisionId={divisionId}
				search={search}
				onChangeSearch={onChangeSearch}
				isFixed={isFixedSearchBar}
				activeTab={POOL_TABS.TEAMS}
				placeholder="Teams"
			/>
			{teamModalElement}
			{poolModalElement}
			{bracketModalElement}
			{
				<DivisionTabs__TeamsWrapper
					$browserHeight={browserHeight}
					$isDesktopFixed={isDesktopFixed}
					ref={scrollRef}
				>
					<DivisionTabs__TeamsWrapperInner>
						{!isMobile && children}
						<DivisionTabs__TeamsList
							$isFixed={isFixedSearchBar}
							$isDesktopFixed={isDesktopFixedTab}
						>
							{teams.map((team) => (
								<div key={team.team_id}>
									{!team.next_match ? (
										<ClubsAndTeams__TeamItem $marginBottom>
											<ClubsAndTeams__TeamDescription $isPlayed={true}>
												<ClubsAndTeams__TeamDescriptionInfoSection>
													<div onClick={() => openTeamModal(team.team_id)}>
														<strong>{team.team_name}</strong>
													</div>
												</ClubsAndTeams__TeamDescriptionInfoSection>
											</ClubsAndTeams__TeamDescription>
											<ClubsAndTeams__TeamCourt $isPlayed={true}>
												<p onClick={() => openBracketModal(team)}>
													<strong>
														<StandingResult standing={team.division_standing} />
													</strong>
												</p>
											</ClubsAndTeams__TeamCourt>
										</ClubsAndTeams__TeamItem>
									) : (
										<ClubsAndTeams__TeamItem $marginBottom>
											<ClubsAndTeams__TeamDescription $isPlayed={false}>
												<ClubsAndTeams__TeamDescriptionInfoSection>
													<div onClick={() => openTeamModal(team.team_id)}>
														<strong>{team.team_name}</strong>
													</div>
													<div>vs {team.next_match.external.opponent_display_name}</div>
												</ClubsAndTeams__TeamDescriptionInfoSection>
											</ClubsAndTeams__TeamDescription>
											<ClubsAndTeams__TeamCourt
												$isPlayed={false}
												onClick={() => {
													setQueryParams({ divisionId: team?.division_id });
													openPoolModal(team.team_id);
												}}
											>
												<div>
													<p>
														{getFormateTime({
															time: team.next_match.secs_start!,
															format: 'EEE, h:mmaaa',
														})}
													</p>
													<p>
														{addShortCourtNamePrefix(
															team.next_match.external.court_info?.short_name || '',
														)}
													</p>
												</div>
											</ClubsAndTeams__TeamCourt>
										</ClubsAndTeams__TeamItem>
									)}
								</div>
							))}
						</DivisionTabs__TeamsList>
					</DivisionTabs__TeamsWrapperInner>
					{!teams.length && !loading && !search && (
						<StyledNotFound>No Teams available yet.</StyledNotFound>
					)}
					{!teams.length && !loading && search && <NotFound type="pool-brackets" />}
				</DivisionTabs__TeamsWrapper>
			}
		</>
	);
};
