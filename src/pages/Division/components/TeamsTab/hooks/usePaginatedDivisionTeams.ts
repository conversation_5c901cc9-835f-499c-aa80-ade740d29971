import { DEFAULT_PAGE_SIZE } from '@/config';
import { PaginatedDivisionTeam } from '@/shared/types/team.types';
import { useCallback, useEffect, useMemo, useState } from 'react';

import {
	PaginatedDivisionTeamsQuery,
	PaginatedDivisionTeamsQueryVariables,
	usePaginatedDivisionTeamsLazyQuery,
} from '@/generated/graphql';

type Params = Omit<PaginatedDivisionTeamsQueryVariables, 'page' | 'pageSize'>;

const INITIAL_PAGE = 1;

export const usePaginatedDivisionTeams = (
	params: Params,
): [() => void, PaginatedDivisionTeam[], boolean] => {
	const { eswId, divisionId, search } = params;
	const [pagesResponses, setPagesResponses] = useState<PaginatedDivisionTeamsQuery[]>([]);

	const [fetchDivisionTeams, { data, loading }] = usePaginatedDivisionTeamsLazyQuery();

	useEffect(() => {
		setPagesResponses([]);
		fetchDivisionTeams({
			variables: {
				eswId,
				divisionId,
				page: INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
			},
		});
	}, [fetchDivisionTeams, search, eswId, divisionId]);

	// Update the pagesResponses array when a new page is fetched taking care of the order
	useEffect(() => {
		if (!data) return;
		setPagesResponses((pagesResponses) => {
			const responses = [...pagesResponses];
			responses[data.paginatedDivisionTeams.page_info.page - 1] = data;
			return responses;
		});
	}, [data]);

	// Combine all teams from all pages
	const teams = useMemo(() => {
		return pagesResponses.reduce<PaginatedDivisionTeam[]>((acc, response) => {
			if (!response) return acc;
			return [
				...acc,
				...((response?.paginatedDivisionTeams?.items as PaginatedDivisionTeam[]) ?? []),
			];
		}, []);
	}, [pagesResponses]);

	// Prepare the fetchNext function
	const fetchNext = useCallback(() => {
		let nextPage;
		const lastResponse = pagesResponses.at(-1);
		if (lastResponse) {
			const { page, page_count } = lastResponse.paginatedDivisionTeams.page_info;
			if (page >= page_count) return;
			nextPage = page + 1;
		}
		fetchDivisionTeams({
			variables: {
				eswId,
				divisionId,
				page: nextPage || INITIAL_PAGE,
				pageSize: DEFAULT_PAGE_SIZE,
				search,
			},
		});
	}, [pagesResponses, fetchDivisionTeams, search, eswId, divisionId]);

	return [fetchNext, teams, loading];
};
