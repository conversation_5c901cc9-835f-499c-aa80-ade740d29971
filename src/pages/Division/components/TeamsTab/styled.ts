import styled from 'styled-components';

export const DivisionTabs__TeamsWrapper = styled.div<{
	$isFixed?: boolean;
	$browserHeight: number;
	$isDesktopFixed: boolean;
}>`
	padding: ${({ $isFixed }) => ($isFixed ? '60px 16px 16px' : '16px')};
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 85px 24px 24px;
		width: 680px;
		min-width: 648px;
		margin-top: ${(props) => (props.$isDesktopFixed ? 210 : -48)}px;
		border-radius: 4px;
		background: #fff;
		min-height: ${(props) => props.$browserHeight - 304}px;
		box-shadow:
			0px 12px 24px -4px rgba(145, 158, 171, 0.12),
			0px 0px 2px 0px rgba(145, 158, 171, 0.2);
		position: fixed;
		left: 50%;
		margin: -48px 0 0 -340px;
		top: 240px;
		overflow-y: auto;
		z-index: 99;
		height: calc(100vh - 246px);
	}
`;
export const DivisionTabs__TeamsWrapperInner = styled.div`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		width: 600px;
		margin: auto;
	}
`;
export const DivisionTabs__TeamsList = styled.ul<{ $isFixed?: boolean; $isDesktopFixed: boolean }>`
	padding: ${({ $isFixed }) => ($isFixed ? '60px 0 0' : '0')};
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 0;
	}
`;
