import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { getTicketsDirectLink } from '@/utils';
import buyAdmissionIcon from '@assets/buy-admission-icon.svg';
import { useMemo } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';

import closeIcon from '@/assets/close-icon.svg';
import searchIcon from '@/assets/searchSmall-icon.svg';

import { Division__MainNavWrapper, Division__SearchBarAdmission } from '@/styles/shared';

import { Select } from '@/components/CustomSelect';
import { MainNav } from '@/components/MainNav';
import { SearchBar__EventInfo } from '@/components/SearchBar/styled';

import { POOL_TABS } from '../../constants';
import {
	Division__Search,
	Division__SearchBar,
	Division__SearchBarFooter,
	Division__SearchBarFooterWrapper,
	Division__SearchBarHeader,
	Division__SearchBarTitle,
	Division__SearchCourtGridButton,
	Division__SearchIcon,
	Division__SearchWrapper,
} from './styled';

type Props = {
	isFixed?: boolean;
	activeTab: POOL_TABS;
	divisionId: string;
	search?: string;
	onChangeSearch?: (_val: string) => void;
	scrollWrapper?: React.MutableRefObject<HTMLDivElement | null>;
	placeholder?: string;
};

export const DivisionSearchBar = ({
	activeTab,
	divisionId,
	search,
	onChangeSearch,
	isFixed,
	placeholder = 'Search',
}: Props) => {
	const { eswId, event, divisions } = useEventDetails();
	const location = useLocation();
	const navigate = useNavigate();
	const eventName = event?.long_name || '';
	const ticketLink = getTicketsDirectLink(event);
	const isShowBuyAdmission = !!event?.tickets_published;

	const tabsWithoutGridButton = [POOL_TABS.STANDINGS];
	const { breakPont } = useCurrentSize();
	const isLarge = breakPont !== 'small';
	const { isDesktopFixed } = useDesktopSticky({ page: 'favorites' });

	const divisionOptions = useMemo(() => {
		if (!divisions) return [];
		return divisions.map(({ division_id, short_name }) => ({
			key: short_name || '',
			value: division_id,
		}));
	}, [divisions]);

	const divisionWithFullNameOptions = useMemo(() => {
		if (!divisions) return [];
		return divisions.map(({ division_id, name }) => ({
			key: name || '',
			value: division_id,
		}));
	}, [divisions]);

	const onChangeDivision = (divisionId: string | number) => {
		const pathSegments = location.pathname.split('/');
		const divisionPathIndex = pathSegments.indexOf('divisions');
		if (divisionPathIndex !== -1 && divisionPathIndex + 1 < pathSegments.length) {
			pathSegments[divisionPathIndex + 1] = divisionId.toString();
		}
		navigate(pathSegments.join('/'));
	};

	if (activeTab === POOL_TABS.FLOW_CHART) {
		return (
			<Division__SearchBar className={isDesktopFixed ? 'sticky' : ''}>
				<Division__SearchBarHeader className={isDesktopFixed ? 'sticky-hide' : ''}>
					{breakPont === 'small' && (
						<>
							<SearchBar__EventInfo $isFullWidth={!isShowBuyAdmission}>
								<Division__SearchBarTitle $isFullWidth={!isShowBuyAdmission}>
									<Link to={`/events/${eswId}`}>{eventName}</Link>
								</Division__SearchBarTitle>
							</SearchBar__EventInfo>
							{isShowBuyAdmission && (
								<Division__SearchBarAdmission href={ticketLink} target="_blank">
									<img src={buyAdmissionIcon} alt="buy admission" />
									Buy admission
								</Division__SearchBarAdmission>
							)}
						</>
					)}
				</Division__SearchBarHeader>
				{isLarge && (
					<Division__MainNavWrapper>
						<MainNav />
						{isShowBuyAdmission && (
							<Division__SearchBarAdmission href={ticketLink} target="_blank">
								<img src={buyAdmissionIcon} alt="buy admission" />
								Buy admission
							</Division__SearchBarAdmission>
						)}
					</Division__MainNavWrapper>
				)}
				<Division__SearchBarFooterWrapper>
					<Division__SearchBarFooter $isFixed={!!isFixed}>
						<Select
							width={155}
							value={divisionId}
							options={divisionWithFullNameOptions}
							onChange={onChangeDivision}
						/>
					</Division__SearchBarFooter>
				</Division__SearchBarFooterWrapper>
			</Division__SearchBar>
		);
	}

	return (
		<Division__SearchBar className={isDesktopFixed ? 'sticky' : ''}>
			<Division__SearchBarHeader className={isDesktopFixed ? 'sticky-hide' : ''}>
				{breakPont === 'small' && (
					<>
						<SearchBar__EventInfo $isFullWidth={!isShowBuyAdmission}>
							<Division__SearchBarTitle $isFullWidth={!isShowBuyAdmission}>
								<Link to={`/events/${eswId}`}>{eventName}</Link>
							</Division__SearchBarTitle>
						</SearchBar__EventInfo>
						{isShowBuyAdmission && (
							<Division__SearchBarAdmission href={ticketLink} target="_blank">
								<img src={buyAdmissionIcon} alt="buy admission" />
								Buy admission
							</Division__SearchBarAdmission>
						)}
					</>
				)}
			</Division__SearchBarHeader>
			{isLarge && (
				<Division__MainNavWrapper>
					<MainNav />
					{isShowBuyAdmission && (
						<Division__SearchBarAdmission href={ticketLink} target="_blank">
							<img src={buyAdmissionIcon} alt="buy admission" />
							Buy admission
						</Division__SearchBarAdmission>
					)}
				</Division__MainNavWrapper>
			)}
			<Division__SearchBarFooterWrapper>
				<Division__SearchBarFooter $isFixed={!!isFixed}>
					<Select
						width={85}
						value={divisionId}
						options={divisionOptions}
						onChange={onChangeDivision}
					/>
					<Division__SearchWrapper>
						<Division__Search
							type="text"
							placeholder={placeholder}
							value={search}
							onChange={(e) => onChangeSearch?.(e.target.value)}
						/>
						{!search && <Division__SearchIcon src={searchIcon} />}
						{search && (
							<Division__SearchIcon src={closeIcon} $isClear onClick={() => onChangeSearch?.('')} />
						)}
					</Division__SearchWrapper>
					{!tabsWithoutGridButton.includes(activeTab) && (
						<Division__SearchCourtGridButton
							onClick={() => navigate(`/events/${eswId}/court-grid?divisionId=${divisionId}`)}
						>
							Grid
						</Division__SearchCourtGridButton>
					)}
				</Division__SearchBarFooter>
			</Division__SearchBarFooterWrapper>
		</Division__SearchBar>
	);
};
