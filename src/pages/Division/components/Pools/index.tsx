import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Link } from 'react-router-dom';

import { Alert } from '@mui/material';

import { Loader } from '@/components/Loader';

import { DivisionPoolTeam, PoolsQuery, usePoolsQuery } from '@/generated/graphql';

import { Round } from './Round';

const Pools = () => {
	const { id, divisionId } = useParams();
	const [rounds, setRounds] = useState<PoolsQuery['pools'][]>([]);

	const { loading, data } = usePoolsQuery({
		variables: {
			id: id!,
			divisionId: divisionId!,
		},
	});

	useEffect(() => {
		if (data) {
			const groupedRounds = data.pools.reduce(
				(acc, el) => {
					const { r_uuid, ...rest } = el;
					if (!acc[r_uuid!] && el.uuid) {
						acc[r_uuid!] = [];
					}
					if (el.uuid) {
						acc[r_uuid!].push({ r_uuid, ...rest });
					}
					return acc;
				},
				{} as Record<string, PoolsQuery['pools']>,
			);

			const result = Object.values(groupedRounds);
			setRounds(result);
		}
	}, [data]);

	return (
		<div>
			{loading && <Loader />}
			{!!rounds.length && (
				<ul>
					{rounds.map((round, index) => (
						<li key={`round_${index}`}>
							{round.map((pool) => {
								return (
									<div key={pool.uuid}>
										<Alert severity="info">
											<Link to={`${pool.uuid}`}>
												{pool.division_short_name} {pool.r_name} {pool.pb_name}{' '}
											</Link>
										</Alert>
										{pool.teams && <Round teams={pool.teams as DivisionPoolTeam[]} />}
									</div>
								);
							})}
						</li>
					))}
				</ul>
			)}
		</div>
	);
};

export default Pools;
