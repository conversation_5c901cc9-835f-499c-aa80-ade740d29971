import styled from 'styled-components';

// * Pool/Brackets
export const DivisionTabs__PoolWrapper = styled.div<{
	$isFixed?: boolean;
	$browserHeight: number;
	$isDesktopFixed: boolean;
}>`
	overflow: hidden;
	padding-top: ${({ $isFixed }) => ($isFixed ? 100 : 0)}px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 130px 24px 24px;
		width: 680px;
		min-width: 648px;
		margin: auto;
		margin-top: ${(props) => (props.$isDesktopFixed ? 130 : -48)}px;
		border-radius: 4px;
		background: #fff;
		min-height: ${(props) => props.$browserHeight - 304}px;
		box-shadow:
			0px 12px 24px -4px rgba(145, 158, 171, 0.12),
			0px 0px 2px 0px rgba(145, 158, 171, 0.2);
		position: fixed;
		left: 50%;
		margin: -48px 0 0 -340px;
		top: 240px;
		z-index: 99;
		overflow-y: auto;
		height: calc(100vh - 246px);
	}
`;
export const DivisionTabs__PoolList = styled.ul<{ $isDesktopFixed: boolean }>`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 0;
		&:first-child {
			margin-top: ${(props) => (props.$isDesktopFixed ? 140 : 0)}px;
		}
	}
`;
export const DivisionTabs__PoolItem = styled.li`
	margin: 0 0 16px;
`;
export const DivisionTabs__PoolItemHeader = styled.div`
	padding: 16px;
	border: 0.5px solid #f4f6f8;
	background: #fff;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		cursor: pointer;
		&:hover {
			strong {
				text-decoration: underline;
			}
		}
	}
	p {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 14px;
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			line-height: 24px;
			justify-content: normal;
			gap: 40px;
		}
	}
	strong {
		display: flex;
		align-items: center;
		gap: 16px;
	}
`;
export const DivisionTabs__EmptyValue = styled.div`
	margin-top: 8px;
	height: 70px;
	padding: 24px;
	border-radius: 4px;
	background: var(--White, #fff);
	box-shadow:
		0px 0px 2px 0px rgba(145, 158, 171, 0.2),
		0px 12px 24px -4px rgba(145, 158, 171, 0.12);
	color: #454f5b;
	font-size: 14px;
	font-weight: 400;
	line-height: 22px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 0 16px;
		display: flex;
		align-items: center;
		margin: 0;
		height: 50px;
	}
`;
export const DivisionTabs__PoolItemTeamList = styled.ul``;
export const DivisionTabs__PoolItemTeamItem = styled.li<{ $isShowStat: boolean }>`
	display: flex;
	background: #f4f6f8;
	padding: 16px;
	margin: 0 0 8px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		cursor: pointer;
		&:hover {
			div:first-child {
				text-decoration: underline;
			}
		}
	}
	div {
		padding: 0 5px;
		position: relative;
		font-weight: 400;
		line-height: 18px;
		text-align: right;
		font-size: 14px;
		span {
			white-space: nowrap;
			overflow: hidden;
			text-overflow: ellipsis;
			font-weight: 400;
			line-height: 18px;
			text-align: right;
			font-size: 14px;
		}
		i {
			font-style: normal;
			font-weight: 700;
		}
	}
	div:nth-child(1) {
		width: 45%;
		/* width: ${(props) => (props.$isShowStat ? '50%' : 'calc(50% - 35px)')}; */
		text-align: left;
	}
	div:nth-child(2) {
		/* width: 7%; */
		width: 10%;
		font-weight: 700;
	}
	div:nth-child(3) {
		/* width: 10%; */
		width: 30%;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		font-weight: 400;
		line-height: 18px;
		text-align: right;
		font-size: 14px;
	}
	div:nth-child(4) {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		font-weight: 400;
		line-height: 18px;
		text-align: right;
		font-size: 14px;
		/* width: 35%; */
	}
	div:nth-child(5) {
		/* width: 15%; */
	}
`;
export const DivisionTabs__TitleWrapper = styled.span<{ $extraOffset?: boolean }>`
	display: inline-block;
	width: ${({ $extraOffset }) => ($extraOffset ? '70%' : '88%')};
	text-align: left !important;
`;
export const DivisionTabs__PreviouslyQualifiedWrapper = styled.section<{ $position?: number }>`
	z-index: ${({ $position }) => ($position === 1 ? 1 : 2)};
	width: 20px;
	position: absolute;
	left: ${({ $position }) => ($position === 1 ? 'calc(100% - 30px)' : 'calc(100% - 55px)')};
	top: 0;
`;

export const SCROLL_OFFSET = 39;

export const DivisionTabs__StickyHeader = styled.div<{
	$isActive?: boolean;
	$isFixed?: boolean;
	$isDesktopFixed?: boolean;
}>`
	display: flex;
	z-index: 4;
	padding: 11px 16px;
	border-bottom: 1px solid #f4f6f8;
	gap: 16px;
	width: 100%;
	overflow-x: scroll;
	position: ${({ $isFixed }) => ($isFixed ? 'fixed' : 'static')};
	top: 110px;
	background: #fff;
	&::-webkit-scrollbar {
		display: none;
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		position: fixed;
		width: 600px;
		top: 262px;
		margin: auto;
		padding: 10px 0 16px;
		border: none;
	}
`;
export const DivisionTabs__ScrollWrapper = styled.div<{ $isFixed?: boolean }>`
	position: fixed;
	width: 100%;
	height: 100%;
	background: #fff;
	outline: none;
	top: ${({ $isFixed }) => ($isFixed ? 190 : 220)}px;
	overflow-y: scroll;
	padding-bottom: 220px;
	padding-top: ${({ $isFixed }) => ($isFixed ? 40 : 0)}px;

	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		position: static;
		width: 600px;
		margin: auto;
		overflow-y: unset;
	}
`;
export const DivisionTabs__AnchorButton = styled.a<{ $isActive?: boolean; $width?: number }>`
	font-size: 14px;
	line-height: 22px;
	text-decoration: none;
	border-radius: 2px;
	background: ${(props) => (props.$isActive ? props.theme.colors.blue : '#fff')} !important;
	color: ${(props) => (props.$isActive ? '#fff' : props.theme.colors.blue)} !important;
	border: 1px solid ${(props) => props.theme.colors.blue};
	min-width: ${(props) =>
		props.$width ? `${(props.$width || 0) + 16 < 102 ? 102 : (props.$width || 0) + 16}px` : 'auto'};
	text-align: center;
	padding: 0;
	height: 31px;
	display: flex;
	align-items: center;
	justify-content: space-around;
	&:hover {
		background: ${(props) => props.theme.colors.blue};
		color: #fff;
	}
	&:focus {
		background: inherit;
		color: inherit;
	}
`;
export const DivisionTabs__TabsWrapper = styled.div`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		width: 600px;
		margin: auto;
	}
`;
export const DivisionTabs__RoundTitle = styled.h3`
	color: #194ed5;
	font-size: 16px;
	font-weight: 700;
	line-height: 24px;
	border-bottom: 2px solid ${(props) => props.theme.colors.blue};
	padding-bottom: 11px;
	margin: 0 0 5px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
		padding-left: 16px;
		padding-right: 16px;
	}
`;
