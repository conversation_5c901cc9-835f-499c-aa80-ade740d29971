import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { DivisionRoundPollBracket } from '@/shared/types/round.types';
import { useCallback, useMemo } from 'react';

import { useDivisionRoundsPoolBracketsQuery } from '@/generated/graphql';

import { getDaysBetweenDates } from '@/utils/time';

import { mergeRounds } from '../helpers';

type PoolBracketTeam = DivisionRoundPollBracket['pool_brackets'][number]['teams'][number];

export const useDivisionRoundsPoolBrackets = (divisionId: string) => {
	const { eswId, event } = useEventDetails();
	const sortTeamBy = event?.teams_settings?.sort_by || '';

	const { data, loading } = useDivisionRoundsPoolBracketsQuery({
		variables: {
			eswId,
			divisionId,
		},
	});

	// Group round by names, as we could have the same round split in multiple records in the DB
	const uniqRounds = useMemo(() => {
		if (!data) return [];
		const groupedRounds = data.divisionRounds.reduce<Map<string, DivisionRoundPollBracket>>(
			(acc, round, i) => {
				const groupKey = round.short_name || `No Name ${i}`;
				if (acc.has(groupKey)) {
					acc.set(groupKey, mergeRounds(acc.get(groupKey)!, round));
				} else {
					acc.set(groupKey, round);
				}
				return acc;
			},
			new Map(),
		);
		return Array.from(groupedRounds.values());
	}, [data]);

	// Get the days between the first and last match of each round
	const roundsDays = useCallback(
		(type: 'short' | 'full') => {
			return uniqRounds.reduce<Map<string, string[]>>((acc, round) => {
				const { uuid, first_match_start, last_match_start } = round;
				const fromDate = first_match_start ? new Date(first_match_start) : null;
				const toDate = last_match_start ? new Date(last_match_start) : null;
				acc.set(uuid, getDaysBetweenDates({ startDate: fromDate, endDate: toDate, format: type }));
				return acc;
			}, new Map());
		},
		[uniqRounds],
	);

	const roundsShortDays = useMemo(() => roundsDays('short'), [roundsDays]);
	const roundsFullDays = useMemo(() => roundsDays('full'), [roundsDays]);

	const roundsWithSortedTeams = useMemo(() => {
		const sortFunction = (a: PoolBracketTeam, b: PoolBracketTeam) => {
			if (sortTeamBy === 'team_name') {
				return a.team_name.localeCompare(b.team_name);
			}
			// Sorting by default by seed and then by team name
			const diff = (a.division_standing?.seed || 0) - (b.division_standing?.seed || 0);
			return diff !== 0 ? diff : a.team_name.localeCompare(b.team_name);
		};
		return uniqRounds.map((round) => {
			return {
				...round,
				pool_brackets: round.pool_brackets.map((poolBracket) => {
					return {
						...poolBracket,
						teams: poolBracket.teams.toSorted(sortFunction),
					};
				}),
			};
		});
	}, [uniqRounds, sortTeamBy]);

	return {
		rounds: roundsWithSortedTeams,
		roundsShortDays,
		roundsFullDays,
		roundsLoading: loading,
	};
};
