import { sanitizeSearchInput } from '@/utils';
import bracketIcon from '@assets/bracketIcon.svg';
import poolIcon from '@assets/poolIcon.svg';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { StringParam, useQueryParams } from 'use-query-params';

import { PoolOrBracketBase, Team } from '@/generated/graphql';

import { filterRounds } from '../helpers';
import { useDivisionRoundsPoolBrackets } from './useDivisionRoundsPoolBrackets';

export const usePoolBrackets = (divisionId: string) => {
	const [queryParams] = useQueryParams({ search: StringParam });

	const [search, setSearch] = useState(queryParams.search || '');
	const onChangeSearch = useCallback((value: string) => {
		setSearch(value);
	}, []);

	const { rounds, roundsShortDays, roundsFullDays, roundsLoading } =
		useDivisionRoundsPoolBrackets(divisionId);

	useEffect(() => {
		setSearch(queryParams.search || '');
	}, [queryParams.search]);

	const sanitizedSearch = sanitizeSearchInput(search).toLowerCase();
	const filteredRounds = useMemo(() => {
		if (!sanitizedSearch) return rounds;
		return filterRounds(rounds, sanitizedSearch);
	}, [rounds, sanitizedSearch]);

	const getRoundsHeadings = useCallback(
		(type: 'short' | 'full') => {
			const roundKey = type === 'short' ? 'short_name' : 'name';
			return rounds.reduce<Map<string, string>>((acc, { uuid, [roundKey]: roundName }) => {
				const days =
					type === 'short' ? roundsShortDays.get(uuid) || [] : roundsFullDays.get(uuid) || [];
				// Displaying the heading using pattern: "Short Name(, Day1&Day2&Day3)"
				acc.set(uuid, [roundName, days.join('&')].join(', '));
				return acc;
			}, new Map());
		},
		[rounds, roundsShortDays, roundsFullDays],
	);

	const roundsShortHeadings = useMemo(() => {
		return getRoundsHeadings('short');
	}, [getRoundsHeadings]);
	const roundsFullHeadings = useMemo(() => {
		return getRoundsHeadings('full');
	}, [getRoundsHeadings]);

	const getPoolBracketIcon = ({ is_pool }: Pick<PoolOrBracketBase, 'is_pool'>) => {
		return is_pool ? poolIcon : bracketIcon;
	};

	const canShowPoolBracketStat = (team: Pick<Team, 'pool_bracket_stat'>) => {
		return !!team.pool_bracket_stat?.points_ratio;
	};

	return {
		search,
		onChangeSearch,
		rounds,
		filteredRounds,
		roundsShortHeadings,
		roundsFullHeadings,
		roundsLoading,
		getPoolBracketIcon,
		canShowPoolBracketStat,
	};
};
