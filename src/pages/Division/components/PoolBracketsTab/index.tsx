import { useAnalytics } from '@/shared/hooks/useAnalytics';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useTeamModal } from '@/shared/hooks/useTeamModal';
import { useMemo } from 'react';
import { StringParam, useQueryParams } from 'use-query-params';

import { StyledNotFound } from '@/styles/shared';

import { AnchorModal } from '@/components/AnchorModal';
import { BracketModal } from '@/components/BracketModal';
import { Loader } from '@/components/Loader';
import { NotFound } from '@/components/NotFound';
import { PoolBracketMatchResult } from '@/components/PoolBracketMatchResult';
import { PoolBracketPointsRatio } from '@/components/PoolBracketPointsRatio';
import { PoolBracketSetsResult } from '@/components/PoolBracketSetsResult';
import { PreviouslyQualifiedInfo } from '@/components/PreviouslyQualifiedInfo';

import { placeholders } from '@/config/searchPlaceholder';

import { getMatchName } from '@/services/teams.service';

import { getFormateTime } from '@/utils/time';

import { POOL_TABS } from '../../constants';
import { DivisionSearchBar } from '../DivisionSearchBar';
import { usePoolBrackets } from './hooks/usePoolBrackets';
import { usePoolBracketsModals } from './hooks/usePoolBracketsModals';
import { useSections } from './hooks/useSections';
import {
	DivisionTabs__AnchorButton,
	DivisionTabs__EmptyValue,
	DivisionTabs__PoolItem,
	DivisionTabs__PoolItemHeader,
	DivisionTabs__PoolItemTeamItem,
	DivisionTabs__PoolItemTeamList,
	DivisionTabs__PoolList,
	DivisionTabs__PoolWrapper,
	DivisionTabs__PreviouslyQualifiedWrapper,
	DivisionTabs__RoundTitle,
	DivisionTabs__ScrollWrapper,
	DivisionTabs__StickyHeader,
	DivisionTabs__TabsWrapper,
	DivisionTabs__TitleWrapper,
} from './styled';

type Props = {
	divisionId: string;
	children: React.ReactNode;
};

export const PoolBracketsTab = ({ divisionId, children }: Props) => {
	const { divisions, loading: eventLoading } = useEventDetails();
	const division = divisions?.find((division) => division.division_id === divisionId);

	const {
		isShowPoolBracketAnchorModal,
		closePoolBracketAnchorModal,
		modalsLoading,
		pools,
		teamPoolData,
		poolOfTeam,
		currentPool,
		closeBracketModal,
		openBracketModal,
		openPoolModalModal,
		openIsPoolModal,
		setOpenIsPoolModal,
		teamRefetch,
		matchesRefetch,
	} = usePoolBracketsModals();

	const {
		search,
		onChangeSearch,
		roundsShortHeadings,
		roundsFullHeadings,
		rounds,
		filteredRounds,
		roundsLoading,
		getPoolBracketIcon,
		canShowPoolBracketStat,
	} = usePoolBrackets(divisionId);

	useAnalytics('Division (Pools/Brackets)', search);

	const isLoading = modalsLoading || roundsLoading || eventLoading;

	const {
		activeSection,
		scrollToSection,
		isFixed,
		mobileWrapper,
		desktopWrapper,
		sectionRefs,
		poolListRef,
	} = useSections({
		roundsStartDates: rounds?.map(({ first_match_start, uuid }) => ({ first_match_start, uuid })),
		divisionId,
	});

	const { browserHeight, breakPont } = useCurrentSize();

	const headingMaxLen = useMemo(() => {
		const headings = Object.values(roundsShortHeadings) as string[];
		return Math.max(...headings.map(({ length }) => length));
	}, [roundsShortHeadings]);

	const { isDesktopFixed } = useDesktopSticky({ page: 'division' });
	const { isDesktopFixed: isDesktopFixedTab } = useDesktopSticky({ page: 'favoritesTabs' });
	const isLarge = breakPont !== 'small';

	const isNothingFound = useMemo(() => {
		// If there is no search, rounds are loading or there are no rounds, return false
		if (!search || roundsLoading || !rounds.length) return false;
		const teamsCount = filteredRounds.reduce((acc, { pool_brackets }) => {
			pool_brackets.forEach(({ teams }) => (acc += teams.length));
			return acc;
		}, 0);
		return !teamsCount;
	}, [search, rounds, filteredRounds, roundsLoading]);

	const [, setQueryParams] = useQueryParams({
		divisionId: StringParam,
		modalTeam: StringParam,
	});
	const { teamModalElement, openTeamModal } = useTeamModal();
	const isShowEarnedBid = false;
	const renderHeadingsList = () => {
		return filteredRounds.map((round, index) => {
			const key = `#anchor${index + 1}`;
			return (
				<DivisionTabs__AnchorButton
					key={key}
					$isActive={activeSection === index}
					$width={headingMaxLen * 10}
					href={key}
					onClick={(e) => {
						e.preventDefault();
						scrollToSection(index);
					}}
				>
					{roundsShortHeadings.get(round.uuid) || ''}
				</DivisionTabs__AnchorButton>
			);
		});
	};

	return (
		<>
			{teamModalElement}
			{currentPool && (
				<BracketModal
					poolOfTeam={currentPool}
					bracketRounds={pools}
					poolId={currentPool.uuid!}
					close={() => {
						closeBracketModal();
						setQueryParams({ divisionId: null });
					}}
				/>
			)}
			<DivisionSearchBar
				placeholder={placeholders.division_poolBracket}
				divisionId={divisionId}
				search={search}
				onChangeSearch={onChangeSearch}
				isFixed={isFixed}
				activeTab={POOL_TABS.POOLS}
			/>
			<DivisionTabs__PoolWrapper
				ref={desktopWrapper}
				$isFixed={isFixed}
				$browserHeight={browserHeight}
				$isDesktopFixed={isDesktopFixed}
			>
				<DivisionTabs__TabsWrapper>{children}</DivisionTabs__TabsWrapper>
				{isLoading && <Loader />}
				{isShowPoolBracketAnchorModal && teamPoolData && (
					// ! Team Modal
					<AnchorModal
						matchesRefetch={matchesRefetch}
						teamRefetch={teamRefetch}
						isPool={openIsPoolModal}
						teamPoolData={teamPoolData}
						close={() => {
							setQueryParams({ divisionId: null, modalTeam: null });
							closePoolBracketAnchorModal();
						}}
						poolOfTeam={poolOfTeam}
					/>
				)}
				{!isLarge && !isNothingFound && (
					<DivisionTabs__StickyHeader $isFixed={isFixed} $isDesktopFixed={isDesktopFixedTab}>
						{renderHeadingsList()}
					</DivisionTabs__StickyHeader>
				)}
				<DivisionTabs__ScrollWrapper
					ref={mobileWrapper}
					$isFixed={isFixed}
					className="scroll-wrapper"
				>
					{isLarge && !isNothingFound && (
						<DivisionTabs__StickyHeader $isFixed={isFixed} $isDesktopFixed={isDesktopFixedTab}>
							{renderHeadingsList()}
						</DivisionTabs__StickyHeader>
					)}
					{!roundsLoading && !rounds.length && (
						<StyledNotFound>No Pools or Brackets available yet.</StyledNotFound>
					)}
					<div ref={poolListRef}>
						{filteredRounds.map((round, index) => {
							return (
								<DivisionTabs__PoolList
									key={`wrapper${index}`}
									id={`anchor${index + 1}`}
									ref={(el) => (sectionRefs.current[index] = el as HTMLUListElement | null)}
									$isDesktopFixed={isDesktopFixedTab}
								>
									<>
										<DivisionTabs__RoundTitle>
											{roundsFullHeadings.get(round.uuid) || ''}
										</DivisionTabs__RoundTitle>
										{round.pool_brackets.map((poolBracket) => {
											return (
												<DivisionTabs__PoolItem key={poolBracket.uuid}>
													<DivisionTabs__PoolItemHeader>
														<p
															onClick={
																!poolBracket.is_pool
																	? () => openBracketModal(poolBracket.uuid)
																	: () => {
																			setOpenIsPoolModal(true);
																			openPoolModalModal(poolBracket.uuid);
																		}
															}
														>
															<strong>
																<img src={getPoolBracketIcon(poolBracket)} alt="" />
																{division?.short_name || ''} {poolBracket.display_name}
															</strong>{' '}
															<span>
																Starts{' '}
																{poolBracket.date_start &&
																	getFormateTime({
																		time: poolBracket.date_start,
																		format: 'EEE, h:mmaaa',
																	})}{' '}
															</span>{' '}
															<span>
																Ct{' '}
																{poolBracket.external.courts_short_info
																	.map((e) => e.short_name)
																	.join(', ') || ''}
															</span>{' '}
															<span>
																{!!poolBracket.is_pool && getMatchName(poolBracket.settings)}
															</span>
														</p>
													</DivisionTabs__PoolItemHeader>
													<DivisionTabs__PoolItemTeamList>
														{!poolBracket.teams.length && (
															<DivisionTabs__EmptyValue>
																Teams are not assigned yet
															</DivisionTabs__EmptyValue>
														)}
														{poolBracket.teams.map((team) => {
															return (
																<DivisionTabs__PoolItemTeamItem
																	key={team.team_id}
																	$isShowStat={canShowPoolBracketStat(team)}
																	onClick={() => {
																		setOpenIsPoolModal(false);
																		openTeamModal(team.team_id);
																		setQueryParams({ divisionId: divisionId });
																	}}
																>
																	<div style={{ position: 'relative' }}>
																		<DivisionTabs__TitleWrapper
																			$extraOffset={
																				!!(
																					team?.extra?.show_accepted_bid &&
																					team?.extra?.show_previously_accepted_bid
																				)
																			}
																		>
																			{team.team_name}
																		</DivisionTabs__TitleWrapper>

																		{team?.extra?.show_previously_accepted_bid && (
																			<DivisionTabs__PreviouslyQualifiedWrapper
																				$position={
																					team?.extra?.show_accepted_bid && isShowEarnedBid ? 2 : 1
																				}
																			>
																				<PreviouslyQualifiedInfo
																					type="previously_accepted_bid"
																					info={team.extra.show_previously_accepted_bid}
																				/>
																			</DivisionTabs__PreviouslyQualifiedWrapper>
																		)}

																		{isShowEarnedBid && team?.extra?.show_accepted_bid && (
																			<DivisionTabs__PreviouslyQualifiedWrapper $position={1}>
																				<PreviouslyQualifiedInfo type="accepted_bid" />
																			</DivisionTabs__PreviouslyQualifiedWrapper>
																		)}
																	</div>

																	{canShowPoolBracketStat(team) ? (
																		<>
																			<div>
																				<PoolBracketMatchResult pbStat={team.pool_bracket_stat} />
																			</div>
																			<div>
																				<PoolBracketSetsResult pbStat={team.pool_bracket_stat} />
																			</div>
																			<div>
																				<PoolBracketPointsRatio pbStat={team.pool_bracket_stat} />
																			</div>
																		</>
																	) : null}
																</DivisionTabs__PoolItemTeamItem>
															);
														})}
													</DivisionTabs__PoolItemTeamList>
												</DivisionTabs__PoolItem>
											);
										})}
									</>
								</DivisionTabs__PoolList>
							);
						})}
						{isNothingFound && <NotFound type="pool-brackets" />}
					</div>
				</DivisionTabs__ScrollWrapper>
			</DivisionTabs__PoolWrapper>
		</>
	);
};
