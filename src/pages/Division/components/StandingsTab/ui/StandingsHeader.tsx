import { StandingsHeader__Item, StandingsHeader__List, StandingsHeader__Wrapper } from '../styled';

type Props = {
	isContainsPoints: boolean;
};
export const StandingsHeader = ({ isContainsPoints }: Props) => {
	return (
		<StandingsHeader__Wrapper>
			<StandingsHeader__List>
				<StandingsHeader__Item $withPoints={isContainsPoints}>F. S.</StandingsHeader__Item>
				{isContainsPoints && (
					<StandingsHeader__Item $withPoints={isContainsPoints}>Points</StandingsHeader__Item>
				)}
				<StandingsHeader__Item $withPoints={isContainsPoints}>Team</StandingsHeader__Item>
				<StandingsHeader__Item $withPoints={isContainsPoints}>Match W-L</StandingsHeader__Item>
				<StandingsHeader__Item $withPoints={isContainsPoints}>Set W-L</StandingsHeader__Item>
				<StandingsHeader__Item $withPoints={isContainsPoints}>Point%</StandingsHeader__Item>
				<StandingsHeader__Item $withPoints={isContainsPoints}>Code</StandingsHeader__Item>
			</StandingsHeader__List>
		</StandingsHeader__Wrapper>
	);
};
