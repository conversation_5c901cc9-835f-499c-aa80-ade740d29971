import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { DivisionTeamStanding } from '@/shared/types/team.types';
import { sanitizeSearchInput } from '@/utils';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { StringParam, useQueryParams } from 'use-query-params';

import { UNKNOWN_STANDING_GROUP } from '@/pages/Favorites/constants';

import { useDivisionTeamsStanding } from '../../../hooks/useDivisionTeamsStanding';

type StandingGroup = {
	heading: string;
	teams: DivisionTeamStanding[];
};

export const useStandings = (divisionId: string) => {
	const { event } = useEventDetails();
	const hideSeeds = !!event?.hide_seeds;
	const [queryParams] = useQueryParams({ search: StringParam });

	const [search, setSearch] = useState(queryParams.search || '');
	const onChangeSearch = useCallback((value: string) => {
		setSearch(value);
	}, []);

	const { data, loading, hasRanks } = useDivisionTeamsStanding(divisionId);

	useEffect(() => {
		setSearch(queryParams.search || '');
	}, [queryParams.search]);

	const sanitizedSearch = sanitizeSearchInput(search).toLowerCase();
	const [standings, headings]: [StandingGroup[], string[]] = useMemo(() => {
		if (!data) return [[], []];
		// Using map to ensure the order of the headings
		const headings = new Set<string>();
		const headingTeams = new Map<string, DivisionTeamStanding[]>();
		data.divisionTeamsStanding.forEach((team) => {
			let heading = team.division_standing?.heading;
			if (!heading) return;
			heading = heading === UNKNOWN_STANDING_GROUP ? 'Pending' : heading;
			headings.add(heading);
			if (sanitizedSearch && !team.team_name.toLowerCase().includes(sanitizedSearch)) return;
			if (!headingTeams.has(heading)) {
				headingTeams.set(heading, []);
			}
			headingTeams.get(heading)!.push(team);
		});
		return [
			Array.from(headingTeams.entries()).map(([heading, teams]) => ({
				heading,
				teams: teams.sort((a, b) => {
					if (hasRanks) {
						// If the teams have ranks, sort by rank
						const rankDiff = (a.division_standing?.rank || 0) - (b.division_standing?.rank || 0);
						if (rankDiff !== 0) return rankDiff;
					} else if (!hideSeeds) {
						// If the teams don't have ranks and seeds are not hidden, sort by seed original
						const rankDiff = (a.division_standing?.seed || 0) - (b.division_standing?.seed || 0);
						if (rankDiff !== 0) return rankDiff;
					}
					// Otherwise, sort by team name
					return a.team_name.localeCompare(b.team_name);
				}),
			})),
			Array.from(headings),
		];
	}, [sanitizedSearch, data, hasRanks, hideSeeds]);

	const isContainsPoints = useMemo(() => {
		return !!standings
			.map((standing) => {
				return standing.teams
					.map((team) => {
						return team.division_standing?.points;
					})
					.filter(Boolean);
			})
			.flat().length;
	}, [standings]);
	return {
		search,
		onChangeSearch,
		headings,
		standings,
		standingsLoading: loading,
		hasRanks,
		isContainsPoints,
	};
};
