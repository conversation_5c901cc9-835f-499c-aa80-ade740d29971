import styled from 'styled-components';

import { ThemeT } from '@/styles/theme';

export const StandingsTab__StandingContainer = styled.div`
	margin: 0 0 16px;
`;
export const StandingsTab__StandingTitle = styled.p<ThemeT>`
	font-size: 14px;
	font-weight: 700;
	line-height: 18px;
	color: ${(props) => props.theme.colors.blue};
	margin: 0 0 8px;
	border-bottom: 2px solid ${(props) => props.theme.colors.blue};
	padding-bottom: 11px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 18px;
		font-weight: 700;
		line-height: 28px;
	}
`;
export const StandingsTab__StandingList = styled.ul`
	background: #fff;
	padding: 16px 8px;
	margin: 0 0 16px;
`;
export const StandingsTab__StandingListItem = styled.li`
	margin: 0 0 30px;
	position: relative;
	&:last-child {
		margin: 0;
	}
`;
export const StandingsTab__PreviouslyQualifiedWrapper = styled.section<{ $position: number }>`
	position: absolute;
	left: ${({ $position }) => ($position === 1 ? 'calc(100% - 30px)' : 'calc(100% - 55px)')};
	top: 0;
	z-index: 2;
`;
export const StandingsTab__StandingListItemBox = styled.div<{ $extraOffset?: boolean }>`
	font-size: 14px;
	font-weight: 400;
	line-height: 18px;
	margin: 0 20px 0 0;
	&:nth-child(1) {
		min-width: 40px !important;
	}
	&:nth-child(2) {
		min-width: 50px !important;
		padding: ${({ $extraOffset }) => ($extraOffset ? '0 60px 0 0' : '0 35px 0 0')};
		margin: 0;
	}
	&:nth-child(3) {
		min-width: 80px !important;
	}
	&:nth-child(4) {
		min-width: 50px !important;
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
		line-height: 24px;
	}
	span {
		cursor: pointer;
		&:hover {
			text-decoration: underline;
		}
	}
`;
export const StandingsTab__ScrollWrapper = styled.div<{
	$isFixed?: boolean;
}>`
	position: fixed;
	width: 100%;
	height: 100%;
	background: #fff;
	outline: none;
	top: ${({ $isFixed }) => ($isFixed ? 190 : 220)}px;
	overflow-y: scroll;
	padding-bottom: 220px;
	padding-top: ${({ $isFixed }) => ($isFixed ? 40 : 0)}px;
	padding-left: 16px;
	padding-right: 16px;

	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		position: static;
		overflow-y: unset;
		width: 600px;
		margin: auto;
		padding: 0 0 16px;
	}
`;
export const StandingsTab__Wrapper = styled.div<{
	$browserHeight: number;
	$isDesktopFixed: boolean;
}>`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 170px 24px 24px;
		width: 680px;
		min-width: 648px;
		margin: auto;
		margin-top: ${(props) => (props.$isDesktopFixed ? 260 : -48)}px;
		border-radius: 4px;
		background: #fff;
		min-height: ${(props) => props.$browserHeight - 304}px;
		box-shadow:
			0px 12px 24px -4px rgba(145, 158, 171, 0.12),
			0px 0px 2px 0px rgba(145, 158, 171, 0.2);
		position: fixed;
		left: 50%;
		margin: -48px 0 0 -340px;
		z-index: 99;
		top: 240px;
		overflow-y: auto;
		height: calc(100vh - 246px);
	}
`;
export const StandingsTab__ContainerList = styled.div`
	display: flex;
	justify-content: space-between;
	ul {
		&:first-child {
			width: 60%;
			@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
				width: 41%;
			}
			li {
				div {
					&:last-child {
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
				}
			}
		}
		&:last-child {
			white-space: nowrap;
			overflow-x: scroll;
			&::-webkit-scrollbar {
				display: none;
			}
			li {
				div {
					text-align: left;
					&:first-child {
						font-weight: 700;
					}

					&:nth-child(2) {
						font-weight: 700;
						i {
							font-style: normal;
							font-weight: 400;
						}
					}
					&:nth-child(3) {
						min-width: 40px;
					}
				}
			}
		}
		li {
			display: flex;
		}
	}
`;
export const StandingsTabs__TabsWrapper = styled.div`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		width: 600px;
		margin: auto;
	}
`;
export const StandingsTabs__ContentWrapper = styled.div<{ $isDesktopFixed: boolean }>`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding-top: ${(props) => (props.$isDesktopFixed ? 10 : 0)}px;
	}
`;
export const StandingsHeader__Wrapper = styled.div<{ theme: ThemeT }>`
	position: fixed;
	z-index: 4;
	width: 616px;
	top: 318px;
	height: 32px;
	background: ${(props) => props.theme.colors.blue};
	border-radius: 2px;
	padding: 7px 14px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
		top: 206px;
		display: none;
	}
`;

export const StandingsHeader__List = styled.ul<{ theme: ThemeT }>`
	display: flex;
	align-items: center;
	height: 100%;
`;

export const StandingsHeader__Item = styled.li<{ theme: ThemeT; $withPoints: boolean }>`
	color: #fff;
	font-size: 14px;
	line-height: 18px;
	&:first-child {
		width: 40px;
	}
	&:nth-child(2) {
		width: ${({ $withPoints }) => ($withPoints ? 60 : 165)}px;
	}
	&:nth-child(3) {
		width: ${({ $withPoints }) => ($withPoints ? 185 : 120)}px;
	}
	&:nth-child(4) {
		width: 115px;
	}
	&:nth-child(5) {
		width: ${({ $withPoints }) => ($withPoints ? 85 : 75)}px;
	}
	&:nth-child(6) {
		width: 62px;
	}
`;
