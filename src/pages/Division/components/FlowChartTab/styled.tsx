import styled from 'styled-components';

export const StyledFlowChartTabWrapper = styled.div`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 70px 24px 24px;
		width: 680px;
		min-width: 648px;
		margin: auto;
		border-radius: 4px;
		background: #fff;
		box-shadow:
			0px 12px 24px -4px rgba(145, 158, 171, 0.12),
			0px 0px 2px 0px rgba(145, 158, 171, 0.2);
		position: fixed;
		left: 50%;
		margin: -48px 0 0 -340px;
		z-index: 99;
		top: 240px;
		overflow-y: auto;
		height: calc(100vh - 246px);
	}
`;

export const StyledFlowChartTabsWrapper = styled.div`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		width: 600px;
		margin: auto;
	}
`;

export const StyledFlowChartImg = styled.img`
	width: 100%;
	height: auto;
`;
