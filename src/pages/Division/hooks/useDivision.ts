import { TeamSettings_SortBy } from '@/config';
import { useUpcomingMappedMatches } from '@/shared/hooks/useUpcommingMappedMatches';
import { ClubsWithTeamsTeam } from '@/shared/types/clubWithTeams.types';
import { useCallback, useEffect, useState } from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import { useEventForDivisionPageQuery, usePoolByTeamIdLazyQuery } from '@/generated/graphql';

import { SearchService } from '@/utils/search.service';

export const useDivision = () => {
	const searchService = new SearchService();
	const params = useParams();
	const navigate = useNavigate();
	const { pathname } = useLocation();
	const [selectedDivision, setSelectedDivision] = useState<number | string>('');
	const [search, setSearch] = useState('');

	const { data: eventData, loading: eventLoading } = useEventForDivisionPageQuery({
		variables: {
			id: params.id!,
		},
	});

	const divisionOptions = eventData?.event?.divisions.map((division) => ({
		label: division.short_name,
		value: division.division_id,
	}));

	useEffect(() => {
		if (divisionOptions && !selectedDivision) {
			const selectedOption = divisionOptions.find((item) => item?.value === +params.divisionId!);
			if (selectedOption) {
				setSelectedDivision(selectedOption?.value || divisionOptions[0].value!);
			}
		}
	}, [divisionOptions, params.divisionId, selectedDivision]);

	useEffect(() => {
		if (selectedDivision) {
			const path = `/events/${params.id}/divisions/${selectedDivision}/${
				pathname.split('/')[pathname.split('/').length - 1]
			}`;

			if (path !== pathname) {
				navigate(path);
			}
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [selectedDivision]);

	const { divisionTeams, loading: upcomingMatchesLoading } = useUpcomingMappedMatches({
		eventId: params.id!,
		divisionId: +params.divisionId!,
	});
	const filterDivisionTeamsBySearch = (divisionTeams: ClubsWithTeamsTeam[]) => {
		return searchService.performSearch(
			{ page: 'division-teams', data: divisionTeams },
			search,
		) as ClubsWithTeamsTeam[];
	};

	const [isShowAnchorModal, setIsAnchorModal] = useState(false);
	const closeAnchorModal = () => {
		setIsAnchorModal(false);
	};

	const [getPoolByTeamId, { data: poolData, loading: poolDataLoading }] =
		usePoolByTeamIdLazyQuery();
	const openAnchorModal = useCallback(
		(teamId: number) => {
			getPoolByTeamId({
				variables: {
					id: params.id!,
					teamId: `${teamId}`,
				},
				onCompleted: () => {
					setIsAnchorModal(true);
				},
			});
		},
		[getPoolByTeamId, params.id],
	);

	const renameRoundName = (roundName: string) => {
		let newName = roundName;

		if (newName.toLowerCase().includes('round')) {
			const roundNumber = newName.split(' ')[1];
			newName = `R${Number(roundNumber)}`;
		}
		return newName;
	};

	const sortByName = (a: ClubsWithTeamsTeam, b: ClubsWithTeamsTeam) => {
		return a.team_name.localeCompare(b.team_name);
	};

	return {
		eventName: eventData?.event?.long_name,
		isShowBuyAdmission: !!eventData?.event?.tickets_published,
		isAssignedTickets: !!eventData?.event?.is_require_recipient_name_for_each_ticket,
		tickets_code: eventData?.event?.tickets_code || '',
		loading: eventLoading || upcomingMatchesLoading || poolDataLoading,
		divisionOptions,
		selectedDivision,
		setSelectedDivision,
		search,
		setSearch,
		eventId: params.id!,
		divisionTeams: filterDivisionTeamsBySearch(divisionTeams).sort(sortByName),
		isShowAnchorModal,
		closeAnchorModal,
		poolData,
		openAnchorModal,
		renameRoundName,
		isHideSeeds: eventData?.event?.hide_seeds,
		isHideStandings: eventData?.event?.teams_settings?.hide_standings,
		sortBy: eventData?.event?.teams_settings?.sort_by || ('team_name' as TeamSettings_SortBy),
	};
};
