import styled from 'styled-components';

export const ScheduleCard__Wrapper = styled.div<{ $isActive: boolean; $isBlur: boolean }>`
	min-width: 150px;
	border-radius: 2px;
	background-color: #fff;
	border: ${(props) => props.$isActive && '1px solid' + '#00AB55'};
	opacity: ${(props) => (props.$isBlur ? 0.2 : 1)};
	box-shadow:
		0px 0px 2px rgba(145, 158, 171, 0.2),
		0px 12px 24px -4px rgba(145, 158, 171, 0.12);
`;
export const ScheduleCard__Header = styled.header`
	font-size: 12px;
	line-height: 18px;
	font-weight: 700;
	border-radius: 2px 2px 0 0;
	background-color: #f4f6f8;
	border: 0.1px solid #dfe3e8;
	padding: 4px 8px;
`;
export const ScheduleCard__Footer = styled.footer`
	font-size: 8px;
	line-height: 18px;
	color: #637381;
	text-align: right;
	padding: 0 8px 3px;
`;
export const ScheduleCard__List = styled.ul`
	padding: 10px 8px 0;
`;
export const ScheduleCard__Item = styled.li`
	display: flex;
	justify-content: space-between;
	p {
		margin: 0 0 4px;
		font-size: 10px;
		&:first-child {
			width: 110px;
			font-size: 10px;
			margin: 0 0 4px;
			text-overflow: ellipsis;
			overflow: hidden;
			white-space: nowrap;
		}
	}
`;
export const ScheduleCard__ItemTimeWrapper = styled.div`
	display: flex;
	align-items: center;
	gap: 3px;
	justify-content: flex-end;
	font-size: 10px;
	line-height: 18px;
`;
export const ScheduleCard__ItemTimeRef = styled.div`
	display: flex;
	justify-content: flex-start;
	color: #919eab;
	font-size: 10px;
	line-height: 18px;
`;
