import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';
import buyAdmissionIcon from '@assets/buy-admission-icon.svg';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Link, useParams } from 'react-router-dom';

import { Select } from '@/components/CustomSelect';
import { MainNav } from '@/components/MainNav';
import { SearchBar__EventTitle } from '@/components/SearchBar/styled';
import { SearchButton } from '@/components/SearchButton';

// import { TimeRange } from '@/components/TimeRange';
import { CourtMatchesDivision, CourtMatchesTime } from '@/generated/graphql';

import {
	getGridFilterStorageByEventId,
	initGridFilterStorage,
	setGridFilterStorage,
} from '@/utils/gridFilterStorage';
import { formatDays, setGridDayToStorage } from '@/utils/time';

import { ICourtFilter, TimeResultT } from '../../types';
import {
	SCROLL_OFFSET_TIME_RANGE,
	TimeRangeSchedule__BuyAdmission,
	TimeRangeSchedule__EventTitleDescriptionWrapper,
	TimeRangeSchedule__Footer,
	TimeRangeSchedule__FooterWrapper,
	TimeRangeSchedule__Header,
	TimeRangeSchedule__MainNavWrapper,
	TimeRangeSchedule__TimeRangeWrapper,
	TimeRangeSchedule__Wrapper,
} from './styled';

type PropsT = {
	COURT_GRID_DURATION?: number;
	ticketLink: string;
	isShowBuyAdmission?: boolean;
	days: string[];
	hours: CourtMatchesTime[];
	divisions: CourtMatchesDivision[];
	courtFilter: ICourtFilter;
	setCourtFilter: Dispatch<SetStateAction<ICourtFilter>>;
	setSearch: Dispatch<SetStateAction<string>>;
	search: string;
	eventName: string;
	eventId?: string | null;
	timeResult: TimeResultT;
	setTimeResult: Dispatch<SetStateAction<TimeResultT>>;
	countOfSelectedMatches: number;
	scrollToElement: (_type: 'prev' | 'next') => void;
	currentMatchIndex: number;
	resetCurrentMatchIndex: () => void;
	description?: null | (() => JSX.Element);
};
export const TimeRangeSchedule = ({
	days,
	hours,
	divisions,
	courtFilter,
	setCourtFilter,
	search,
	setSearch,
	eventId,
	timeResult,
	setTimeResult,
	countOfSelectedMatches,
	scrollToElement,
	currentMatchIndex,
	resetCurrentMatchIndex,
	isShowBuyAdmission,
	ticketLink,
	eventName,
	COURT_GRID_DURATION = 3,
}: PropsT) => {
	const [isTimeRange] = useState(true);
	const divisionOptions = divisions.map((d) => ({
		key: d.division_name,
		value: d.division_id?.toString(),
	}));
	divisionOptions.unshift({ key: 'All Divisions', value: '0' });
	const { id } = useParams();

	const changeDay = (newValue: string | number | null) => {
		newValue && eventId && setGridDayToStorage(eventId, newValue.toString());
		newValue && id && setGridFilterStorage({ eventId: id, date: newValue });

		newValue && setCourtFilter({ ...courtFilter, day: newValue.toString() });
	};
	const changeDivision = (newValue: string | number | null) => {
		newValue && setCourtFilter({ ...courtFilter, division: newValue.toString() });
		newValue && id && setGridFilterStorage({ eventId: id, divisionId: newValue });
	};

	const selectedDivision = divisionOptions.find((d) => d.value === courtFilter.division)?.value;

	useEffect(() => {
		initGridFilterStorage(eventId!);
		const filterStorage = getGridFilterStorageByEventId(id!);

		if (!filterStorage) {
			return;
		}

		// * Set default values on the first render from storage if exist
		if (filterStorage.date) {
			courtFilter.day = filterStorage.date;
		}

		if (filterStorage.startTime && filterStorage.endTime) {
			timeResult.start = filterStorage.startTime;
			timeResult.end = filterStorage.endTime;
		}
		if (filterStorage.divisionId) {
			courtFilter.division = filterStorage.divisionId;
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	// const getEndDate = () => {
	// 	return hours
	// 		.map((h) => {
	// 			if (h.time) {
	// 				const value = h.time + 1;
	// 				if (value > 12) {
	// 					return `${value - 12} PM`;
	// 				}
	// 				if (value === 12) {
	// 					return `${value} PM`;
	// 				}
	// 				return `${value} AM`;
	// 			}
	// 		})
	// 		.filter(Boolean);
	// };

	const onChangeTime = (value: string | number) => {
		const filteredHours = hours.filter((h) => h.time);
		const index = filteredHours.findIndex((h) => h.time12 === value);
		const startValue = filteredHours[index];
		const endValue =
			filteredHours[index + COURT_GRID_DURATION] || filteredHours[filteredHours.length - 1];
		setTimeResult({
			start: startValue.time12!,
			end: endValue.time12!,
		});
	};
	const getTimeOptions = () => {
		return hours
			.map((h) => {
				if (h.time12) {
					return { key: h.time12, value: h.time12 };
				}
			})
			.filter(Boolean) as Record<string, string>[];
	};

	const [isFixed, setIsFixed] = useState(window.scrollY > SCROLL_OFFSET_TIME_RANGE);

	// TODO move to share
	useEffect(() => {
		const handleScroll = () => {
			const newScrollTop = window.scrollY || document.documentElement.scrollTop;
			setIsFixed(newScrollTop > SCROLL_OFFSET_TIME_RANGE);
		};

		window.addEventListener('scroll', handleScroll);

		return () => {
			window.removeEventListener('scroll', handleScroll);
		};
	}, []);
	const { breakPont } = useCurrentSize();
	const isLarge = breakPont !== 'small';
	const { isDesktopFixed } = useDesktopSticky({ page: 'courtGrid' });
	return (
		<>
			<TimeRangeSchedule__Wrapper $isLarge={isLarge} className={isDesktopFixed ? 'sticky' : ''}>
				<TimeRangeSchedule__Header
					className={isLarge ? 'sticky-hide' : isDesktopFixed ? 'sticky-hide' : ''}
				>
					<TimeRangeSchedule__EventTitleDescriptionWrapper>
						{breakPont === 'small' && (
							<SearchBar__EventTitle>
								<Link to={`/events/${id}`}>{eventName}</Link>
							</SearchBar__EventTitle>
						)}
					</TimeRangeSchedule__EventTitleDescriptionWrapper>
					{isShowBuyAdmission && (
						<TimeRangeSchedule__BuyAdmission href={ticketLink} target="_blank">
							<img src={buyAdmissionIcon} alt="buy admission" /> Buy admission
						</TimeRangeSchedule__BuyAdmission>
					)}
				</TimeRangeSchedule__Header>
				{isLarge && (
					<TimeRangeSchedule__MainNavWrapper>
						<MainNav />
						{isShowBuyAdmission && (
							<TimeRangeSchedule__BuyAdmission href={ticketLink} target="_blank">
								<img src={buyAdmissionIcon} alt="buy admission" /> Buy admission
							</TimeRangeSchedule__BuyAdmission>
						)}
					</TimeRangeSchedule__MainNavWrapper>
				)}
				<TimeRangeSchedule__FooterWrapper>
					<TimeRangeSchedule__Footer $isFixed={isFixed}>
						<Select
							width={90}
							value={courtFilter.day}
							options={days.map((d) => ({ key: formatDays(d), value: d }))}
							onChange={changeDay}
						/>
						<TimeRangeSchedule__TimeRangeWrapper>
							{isTimeRange ? (
								// <TimeRange
								// 	timeResult={timeResult}
								// 	setTimeResult={setTimeResult}
								// 	startRange={hours.map((h) => h.time12).filter(Boolean) as string[]}
								// 	endRange={getEndDate() as string[]}
								// />
								<></>
							) : (
								<Select
									width={90}
									value={timeResult.start}
									options={getTimeOptions()}
									onChange={onChangeTime}
								/>
							)}
						</TimeRangeSchedule__TimeRangeWrapper>
						<Select
							width={83}
							value={selectedDivision}
							options={divisionOptions as Record<string, string>[]}
							onChange={changeDivision}
						/>
						<SearchButton
							onClose={resetCurrentMatchIndex}
							currentResult={currentMatchIndex}
							search={search}
							setSearch={setSearch}
							resultsCount={countOfSelectedMatches}
							navigateResults={scrollToElement}
						/>
					</TimeRangeSchedule__Footer>
				</TimeRangeSchedule__FooterWrapper>
			</TimeRangeSchedule__Wrapper>
		</>
	);
};
