import styled from 'styled-components';

import TableCell from '@mui/material/TableCell';

export const TableSchedule__TableWrapper = styled.div<{ $isFixed: boolean }>`
	//TODO
	padding: ${({ $isFixed }) => ($isFixed ? '47px 16px 16px 16px' : '10px 16px')};
	table {
		border: none !important;
	}
`;
export const TableSchedule__Wrapper = styled.div`
	min-width: 56px;
	max-width: 70px;
	font-size: 12px;
	text-decoration: underline;
	line-height: 18px;
	font-weight: 700;
	overflow: hidden;
	display: inline-block;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding: 0 8px 0 0;
`;
export const TableSchedule__RangeCell = styled(TableCell)`
	padding: 0 !important;
	border: none !important;
	vertical-align: top !important;
`;
export const TableSchedule__RangeCellWrapper = styled.div`
	margin-bottom: 16px;
`;
export const TableSchedule__RangeCellContainer = styled.div`
	border-radius: 2px;
	background-color: #f4f6f8;
	font-size: 12px !important;
	line-height: 18px !important;
	text-align: center !important;
	font-weight: 700 !important;
	border: none !important;
	padding: 11px !important;
	position: relative;
	img {
		position: absolute;
		transition: all 0.3s;
		cursor: pointer;
		opacity: 1;
		&:hover {
			opacity: 0.3;
		}
	}
`;
export const TableSchedule__Cell = styled(TableCell)`
	border: none !important;
	padding: 0 8px 8px 0 !important;
	vertical-align: top !important;
	&:last-child {
		padding-right: 0 !important;
	}
`;
export const TableSchedule__CellCourt = styled(TableCell)`
	border: none !important;
	padding: 0 !important;
	vertical-align: top !important;
`;
export const TableSchedule__NextButton = styled.img`
	top: 50%;
	right: 8px;
	transform: translateY(-50%);
`;
export const TableSchedule__PrevButton = styled.img`
	top: 50%;
	left: 8px;
	transform: translateY(-50%);
`;
