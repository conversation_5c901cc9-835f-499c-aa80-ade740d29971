import { useAnalytics } from '@/shared/hooks/useAnalytics';

import { Loader } from '@/components/Loader';

import { TableSchedule } from './components/TableSchedule';
import { TimeRangeSchedule } from './components/TimeRangeSchedule';
import { useSchedule } from './hooks/useSchedule';
import { Schedule__Wrapper } from './styled';

export const Schedule = () => {
	const {
		hours,
		divisions,
		courts,
		days,
		isLoading,
		courtFilter,
		setCourtFilter,
		directionHandler,
		setSelectedCourts,
		search,
		setSearch,
		eventName,
		timeResult,
		setTimeResult,
		isShowNextButton,
		isShowPrevButton,
		countOfSelectedMatches,
		scrollToElement,
		currentMatchIndex,
		resetCurrentMatchIndex,
		currentSelectedMatchId,
		activeCourts,
		resetFilters,
		isShowBuyAdmission,
		ticketLink,
	} = useSchedule();

	useAnalytics('Schedule', search);

	return (
		<Schedule__Wrapper>
			{isLoading && <Loader />}
			{hours && days && divisions && courts && !!Number(courtFilter.hour) && (
				<>
					<TimeRangeSchedule
						isShowBuyAdmission={isShowBuyAdmission}
						timeResult={timeResult}
						setTimeResult={setTimeResult}
						eventName={eventName}
						search={search}
						setSearch={setSearch}
						days={days}
						hours={hours}
						divisions={divisions}
						courtFilter={courtFilter}
						setCourtFilter={setCourtFilter}
						countOfSelectedMatches={countOfSelectedMatches}
						scrollToElement={scrollToElement}
						currentMatchIndex={currentMatchIndex}
						resetCurrentMatchIndex={resetCurrentMatchIndex}
						ticketLink={ticketLink}
					/>
					<TableSchedule
						resetFilters={resetFilters}
						activeCourts={activeCourts}
						currentSelectedMatchId={currentSelectedMatchId}
						isShowNextButton={isShowNextButton}
						isShowPrevButton={isShowPrevButton}
						courts={courts}
						directionHandler={directionHandler}
						setSelectedCourts={setSelectedCourts}
					/>
				</>
			)}
		</Schedule__Wrapper>
	);
};
