import { getTicketsDirectLink } from '@/utils';
import { useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import {
	CourtMatchesCourt,
	CourtMatchesTime,
	useCourtMatchesQuery,
	useDaysOfEventQuery,
	useEventQuery,
} from '@/generated/graphql';

import { getDifferenceInHours } from '@/utils/time';

import { DirectionT, ICourtFilter } from '../types';

const COURT_GRID_DURATION = 3;

const INITIAL_STATE = {
	day: '',
	hour: '0',
	court: '',
	duration: `${COURT_GRID_DURATION}`,
	division: '0',
};
export const useSchedule = () => {
	const params = useParams();
	const [selectedCourts, setSelectedCourts] = useState<string[]>([]);
	const [search, setSearch] = useState('');
	const [courtMatchesTime, setCourtsMatchesTime] = useState<CourtMatchesTime[]>([]);
	const [timeResult, setTimeResult] = useState({
		start: '',
		end: '',
	});

	const [isShowNextButton, setShowNextButton] = useState(true);
	const [isShowPrevButton, setShowPrevButton] = useState(false);

	const [courtFilter, setCourtFilter] = useState<ICourtFilter>({
		...INITIAL_STATE,
	});

	const { data: eventDays, loading: loadingEventDays } = useDaysOfEventQuery({
		variables: {
			id: `${params.id}`,
		},
	});

	const { data: eventData, loading: loadingEvent } = useEventQuery({
		variables: {
			id: params.id!,
		},
	});

	const days = eventDays?.event?.days;

	useEffect(() => {
		if (days) {
			setCourtFilter((prev) => ({
				...prev,
				day: `${days[days.length - 1]}`,
			}));
		}
	}, [days]);

	useEffect(() => {
		if (
			courtMatchesTime.length &&
			courtMatchesTime[COURT_GRID_DURATION]?.time12 &&
			!timeResult.start &&
			!timeResult.end
		) {
			setTimeResult((prev) => ({
				...prev,
				start: courtMatchesTime[0].time12!,
				end: courtMatchesTime[COURT_GRID_DURATION].time12!,
			}));
		}
	}, [courtMatchesTime, timeResult.end, timeResult.start]);

	const duration = getDifferenceInHours(timeResult.start, timeResult.end);

	const { data, loading: loadingCourtMatches } = useCourtMatchesQuery({
		variables: {
			id: `${params.id}`,
			day: courtFilter.day,
			hour: courtFilter.hour,
			hours: duration ? `${duration}` : `${courtFilter.duration}`,
			division: courtFilter.division,
		},
		onCompleted: (data) => {
			data?.courtMatches.hours && setCourtsMatchesTime(data.courtMatches.hours);
		},
		skip: !courtFilter.day,
	});

	const hours = data?.courtMatches.hours;
	const divisions = data?.courtMatches.divisions;
	const courts = data?.courtMatches.courts;

	const isLoading = loadingCourtMatches || loadingEventDays || loadingEvent;

	const directionHandler = ({ type }: DirectionT) => {
		if (hours) {
			const currentStartIndex = hours.findIndex((h) => h.time12 === timeResult.start);
			const currentEndIndex = hours.findIndex((h) => h.time12 === timeResult.end);
			if (type === 'next') {
				const nextTimeStart = hours[currentStartIndex + 1];
				const nextTimeEnd = hours[currentEndIndex + 1];
				if (nextTimeEnd?.time) {
					setTimeResult((prev) => ({
						...prev,
						start: `${nextTimeStart.time12}`,
						end: `${nextTimeEnd.time12}`,
					}));
				}
			}
			if (type === 'prev') {
				const prevStartTime = hours[currentStartIndex - 1];
				const prevEndTime = hours[currentEndIndex - 1];
				if (prevStartTime?.time) {
					setTimeResult((prev) => ({
						...prev,
						start: `${prevStartTime.time12}`,
						end: `${prevEndTime.time12}`,
					}));
				}
			}
		}
	};

	useEffect(() => {
		if (hours) {
			const currentEndIndex = hours.findIndex((h) => h.time12 === timeResult.end);
			const nextTimeEnd = hours[currentEndIndex + 1];

			const currentStartIndex = hours.findIndex((h) => h.time12 === timeResult.start);
			const prevStartTime = hours[currentStartIndex - 1];

			if (hours[currentStartIndex]) {
				setCourtFilter((prev) => ({
					...prev,
					hour: `${hours[currentStartIndex].time}`,
				}));
			}

			if (nextTimeEnd?.time) {
				setShowNextButton(true);
			} else {
				setShowNextButton(false);
			}

			if (prevStartTime?.time) {
				setShowPrevButton(true);
			} else {
				setShowPrevButton(false);
			}
		}
	}, [courtFilter.hour, hours, timeResult]);

	const filterCourts = (courts?: CourtMatchesCourt[] | null) => {
		if (courts) {
			const isShowAllCourts = !selectedCourts.length;

			if (isShowAllCourts) {
				return courts;
			}
			return courts.filter((c) => selectedCourts.includes(c.court_id!));
		}

		return courts;
	};

	const [currentMatchIndex, setCurrentMatchIndex] = useState(-1);

	const getSelectedMatchesId = () => {
		if (!search) {
			return [];
		}
		return filterCourts(courts)
			?.map((c) => {
				return c.matches?.map((m) => {
					return { ...m };
				});
			})
			.flat()
			.filter((m) => {
				return (
					m?.team_1_name?.toLowerCase().includes(search.toLowerCase()) ||
					m?.team_2_name?.toLowerCase().includes(search.toLowerCase())
				);
			})
			.map((m) => m?.match_id);
	};

	const selectedMatches = getSelectedMatchesId() as string[];

	const scrollToElement = (type: 'prev' | 'next') => {
		setCurrentMatchIndex((prev) => {
			let id = '';
			let index = prev;
			if (type === 'prev' && selectedMatches[prev - 1]) {
				index = prev - 1;
			}

			if (type === 'next' && selectedMatches[prev + 1]) {
				index = prev + 1;
			}

			id = selectedMatches[index];

			if (id) {
				const element = document.getElementById(id);
				if (element) {
					element.scrollIntoView({
						behavior: 'instant',
						block: 'center',
						inline: 'center',
					});
				}
			}
			return index;
		});
	};

	const resetFilters = useCallback(() => {
		setSearch('');
		setTimeResult({
			start: courtMatchesTime[0].time12!,
			end: courtMatchesTime[COURT_GRID_DURATION].time12!,
		});
		setCourtFilter({
			...courtFilter,
			division: '0',
			day: `${days?.[days.length - 1]}`,
		});
	}, [courtFilter, courtMatchesTime, days]);

	const resetCurrentMatchIndex = () => setCurrentMatchIndex(-1);
	const activeCourts = courts?.filter((c) => c.matches && c.matches.length > 0) || [];

	return {
		days,
		hours,
		divisions,
		isLoading,
		courtFilter,
		setCourtFilter,
		directionHandler,
		setSelectedCourts,
		courts: filterCourts(courts),
		search,
		setSearch,
		eventName: eventData?.event?.long_name || '',
		ticketLink: getTicketsDirectLink(eventData?.event),
		isShowBuyAdmission: !!eventData?.event?.tickets_published,
		timeResult,
		setTimeResult,
		isShowNextButton,
		isShowPrevButton,
		countOfSelectedMatches: selectedMatches.length,
		scrollToElement,
		currentMatchIndex,
		resetCurrentMatchIndex,
		currentSelectedMatchId: selectedMatches[currentMatchIndex] || null,
		activeCourts,
		resetFilters,
	};
};
