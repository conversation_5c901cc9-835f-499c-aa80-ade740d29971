import { useEffect, useState } from 'react';

import { SCROLL_OFFSET_TIME_RANGE } from '../components/TimeRangeSchedule/styled';

export const useStickySearch = () => {
	const [isFixed, setIsFixed] = useState(window.scrollY > SCROLL_OFFSET_TIME_RANGE);

	useEffect(() => {
		const handleScroll = () => {
			const newScrollTop = window.scrollY || document.documentElement.scrollTop;
			setIsFixed(newScrollTop > SCROLL_OFFSET_TIME_RANGE);
		};

		window.addEventListener('scroll', handleScroll);

		return () => {
			window.removeEventListener('scroll', handleScroll);
		};
	}, []);

	return {
		isFixed,
	};
};
