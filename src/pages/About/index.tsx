import { useAnalytics } from '@/shared/hooks/useAnalytics';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useSearchResultCounterStore } from '@/store/searchResultCounter.store';
import { useEffect } from 'react';

import { Loader } from '@/components/Loader';
import { SearchBar } from '@/components/SearchBar';

import { placeholders } from '@/config/searchPlaceholder';

import { EventDescription } from './components/EventDescription';
import { EventNav } from './components/EventNav';
import { useAbout } from './hooks/useAbout';
import { About__Inner, About__Wrapper } from './styled';

const About = () => {
	const { event } = useEventDetails();
	const { setCount } = useSearchResultCounterStore();
	const { nav, loading, search, onChangeSearch, handleKeyDown } = useAbout();
	useAnalytics('About', search);

	const { browserHeight } = useCurrentSize();
	const { isDesktopFixed } = useDesktopSticky({
		page: 'about',
	});

	useEffect(() => {
		setCount({
			favoritesCount: null,
			clubsCount: null,
			rosterCount: null,
			divisionsCount: null,
		});

		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	return (
		<>
			{loading && <Loader />}
			<SearchBar
				search={search}
				onChangeSearch={onChangeSearch}
				handleKeyDown={handleKeyDown}
				isTextFieldHidden={!event?.schedule_published}
				placeholder={placeholders.about}
			/>
			<About__Wrapper $isDesktopFixed={isDesktopFixed} $browserHeight={browserHeight}>
				<About__Inner>
					<EventNav nav={nav} />
					<EventDescription />
					{/*Hidden div to test Sentry error handling*/}
					<div
						style={{ display: 'none' }}
						onClick={() => {
							throw new Error('Test error');
						}}
					>
						THROW
					</div>
				</About__Inner>
			</About__Wrapper>
		</>
	);
};

export default About;
