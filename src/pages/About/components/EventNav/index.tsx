import { useEventDetails } from '@/shared/hooks/useEventDetails';
import ballerLogoIcon from '@assets/baller-logo.svg';
import navAboutEventIcon from '@assets/navAboutEventIcon.svg';
import navCalendarIcon from '@assets/navCalendarIcon.svg';
import navCameraIcon from '@assets/navCameraIcon.svg';
import navClubsTeamsIcon from '@assets/navClubsTeamsIcon.svg';
import navCourtGridIcon from '@assets/navCourtGridIcon.svg';
import navDivisionsIcon from '@assets/navDivisionsIcon.svg';
import navFavoritesIcon from '@assets/navFavoritesIcon.svg';
import navPreviouslyQualifiedIcon from '@assets/navPreviouslyQualifiedIcon.svg';
import navRosterIcon from '@assets/navRosterIcon.svg';
import { useParams } from 'react-router-dom';

import { NavT } from '../../constants';
import {
	EventNav__Count,
	EventNav__Item,
	EventNav__List,
	EventNav__Title,
	EventNav__TitleContent,
	EventNav__TitleStream,
} from './styled';

type PropsT = {
	nav: NavT;
};
export const EventNav = ({ nav }: PropsT) => {
	const { id } = useParams();
	const { event } = useEventDetails();

	const getNavItemIcon = (key: string) => {
		switch (key) {
			case 'favorites':
				return <img src={navFavoritesIcon} alt="favorites icon" />;
			case 'clubs-teams':
				return <img src={navClubsTeamsIcon} alt="club and teams icon" />;
			case 'athletes':
				return <img src={navRosterIcon} alt="roster icon" />;
			case 'divisions':
				return <img src={navDivisionsIcon} alt="divisions icon" />;
			case 'schedule':
				return <img src={navCourtGridIcon} alt="grid icon" />;
			case 'previously-qualified':
				return <img src={navPreviouslyQualifiedIcon} alt="medal icon" />;
			case 'about-event':
				return <img src={navAboutEventIcon} alt="info icon" />;
			case 'all-events':
				return <img src={navCalendarIcon} alt="calendar icon" />;
			default:
				return null;
		}
	};

	return (
		<EventNav__List>
			{nav.map((item, index) => (
				<EventNav__Item key={index}>
					<EventNav__Title to={item.path}>
						<EventNav__TitleContent>
							{getNavItemIcon(item.key)}
							{item.title}
						</EventNav__TitleContent>
						{!!item.count && <EventNav__Count>{item.count}</EventNav__Count>}
					</EventNav__Title>
				</EventNav__Item>
			))}
			{event?.teams_settings?.baller_tv_available && (
				<EventNav__Item>
					<EventNav__TitleStream
						href={`${import.meta.env.VITE_BALLERTV_URL}/events?sport_wrench_event_id=${id}`}
						target="_blank"
					>
						<img src={navCameraIcon} alt="camera icon" />
						Watch Live on <img src={ballerLogoIcon} alt="baller" />
					</EventNav__TitleStream>
				</EventNav__Item>
			)}
		</EventNav__List>
	);
};
