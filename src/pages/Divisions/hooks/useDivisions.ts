import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useSearchResultCounterStore } from '@/store/searchResultCounter.store';
import { sanitizeSearchInput } from '@/utils';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { StringParam, useQueryParams } from 'use-query-params';

export const useDivisions = () => {
	const { divisions, loading } = useEventDetails();
	const [queryParams] = useQueryParams({ search: StringParam });
	const { setCount } = useSearchResultCounterStore();

	const [search, setSearch] = useState(queryParams.search || '');
	const onChangeSearch = useCallback((value: string) => {
		setSearch(value);
	}, []);

	const sanitizedSearch = sanitizeSearchInput(search).toLowerCase();
	const filteredDivisions = useMemo(() => {
		if (!divisions?.length) return [];
		if (!sanitizedSearch) return divisions;

		return divisions.filter((division) => division.name.toLowerCase().includes(sanitizedSearch));
	}, [sanitizedSearch, divisions]);

	useEffect(() => {
		setSearch(queryParams.search || '');
	}, [queryParams.search]);

	useEffect(() => {
		if (!divisions || loading) return;
		setCount({ divisionsCount: filteredDivisions.length });
	}, [divisions, filteredDivisions.length, loading, setCount]);

	return {
		divisions: filteredDivisions,
		search,
		onChangeSearch,
		loading,
	};
};
