import { Link } from 'react-router-dom';
import styled from 'styled-components';

export const Divisions__Wrapper = styled.div<{
	$browserHeight: number;
	$isDesktopFixed: boolean;
	$isTab?: boolean;
}>`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: ${(props) => (props.$isTab ? '85px 24px 24px 24px' : '24px')};
		width: 680px;
		min-width: 648px;
		margin: auto;
		border-radius: 4px;
		background: #fff;
		min-height: ${(props) => props.$browserHeight - 304}px;
		box-shadow:
			0px 12px 24px -4px rgba(145, 158, 171, 0.12),
			0px 0px 2px 0px rgba(145, 158, 171, 0.2);
		position: fixed;
		top: 62px;
		left: 50%;
		margin: -48px 0 0 -340px;
		top: 240px;
		overflow-y: auto;
		z-index: 99;
		height: calc(100vh - 246px);
	}
`;
export const Divisions__List = styled.ul<{ $isTab?: boolean }>`
	padding: ${(props) => (props.$isTab ? '0 0 66px' : '16px 16px 66px 16px')};
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		width: 600px;
		margin: auto;
		padding: 0;
	}
`;
export const Divisions__Item = styled.li`
	position: relative;
	margin: 0 0 16px;
	border-radius: 8px;
	border: 1px solid #f4f6f8;
	padding: 24px 16px;
`;
export const Divisions__Title = styled.div`
	font-size: 14px;
	line-height: 22px;
	font-weight: 700;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
	}
`;
export const Divisions__TeamsCount = styled.div`
	font-size: 14px;
	line-height: 18px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 14px;
	}
`;
export const Divisions__Link = styled(Link)`
	text-decoration: none;
	display: flex;
	justify-content: space-between;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		&:hover {
			div:first-child {
				text-decoration: underline;
			}
		}
	}
`;
