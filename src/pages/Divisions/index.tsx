import { useAnalytics } from '@/shared/hooks/useAnalytics';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';
import { useEventDetails } from '@/shared/hooks/useEventDetails';

import { Loader } from '@/components/Loader';
import { NotFound } from '@/components/NotFound';
import { SearchBar } from '@/components/SearchBar';

import { placeholders } from '@/config/searchPlaceholder';

import { useDivisions } from './hooks/useDivisions';
import {
	Divisions__Item,
	Divisions__Link,
	Divisions__List,
	Divisions__TeamsCount,
	Divisions__Title,
	Divisions__Wrapper,
} from './styled';

type PropsT = {
	isTab?: boolean;
};
const Divisions = ({ isTab }: PropsT) => {
	const ACTIVE_TAB = 'pools';
	const { eswId } = useEventDetails();

	const { divisions, search, onChangeSearch, loading } = useDivisions();
	useAnalytics('Divisions', search);

	const { browserHeight } = useCurrentSize();
	const { isDesktopFixed } = useDesktopSticky({ page: 'divisions' });
	return (
		<>
			{loading && <Loader />}
			{!isTab && (
				<SearchBar
					placeholder={placeholders.divisions}
					search={search}
					onChangeSearch={onChangeSearch}
				/>
			)}
			<Divisions__Wrapper
				$browserHeight={browserHeight}
				$isDesktopFixed={isDesktopFixed}
				$isTab={isTab}
			>
				{!divisions.length && !loading && <NotFound type="divisions" />}
				<Divisions__List $isTab={isTab} data-testid="divisions-list">
					{divisions.map((division) => (
						<Divisions__Item key={division.division_id} data-testid="division-item">
							<Divisions__Link
								to={`/events/${eswId}/divisions/${division.division_id}/${ACTIVE_TAB}`}
							>
								<Divisions__Title>{division.name}</Divisions__Title>
								<Divisions__TeamsCount>{division.teams_count} Teams</Divisions__TeamsCount>
							</Divisions__Link>
						</Divisions__Item>
					))}
				</Divisions__List>
			</Divisions__Wrapper>
		</>
	);
};

export default Divisions;
