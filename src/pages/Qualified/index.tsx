import { useAnalytics } from '@/shared/hooks/useAnalytics';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';

import { Loader } from '@/components/Loader';
import { NotFound } from '@/components/NotFound';
import { SearchBar } from '@/components/SearchBar';

import { placeholders } from '@/config/searchPlaceholder';

import { useQualified } from './hooks/useQualified';
import { Qualified__Content, Qualified__Wrapper } from './styled';

const Qualified = () => {
	const { search, onChangeSearch, loading, qualifiedTeams } = useQualified();
	useAnalytics('Qualified Teams', search);

	const { browserHeight } = useCurrentSize();
	const { isDesktopFixed } = useDesktopSticky({
		page: 'qualified',
	});
	return (
		<>
			{loading && <Loader />}
			<SearchBar
				placeholder={placeholders.previouslyQualified}
				search={search}
				onChangeSearch={onChangeSearch}
			/>
			{
				<Qualified__Wrapper $browserHeight={browserHeight} $isDesktopFixed={isDesktopFixed}>
					{!qualifiedTeams.length && !loading && <NotFound type="qualified" />}
					<Qualified__Content>
						{!!qualifiedTeams.length && (
							<table>
								<thead>
									<tr>
										<td>Division</td>
										<td>Team Name</td>
										<td>Earned Bid</td>
										<td>Earned At</td>
									</tr>
								</thead>
								<tbody>
									{qualifiedTeams.map((team) => (
										<tr key={team.team_id}>
											<td>{team.division_name}</td>
											<td>{team.team_name}</td>
											<td>{team.bid_earned}</td>
											<td>{team.earned_at}</td>
										</tr>
									))}
								</tbody>
							</table>
						)}
					</Qualified__Content>
				</Qualified__Wrapper>
			}
		</>
	);
};

export default Qualified;
