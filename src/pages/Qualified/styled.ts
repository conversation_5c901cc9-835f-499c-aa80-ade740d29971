import styled from 'styled-components';

export const Qualified__Wrapper = styled.div<{ $browserHeight: number; $isDesktopFixed: boolean }>`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding-bottom: 16px;
		overflow: hidden;
		width: calc(100% - 120px);
		width: 680px;
		margin: -48px auto;
		border-radius: 4px;
		background: #fff;
		min-height: ${(props) => props.$browserHeight - 304}px;
		box-shadow:
			0px 12px 24px -4px rgba(145, 158, 171, 0.12),
			0px 0px 2px 0px rgba(145, 158, 171, 0.2);
		position: fixed;
		left: 50%;
		margin: -48px 0 0 -340px;
		z-index: 99;
		top: 240px;
		overflow-y: auto;
		height: calc(100vh - 246px);
	}
`;
export const Qualified__Content = styled.div`
	table {
		width: 100%;
		border-collapse: collapse;
		thead {
			tr {
				td {
					/* width: 25%; */
					padding: 18px 8px;
					font-size: 14px;
					font-weight: 700;
					line-height: 18px;
					background: #f4f6f8;

					@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
						font-size: 14px;
					}
					&:first-child {
						padding: 0 8px 0 16px;
					}
					&:nth-child(2) {
					}
					&:nth-child(3) {
						padding: 18px 5px;
					}
					&:last-child {
						padding: 0 16px 0 8px;
					}
				}
			}
		}
		tbody {
			tr {
				border-bottom: 1px solid #dfe3e8;
				td {
					font-size: 14px;
					line-height: 18px;
					font-weight: 400;
					padding: 16px 8px 16px;
					@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
						overflow: hidden;
						white-space: nowrap;
						max-width: 100px;
						text-overflow: ellipsis;
					}
					&:first-child {
						padding: 0 8px 0 16px;
						font-weight: 700;
					}
					&:nth-child(2) {
					}
					&:nth-child(3) {
						font-weight: 700;
					}
					&:last-child {
						padding: 0 16px 0 8px;
					}
				}
			}
		}
	}
`;
