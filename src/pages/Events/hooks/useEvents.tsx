import { SEARCH_DEBOUNCE_TIME } from '@/config';
import { sanitizeSearchInput } from '@/utils';
import { CustomMultiSelect } from '@components/CustomMultiselect';
import {
	Events__DataWrapper,
	Events__FilterContainer,
	Events__SelectWrapper,
} from '@pages/Events/styled';
import * as DateFns from 'date-fns';
import { ChangeEvent, useEffect, useMemo, useState } from 'react';
import { useDebounce } from 'use-debounce';

import { EventList } from '../components/EventList';
import { usePaginatedEvents } from './usePaginatedEvents';

const PAST_YEARS_OPTIONS_FROM = 2015;

export const useEvents = ({ scrollRef }: { scrollRef: React.RefObject<HTMLDivElement> }) => {
	const [searchValue, setSearchValue] = useState('');
	const [activeTab, setActiveTab] = useState('Current');
	const [currentPastYear, setCurrentPastYear] = useState<string[]>([]);
	const [multiSelectName, setMultiSelectName] = useState('');

	const changeSearchValue = (e: ChangeEvent<HTMLInputElement>) => {
		setSearchValue(e.target.value);
	};

	const onPastEventYearsChanged = (years: string[]) => {
		const sortedYears = years.toSorted();
		if (
			sortedYears.length !== currentPastYear.length ||
			sortedYears.some((year, index) => year !== currentPastYear[index])
		) {
			return setCurrentPastYear(sortedYears);
		}
	};

	useEffect(() => {
		if (!currentPastYear.length) {
			setMultiSelectName('Select year');
		} else {
			const years = currentPastYear.map((y) => Number(y)).sort();
			setMultiSelectName(`Selected years: (${years.join(', ')})`);
		}
	}, [currentPastYear]);

	const pastYearsOptions = useMemo(() => {
		const from = PAST_YEARS_OPTIONS_FROM;
		const to = new Date().getFullYear();

		return Array.from({ length: to - from + 1 }, (_, i) => {
			const year = (from + i).toString();
			return { key: year, value: year };
		}).sort((a, b) => Number(b.value) - Number(a.value));
	}, []);

	const startOfHour = DateFns.startOfHour(new Date());
	const now = startOfHour.toISOString();
	const next7Days = DateFns.addDays(startOfHour, 7).toISOString();

	const [searchDebounce] = useDebounce(sanitizeSearchInput(searchValue), SEARCH_DEBOUNCE_TIME);

	const [fetchNextCurrentEvents, currentEvents, loadingCurrentEvents] = usePaginatedEvents({
		search: searchDebounce,
		startBefore: next7Days,
		endAfter: now,
	});

	const [fetchNextFutureEvents, futureEvents, loadingFutureEvents] = usePaginatedEvents({
		search: searchDebounce,
		startAfter: now,
	});

	const [fetchNextPastEvents, pastEvents, loadingPastEvents] = usePaginatedEvents({
		search: searchDebounce,
		endBefore: now,
		years: currentPastYear,
		asc: false,
	});

	useEffect(() => {
		scrollRef.current?.scrollTo(0, 0);
		document.documentElement.scrollTop = 0;
		document.body.scrollTop = 0;
	}, [multiSelectName, scrollRef]);

	const TABS = {
		Current: (
			<>
				{currentEvents && (
					<EventList
						isLoading={loadingCurrentEvents}
						events={currentEvents}
						onScrollEnd={fetchNextCurrentEvents}
						scrollRef={scrollRef}
					/>
				)}
			</>
		),
		Future: (
			<>
				{futureEvents && (
					<EventList
						isLoading={loadingFutureEvents}
						events={futureEvents}
						onScrollEnd={fetchNextFutureEvents}
						scrollRef={scrollRef}
					/>
				)}
			</>
		),
		Past: (
			<>
				<Events__FilterContainer>
					<Events__SelectWrapper>
						<CustomMultiSelect
							type="pastEventsYear"
							isBlue
							data={pastYearsOptions}
							selectName={multiSelectName}
							dropDownName="Select years"
							setSelectedData={onPastEventYearsChanged}
						/>
					</Events__SelectWrapper>
				</Events__FilterContainer>
				{pastEvents && (
					<Events__DataWrapper>
						<EventList
							isLoading={loadingPastEvents}
							events={pastEvents}
							onScrollEnd={fetchNextPastEvents}
							scrollRef={scrollRef}
						/>
					</Events__DataWrapper>
				)}
			</>
		),
	};

	return {
		activeTab,
		tabs: Object.entries(TABS),
		changeTab: setActiveTab,
		loading: loadingCurrentEvents || loadingPastEvents || loadingFutureEvents,
		searchValue,
		changeSearchValue,
	};
};
