import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useLazyScrollTrigger } from '@/shared/hooks/useLazyScrollTrigger';
import { PaginatedEvent } from '@/shared/types/event.types';
import logoTeamPlaceholder from '@assets/logo-team-placeholder.png';
import { useRef } from 'react';
import { Link } from 'react-router-dom';

import calendarIcon from '@/assets/calendar-icon.svg';

import { NotFound } from '@/components/NotFound';
import { Spinner } from '@/components/Spinner';

import { getFormateTime } from '@/utils/time';

import {
	EventList__Item,
	EventList__ItemLeftBox,
	EventList__ItemName,
	EventList__ItemRightBox,
	EventList__List,
	EventList__Logo,
} from './styled';

type Props = {
	events: PaginatedEvent[];
	isLoading: boolean;
	scrollRef: React.RefObject<HTMLDivElement>;
	onScrollEnd: () => void;
};

const HOST =
	import.meta.env.VITE_APP_ENV === 'development'
		? import.meta.env.VITE_DEV_SERVER
		: import.meta.env.VITE_PROD_SERVER;

export const EventList = ({ events, isLoading, onScrollEnd, scrollRef }: Props) => {
	const listRef = useRef<HTMLUListElement>(null);
	const isMobile = useCurrentSize().breakPont === 'small';
	useLazyScrollTrigger(onScrollEnd, isMobile ? null : scrollRef);

	return (
		<>
			{isLoading && <Spinner />}
			{!events.length && !isLoading && <NotFound type="all-events" />}
			<EventList__List ref={listRef}>
				{events.map(
					({ event_id, long_name, city, state, date_start, schedule_published, small_logo }) => (
						<EventList__Item key={event_id}>
							<Link to={`/events/${event_id}`}>
								<EventList__ItemLeftBox>
									{small_logo && (
										<EventList__Logo
											src={`${HOST}${small_logo}`}
											alt="event logo"
											onError={(e) => {
												const target = e.target as HTMLImageElement;
												target.src = logoTeamPlaceholder;
											}}
										/>
									)}
									<EventList__ItemName>
										<p>{long_name}</p>
										<p>
											{date_start && getFormateTime({ time: date_start, format: 'MMM d, yyyy' })}{' '}
											{city}
											{state && `, ${state}`}
										</p>
									</EventList__ItemName>
								</EventList__ItemLeftBox>
								<EventList__ItemRightBox>
									{schedule_published && <img src={calendarIcon} alt="schedule published" />}
								</EventList__ItemRightBox>
							</Link>
						</EventList__Item>
					),
				)}
			</EventList__List>
		</>
	);
};
