import { useAnalytics } from '@/shared/hooks/useAnalytics';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useEvents } from '@pages/Events/hooks/useEvents';
import { useEffect, useRef } from 'react';

import { Tabs } from '@/components/Tabs';
import { Search } from '@/components/form/Search';

import { Events__SearchContainer, Events__TabsWrapper, Events__Wrapper } from './styled';

export const Events = () => {
	const scrollRef = useRef<HTMLDivElement>(null);
	const { tabs, activeTab, changeTab, searchValue, changeSearchValue } = useEvents({ scrollRef });
	useAnalytics('Events', searchValue);
	const { browserHeight } = useCurrentSize();

	useEffect(() => {
		if (scrollRef.current) {
			scrollRef.current.scrollTop = 0;
			document.documentElement.scrollTop = 0;
		}
	}, [activeTab]);
	return (
		<Events__Wrapper>
			<Events__SearchContainer>
				<Search
					onChange={changeSearchValue}
					placeholder="Event name"
					variant="outlined"
					value={searchValue}
				/>
			</Events__SearchContainer>
			<Events__TabsWrapper $browserHeight={browserHeight} ref={scrollRef}>
				<Tabs activeTab={activeTab} handleChange={(_e, value) => changeTab(value)} tabs={tabs} />
			</Events__TabsWrapper>
		</Events__Wrapper>
	);
};
