import styled from 'styled-components';

export const Roster__Wrapper = styled.div<{ $browserHeight: number; $isDesktopFixed: boolean }>`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 85px 24px 0;
		width: 680px;
		min-width: 648px;
		margin: -48px auto auto;
		border-radius: 4px;
		background: rgb(255, 255, 255);
		min-height: 1323px;
		box-shadow:
			rgba(145, 158, 171, 0.12) 0px 12px 24px -4px,
			rgba(145, 158, 171, 0.2) 0px 0px 2px 0px;
		position: fixed;
		left: 50%;
		margin: -48px 0 0 -340px;
		z-index: 99;
		top: 240px;
		overflow-y: auto;
		height: calc(100vh - 246px);
	}
`;
export const Roster__TabContentWrapper = styled.div<{ $isTab: boolean }>`
	padding: ${(props) => (props.$isTab ? '0 0 66px' : '16px 16px 66px 16px')};
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 0 0 66px;
	}
`;
export const Roster__TabContentList = styled.ul``;
export const Roster__TabContentItem = styled.li`
	margin-bottom: 16px;
	cursor: pointer;
	&:last-child {
		margin-bottom: 0;
	}
`;
