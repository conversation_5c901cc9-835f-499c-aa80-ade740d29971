import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useLazyScrollTrigger } from '@/shared/hooks/useLazyScrollTrigger';
import { useTeamModal } from '@/shared/hooks/useTeamModal';
import { PaginatedStaff } from '@/shared/types/staff.types';
import { Loader } from '@components/Loader';

import { NotFound } from '@/components/NotFound';
import { Spinner } from '@/components/Spinner';

import {
	Roster__TabContentItem,
	Roster__TabContentList,
	Roster__TabContentWrapper,
} from '../../styled';
import { RosterCard } from '../RosterCard';

type PropsT = {
	staff: PaginatedStaff[];
	isLoading: boolean;
	onScrollEnd: () => void;
	scrollRef: React.RefObject<HTMLDivElement>;
	isTab?: boolean;
};

export const StaffList = ({ staff, isLoading, onScrollEnd, scrollRef, isTab }: PropsT) => {
	const isMobile = useCurrentSize().breakPont === 'small';
	useLazyScrollTrigger(onScrollEnd, isMobile ? null : scrollRef);

	const { teamModalElement, openTeamModal, teamDataLoading } = useTeamModal();

	return (
		<>
			{isLoading && <Spinner />}
			{teamDataLoading && <Loader />}
			{teamModalElement}
			<Roster__TabContentWrapper $isTab={!!isTab}>
				{!staff.length && !isLoading && <NotFound type="roster" />}
				<Roster__TabContentList>
					{staff.map((item) => {
						const role = item.role_name ? ` - ${item.role_name}` : '';
						const header = `${item.first} ${item.last}${role}`;
						const content = `${item.team_name} - ${item.organization_code}`;
						const footer = `${item.club_name}, ${item.state}`;

						return (
							<Roster__TabContentItem
								data-testid="staff-item"
								key={item.staff_id}
								onClick={() => openTeamModal(item.team_id)}
							>
								<RosterCard header={header} content={content} footer={footer} />
							</Roster__TabContentItem>
						);
					})}
				</Roster__TabContentList>
			</Roster__TabContentWrapper>
		</>
	);
};
