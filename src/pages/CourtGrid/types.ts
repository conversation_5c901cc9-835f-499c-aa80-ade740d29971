import { CourtsRangeMatchesQuery, EventDetailsQuery } from '@/generated/graphql';

export type DivisionInfo = Pick<
	EventDetailsQuery['divisions'][number],
	'division_id' | 'short_name'
>;

export type MatchesTimeRange = Pick<
	EventDetailsQuery['divisions'][number]['matches_time_ranges'][number],
	'start_time' | 'end_time'
>;

export type AlertType = 'error' | 'warning' | null;

export type GridMatch = CourtsRangeMatchesQuery['dayRangeMatches']['items'][number];
export type GridCourt = CourtsRangeMatchesQuery['relatedCourts'][number];
export type GridCourtMatches = {
	court: GridCourt;
	courtAlert: AlertType;
	courtTbMatchesCount: number;
	matches: GridMatch[];
};

export type GridTimePeriod = {
	periodStart: number;
	periodEnd: number;
	periodDisplayName: string;
};
