import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { bracketGridMatchNameSignal } from '@/signals/bracketGridMatchNameSignal';
import { useState } from 'react';
import { StringParam, useQueryParams } from 'use-query-params';

import { getFormateTime } from '@/utils/time';

import { getMatchAlert } from '../../helpers';
import { GridMatch } from '../../types';
import { Menu } from './menu';
import {
	CardGridCard__Footer,
	CardGridCard__Footer__Time,
	CardGridCard__Header,
	CardGridCard__MatchAndPoolBracketTitleWrapper,
	CardGridCard__QuarterTime,
	CardGridCard__RefName,
	CardGridCard__TeamItem,
	CardGridCard__TeamItem__Name,
	CardGridCard__TeamItem__Score,
	CardGridCard__TeamList,
	CardGridCard__TimeAlertDescription,
	CardGridCard__Title,
	CardGridCard__Wrapper,
} from './styled';

type Props = {
	match: GridMatch;
	focusedMatchId: string | null;
	tbMatchesCount?: number;
	minPeriod?: number;
	openTeamModal: (_teamId: string) => void;
	openPoolModal: (_teamId: string, _poolId: string) => void;
	openBracketModal: (_params: { team_id: string; bracketId: string; division_id: string }) => void;
	rowsCount?: number;
};

export const GridCard = ({
	match,
	focusedMatchId,
	tbMatchesCount,
	minPeriod,
	openTeamModal,
	openPoolModal,
	openBracketModal,
	rowsCount,
}: Props) => {
	const {
		match_id,
		results,
		secs_start,
		secs_end,
		secs_finished,
		is_tb,
		external,
		division_id,
		team_id,
		opponent_id,
	} = match;

	const title = `${match.division_name} ${match.match_name}`;
	const isTbMatch = !!is_tb;

	const winnerTeam = results?.winner_team || {};
	// API response contains team1 as team, and team2 as opponent_team
	const isTeamWon = winnerTeam.roster_team_id === match.team_id;
	const scores = winnerTeam.scores || '0-0 (0-0,0-0)';
	const [winnerScore, looserScore] = (scores.split(' ')[0] || '0-0').split('-');
	const isMatchPlayed = !!winnerTeam.scores;

	const minStart = getFormateTime({ time: secs_start!, format: 'mm' });
	const isQuarter = !['00', '15', '30', '45'].includes(minStart);

	const matchDuration = +getFormateTime({ time: secs_end! - secs_start!, format: 'mm' });

	const isMatchDuration45mins = matchDuration === 45;
	const isMatchDuration60mins = matchDuration === 0;

	const { event } = useEventDetails();

	const [activeMenuId, setActiveMenuId] = useState<string | null>(null);

	const toggleMenu = (id: string | null) => {
		setActiveMenuId((prev) => (prev === id ? null : id));
	};

	const [, setQueryParams] = useQueryParams({
		divisionId: StringParam,
	});

	const isTeamHasId = (teamName: string) => !teamName.toLowerCase().includes('winner');

	const poolBracketHandler = () => {
		const id = Number(team_id) || Number(opponent_id);
		setQueryParams({ divisionId: division_id });
		bracketGridMatchNameSignal.value = match.match_name || '';
		external.pool_bracket_info.is_pool
			? openPoolModal(`${id}`, match.external.pool_bracket_info.uuid)
			: openBracketModal({
					team_id: `${id}`,
					bracketId: external.pool_bracket_info.uuid,
					division_id,
				});
	};

	const team1ModalHandler = (e: React.MouseEvent<HTMLElement>) => {
		e.stopPropagation();
		if (match.team_id === '0' && match.external.team_pb_seed?.id) {
			openPoolModal(match.team_id, match.external.team_pb_seed?.id);
			return;
		}

		isTeamHasId(external.team_display_name) && openTeamModal(`${team_id}`);
	};
	const team2ModalHandler = (e: React.MouseEvent<HTMLElement>) => {
		e.stopPropagation();
		if (match.opponent_id === '0' && match.external.opponent_pb_seed?.id) {
			openPoolModal(match.opponent_id, match.external.opponent_pb_seed?.id);
			return;
		}

		isTeamHasId(external.opponent_display_name) && openTeamModal(`${opponent_id}`);
	};

	return (
		<>
			<CardGridCard__Wrapper
				id={match_id}
				$minPeriod={minPeriod}
				$isMatchDuration45mins={isMatchDuration45mins}
				$isMatchDuration60mins={isMatchDuration60mins}
				$isFixedWidth
				$isTbMatch={isTbMatch}
				$width={0} // Disabled for now
				$isActive={focusedMatchId === match_id}
				$isBlur={!!focusedMatchId && focusedMatchId !== match_id}
				$isFinished={!!secs_finished}
				$isMiddle={!isTbMatch && tbMatchesCount === 2} // Some magic happens here
				$rowsCount={rowsCount}
			>
				<CardGridCard__Header>
					<CardGridCard__Title
						$isTbMatch={isTbMatch}
						$isQuarter={isQuarter}
						$isShowMenu={!!event?.teams_settings?.baller_tv_available}
						onClick={poolBracketHandler}
					>
						<span>
							<CardGridCard__MatchAndPoolBracketTitleWrapper>
								{title}
							</CardGridCard__MatchAndPoolBracketTitleWrapper>
						</span>

						<Menu
							id={match_id}
							toggleMenu={toggleMenu}
							activeMenuId={activeMenuId}
							poolBracketHandler={poolBracketHandler}
							team1Name={external.team_display_name}
							team2Name={external.opponent_display_name}
							team1Handler={team1ModalHandler}
							team2Handler={team2ModalHandler}
						/>
					</CardGridCard__Title>
					{isQuarter && (
						<CardGridCard__QuarterTime>
							{getFormateTime({ time: secs_start!, format: 'h:mmaa' })}
						</CardGridCard__QuarterTime>
					)}
				</CardGridCard__Header>
				<CardGridCard__TeamList>
					<CardGridCard__TeamItem $isLoose={!isTeamWon} $isMatchPlayed={isMatchPlayed}>
						<CardGridCard__TeamItem__Name
							$isTbMatch={isTbMatch}
							$isTeamHasId={isTeamHasId(external.team_display_name)}
							onClick={team1ModalHandler}
						>
							<CardGridCard__MatchAndPoolBracketTitleWrapper>
								{external.team_display_name}
							</CardGridCard__MatchAndPoolBracketTitleWrapper>
						</CardGridCard__TeamItem__Name>
						<CardGridCard__TeamItem__Score $isTbMatch={isTbMatch}>
							{isTeamWon ? winnerScore : looserScore}
						</CardGridCard__TeamItem__Score>
					</CardGridCard__TeamItem>
					<CardGridCard__TeamItem $isLoose={isTeamWon} $isMatchPlayed={isMatchPlayed}>
						<CardGridCard__TeamItem__Name
							$isTbMatch={isTbMatch}
							$isTeamHasId={isTeamHasId(external.opponent_display_name)}
							onClick={team2ModalHandler}
						>
							<CardGridCard__MatchAndPoolBracketTitleWrapper>
								{external.opponent_display_name}
							</CardGridCard__MatchAndPoolBracketTitleWrapper>
						</CardGridCard__TeamItem__Name>
						<CardGridCard__TeamItem__Score $isTbMatch={isTbMatch}>
							{isTeamWon ? looserScore : winnerScore}
						</CardGridCard__TeamItem__Score>
					</CardGridCard__TeamItem>
				</CardGridCard__TeamList>
				<CardGridCard__Footer>
					<CardGridCard__Footer__Time $isTbMatch={isTbMatch}>
						{secs_finished ? (
							<div>&nbsp;</div>
						) : (
							<CardGridCard__RefName $isFullWidth={!secs_finished}>
								<span>W: {external.ref_team_display_name}</span>
							</CardGridCard__RefName>
						)}
						{!!secs_finished && (
							<CardGridCard__TimeAlertDescription $type={getMatchAlert(match)}>
								{getFormateTime({ time: secs_finished, format: 'h:mmaaa' })}
							</CardGridCard__TimeAlertDescription>
						)}
					</CardGridCard__Footer__Time>
				</CardGridCard__Footer>
			</CardGridCard__Wrapper>
		</>
	);
};
