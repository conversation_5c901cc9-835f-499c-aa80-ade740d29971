import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useEffect, useRef } from 'react';

import ballerLogoIcon from '../../img/baller-logo.svg';
import cameraIcon from '../../img/camera.svg';
import menuIcon from '../../img/menu.svg';
import {
	CardGridCard__MenuButton,
	CardGridCard__MenuButtonWrapper,
	CardGridCard__MenuItem,
	CardGridCard__MenuList,
	CardGridCard__MenuWrapper,
	StyledCameraIcon,
} from './styled';

type Props = {
	id: string;
	team1Name: string;
	team2Name: string;
	team1Handler: (_e: React.MouseEvent<HTMLElement>) => void;
	team2Handler: (_e: React.MouseEvent<HTMLElement>) => void;
	activeMenuId: string | null;
	toggleMenu: (_id: string | null) => void;
	poolBracketHandler: () => void;
};

export const Menu = ({
	id,
	activeMenuId,
	toggleMenu,
	team1Name,
	team2Name,
	team1<PERSON><PERSON><PERSON>,
	team2<PERSON><PERSON><PERSON>,
	poolBracketHandler,
}: Props) => {
	const isShowMenu = activeMenuId === id;
	const dropdownRef = useRef<HTMLDivElement>(null);
	const buttonRef = useRef<HTMLImageElement>(null);
	const { event } = useEventDetails();
	const isBallerTvAvailable = event?.teams_settings?.baller_tv_available;
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node) &&
				!(buttonRef.current && buttonRef.current.contains(event.target as Node))
			) {
				toggleMenu(null);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, [toggleMenu]);

	const openBallerTv = (e: React.MouseEvent<HTMLElement>) => {
		e.stopPropagation();
		toggleMenu(null);
		window.open(
			`${import.meta.env.VITE_BALLERTV_URL}/streams?sport_wrench_match_uuid=${id}`,
			'_blank',
		);
	};

	return (
		<>
			<CardGridCard__MenuButtonWrapper onClick={(e) => e.stopPropagation()} ref={buttonRef}>
				{isBallerTvAvailable && (
					<StyledCameraIcon
						src={cameraIcon}
						alt="camera icon"
						onClick={() => toggleMenu(isShowMenu ? null : id)}
					/>
				)}
				<CardGridCard__MenuButton
					ref={buttonRef}
					src={menuIcon}
					alt="menu icon"
					onClick={() => toggleMenu(isShowMenu ? null : id)}
				/>
			</CardGridCard__MenuButtonWrapper>
			{isShowMenu && (
				<CardGridCard__MenuWrapper ref={dropdownRef}>
					<CardGridCard__MenuList>
						{isBallerTvAvailable && (
							<CardGridCard__MenuItem>
								<div>
									<a target="_blank" onClick={openBallerTv} rel="noreferrer">
										Watch Live on <img src={ballerLogoIcon} alt="baller logo" />
									</a>
								</div>
							</CardGridCard__MenuItem>
						)}
						<CardGridCard__MenuItem
							onClick={(e) => {
								toggleMenu(null);
								team1Handler(e);
							}}
						>
							<b>{team1Name}</b>
						</CardGridCard__MenuItem>
						<CardGridCard__MenuItem
							onClick={(e) => {
								toggleMenu(null);
								team2Handler(e);
							}}
						>
							<b>{team2Name}</b>
						</CardGridCard__MenuItem>
						<CardGridCard__MenuItem
							onClick={() => {
								toggleMenu(null);
								poolBracketHandler();
							}}
						>
							<b>Pool/Bracket Details</b>
						</CardGridCard__MenuItem>
					</CardGridCard__MenuList>
				</CardGridCard__MenuWrapper>
			)}
		</>
	);
};
