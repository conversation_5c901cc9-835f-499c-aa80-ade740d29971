import styled from 'styled-components';

import { getCourtGridTypeAlert } from '@/styles/shared';

const OFFSET_IN_THE_CARD = 16;

const getCardWidth = ({
	isFixedWidth,
	width,
	isMatchDuration45mins,
	isMatchDuration60mins,
	minPeriod,
}: {
	isFixedWidth: boolean;
	width: number;
	isMatchDuration45mins?: boolean;
	isMatchDuration60mins?: boolean;
	minPeriod?: number;
}) => {
	if (isFixedWidth) {
		if (isMatchDuration45mins) {
			return 300;
		}
		if (isMatchDuration60mins && minPeriod === 15) {
			return 400;
		}
		return 200;
	}
	return width - OFFSET_IN_THE_CARD;
};

const getOffsetTop = (props: {
	$isMiddle?: boolean;
	$isFixedWidth?: boolean;
	$rowsCount?: number;
}) => {
	if (props.$isMiddle) return '58px';
	if (props.$isFixedWidth && props.$rowsCount !== 1) return '10px';
	if (props.$rowsCount === 1) return '40px';
	return '8px';
};

export const CardGridCard__Wrapper = styled.div<{
	$width: number;
	$minPeriod?: number;
	$isActive: boolean;
	$isBlur: boolean;
	$isFinished?: boolean;
	$isTbMatch?: boolean;
	$isMiddle?: boolean;
	$rowsCount?: number;
	$isFixedWidth?: boolean;
	$isMatchDuration45mins?: boolean;
	$isMatchDuration60mins?: boolean;
}>`
	width: ${(props) =>
		getCardWidth({
			isFixedWidth: !!props.$isFixedWidth,
			width: props.$width,
			isMatchDuration45mins: props.$isMatchDuration45mins,
			isMatchDuration60mins: props.$isMatchDuration60mins,
			minPeriod: props.$minPeriod,
		})}px;
	border: ${(props) => props.$isActive && '1px solid' + '#00AB55'};
	opacity: ${(props) => (props.$isBlur ? 0.2 : 1)};
	left: ${(props) => (props.$isFixedWidth ? 4 : 8)}px;
	position: absolute;
	background-color: ${(props) => (props.$isFinished ? '#f4f6f8' : '#fff')};
	border-radius: 8px;
	box-shadow:
		0px 0px 2px 0px rgba(145, 158, 171, 0.2),
		0px 12px 24px -4px rgba(145, 158, 171, 0.12);
	height: ${(props) => (props.$isFixedWidth ? '128px' : '90px')};
	padding: ${(props) =>
		props.$isTbMatch ? '6px 9px 3px 9px' : props.$isFixedWidth ? '10px 15px' : '6px 9px 3px 27px'};
	&:first-child {
		top: ${(props) => getOffsetTop(props)};
	}
	&:nth-child(2) {
		top: auto;
		bottom: 8px;
	}
`;
export const CardGridCard__Header = styled.header`
	display: flex;
	justify-content: space-between;
	width: 100%;
`;

const getGridCardTitleWidth = (isShowMenu: boolean) => {
	return isShowMenu ? 'calc(100% - 40px)' : '100%';
};
export const CardGridCard__Title = styled.div<{
	$isTbMatch?: boolean;
	$isQuarter?: boolean;
	$isShowMenu?: boolean;
}>`
	font-size: ${(props) => (props.$isTbMatch ? '10px' : '12px')};
	margin: 0 0 10px;
	font-size: 14px;
	font-weight: 600;
	line-height: 22px;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;

	width: ${(props) => (props.$isQuarter ? 'calc(100% - 50px)' : '100%')};
	display: flex;
	align-items: center;
	justify-content: space-between;
	span {
		cursor: pointer;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		display: inline-block;
		width: ${(props) => getGridCardTitleWidth(!!props.$isShowMenu)};
		color: ${(props) => (props.$isTbMatch ? '#ff997a' : props.theme.colors.blue)};
		&:hover {
			text-decoration: underline;
		}
	}
`;
export const CardGridCard__QuarterTime = styled.div`
	color: #637381;
	font-size: 10px;
	font-weight: 700;
	line-height: 18px;
	margin: 3px 0 0;
`;

export const CardGridCard__TeamList = styled.ul`
	margin: 0 0 11px;
`;
export const CardGridCard__TeamItem = styled.li<{ $isLoose?: boolean; $isMatchPlayed: boolean }>`
	display: flex;
	justify-content: space-between;
	margin: 0 0 2px;
	span {
		color: ${(props) => (props.$isLoose ? '#637381' : '#454F5B')};
		font-weight: ${(props) => (!props.$isLoose ? (props.$isMatchPlayed ? 700 : 400) : 400)};
	}
	&:last-child {
		margin-bottom: 0;
	}
`;
export const CardGridCard__TeamItem__Name = styled.span<{
	$isTbMatch?: boolean;
	$isTeamHasId: boolean;
}>`
	font-size: ${(props) => (props.$isTbMatch ? '8px' : '14px')};
	font-family: 'Public Sans';
	color: #454f5b;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	cursor: ${(props) => (props.$isTeamHasId ? 'pointer' : 'default')};
	&:hover {
		text-decoration: ${(props) => (props.$isTeamHasId ? 'underline' : 'none')};
	}
`;
export const CardGridCard__TeamItem__Score = styled.span<{ $isTbMatch?: boolean }>`
	font-size: ${(props) => (props.$isTbMatch ? '8px' : '14px')};
	font-family: 'Public Sans';
	color: #454f5b;
`;
export const CardGridCard__Footer = styled.footer``;
export const CardGridCard__Footer__Time = styled.div<{ $isTbMatch?: boolean }>`
	font-size: ${(props) => (props.$isTbMatch ? '8px' : '12px')};
	color: #637381;
	display: flex;
	justify-content: space-between;
	align-items: center;
`;
export const CardGridCard__RefName = styled.span<{ $isFullWidth?: boolean }>`
	font-size: 12px;
	line-height: 18px;
	color: #637381;
	width: ${(props) => (props.$isFullWidth ? '100%' : 'calc(100% - 50px')};

	display: flex;
	align-items: center;
	gap: 2.6px;
	span {
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
		max-width: 140px;
		font-style: italic;
		font-weight: 400;
		color: ${(props) => props.theme.colors.light};
	}
`;
export const CardGridCard__TimeAlert = styled.div<{ $type: 'warning' | 'error' }>`
	border-radius: 4px;
	background: ${(props) => (props.$type === 'warning' ? '#ff997a' : '#FF6161')};
	box-shadow:
		0px 12px 24px -4px rgba(145, 158, 171, 0.12),
		0px 0px 2px 0px rgba(145, 158, 171, 0.2);
	width: 10px;
	height: 100%;
	position: absolute;
	left: 0;
	top: 0;
`;
export const CardGridCard__TimeAlertDescription = styled.div<{
	$type?: 'warning' | 'error' | null;
}>`
	color: ${(props) => getCourtGridTypeAlert(props.$type)};
	font-weight: 700;
`;

export const CardGridCard__MenuButtonWrapper = styled.div`
	display: flex;
	align-items: center;
	position: relative;
	z-index: 1;
	left: 5px;
`;
export const CardGridCard__MenuButton = styled.img`
	cursor: pointer;
`;
export const CardGridCard__MenuWrapper = styled.div`
	padding: 5px 10px;
	border-radius: 12px;
	background: #fff;
	box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.16);
	position: absolute;
	z-index: 4;
	right: 0%;
	top: 35px;
	max-width: 200px;
`;
export const CardGridCard__MenuList = styled.ul``;
export const CardGridCard__MenuItem = styled.li`
	color: #212b36;
	font-size: 14px;
	font-weight: 400;
	line-height: 22px;
	display: flex;
	align-items: center;
	gap: 5px;
	cursor: pointer;
	padding: 2px 0;
	border-bottom: 1px solid rgba(145, 158, 171, 0.24);

	b {
		font-weight: normal;
		text-overflow: ellipsis;
		overflow: hidden;
		white-space: nowrap;
		display: block;
		width: 100%;
		padding: 5px;
	}
	&:last-child {
		border-bottom: none;
	}
	a {
		text-decoration: none;
		color: #212b36;
	}
	div {
		padding: 5px;
		width: 100%;
		transition: all 0.2s ease-in-out;
	}
	&:hover {
		transition: all 0.2s ease-in-out;
		border-radius: 6px;
		background: #f4f6f8;
	}
`;

export const CardGridCard__MatchAndPoolBracketTitleWrapper = styled.i`
	position: relative;
	z-index: 3;
	font-style: normal;
`;
export const StyledCameraIcon = styled.img`
	cursor: pointer;
`;
