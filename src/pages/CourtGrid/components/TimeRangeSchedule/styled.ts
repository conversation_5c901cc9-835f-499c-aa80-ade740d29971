import styled from 'styled-components';

import { MainNav__Wrapper } from '@/components/MainNav/styled';

export const SCROLL_OFFSET_TIME_RANGE = 39;

export const TimeRangeSchedule__Wrapper = styled.div<{ $isLarge: boolean }>`
	padding: 16px;
	background: ${(props) => props.theme.colors.blue};
	width: 100%;
	z-index: 2;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		height: 130px !important;
		padding: 12px 60px 0 60px;
		position: fixed;
		${MainNav__Wrapper} {
			margin: 0;
		}
	}
`;
export const TimeRangeSchedule__Header = styled.header`
	display: flex;
	justify-content: space-between;
	margin: 0 0 16px;
`;
export const TimeRangeSchedule__EventTitleDescriptionWrapper = styled.div<{
	$fullWidth?: boolean;
}>`
	width: ${({ $fullWidth }) => ($fullWidth ? '100%' : 'calc(100% - 100px)')};
`;
export const TimeRangeSchedule__EventName = styled.p<{ $isFullWidth?: boolean }>`
	font-size: 14px;
	line-height: 22px;
	font-weight: 700;
	color: #fff;
	text-overflow: ellipsis;
	overflow: hidden;
	width: 100%;
	white-space: nowrap;
	a {
		color: #fff;
		text-decoration: none;

		&:hover {
			text-decoration: underline;
		}
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 20px;
		font-weight: 700;
		line-height: 30px;
		margin: 0 0 4px;
	}
`;
export const TimeRangeSchedule__BuyAdmission = styled.a`
	font-size: 10px;
	text-decoration: underline;
	line-height: 18px;
	color: #f9fafb;
	display: flex;
	align-items: center;
	gap: 4px;

	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
		max-width: 140px;
		text-decoration: none;
		width: 100%;

		&:hover {
			text-decoration: underline;
		}
		img {
			width: 20px;
			height: 14px;
		}
	}
`;
export const TimeRangeSchedule__Footer = styled.footer<{ $isFixed: boolean; $isHidden?: boolean }>`
	display: flex;
	gap: 10px;
	position: ${({ $isFixed }) => ($isFixed ? 'fixed' : 'static')};
	background: ${(props) => props.theme.colors.blue};
	width: 100%;
	left: 0;
	top: ${SCROLL_OFFSET_TIME_RANGE}px;
	padding: ${({ $isFixed }) => ($isFixed ? 16 : 0)}px;
	z-index: 9;
	visibility: ${({ $isHidden }) => ($isHidden ? 'hidden' : 'initial')};
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		position: static;
		padding: 0;
	}
`;

export const TimeRangeSchedule__TimeRangeWrapper = styled.div`
	min-width: 100px;
	width: 100%;
`;
export const TimeRangeSchedule__FooterWrapper = styled.div`
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		width: 600px;
		margin: auto;
		position: relative;
	}
`;
export const TimeRangeSchedule__MainNavWrapper = styled.div`
	display: flex;
	justify-content: space-between;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 0 0 10px;
	}
`;
export const TimeRangeSchedule__EventTitle = styled.p`
	font-size: 14px;
	line-height: 22px;
	color: #fff;
	font-weight: 600;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	a {
		color: #fff;
		text-decoration: none;
		&:hover {
			text-decoration: underline;
		}
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 20px;
		font-weight: 700;
		line-height: 30px;
		margin-bottom: 4px;
	}
`;
