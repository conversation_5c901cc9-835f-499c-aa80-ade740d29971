import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { selectBestDay } from '@pages/CourtGrid/helpers';
import { useEffect, useLayoutEffect, useState } from 'react';
import { StringParam, useQueryParams } from 'use-query-params';

import { shiftTime } from '@/utils/time';

import { ALL_DAY_RANGE, ALL_DIVISIONS_KEY, INITIAL_TIME_WINDOW } from '../constants';
import { DivisionInfo } from '../types';
import { useCourtGridService } from './useCourtGridService';
import { useDivisionsMatchesTimeRanges } from './useDivisionsMatchesTimeRanges';

export const useCourtGridFilter = () => {
	const { divisions: eventDivisions } = useEventDetails();

	const [queryParams] = useQueryParams({
		divisionId: StringParam,
	});

	const { initialFilterOptions, saveFilterOptions } = useCourtGridService();

	const [divisionId, setDivisionId] = useState<string>('');
	const [day, setDay] = useState<string>('');
	const [timeRange, setTimeRange] = useState<[string, string] | null>(null);

	const [days, setDays] = useState<string[]>([]);
	const [divisions, setDivision] = useState<DivisionInfo[]>([]);
	const [timeRangeMinMax, setTimeRangeMinMax] = useState<[string, string]>(ALL_DAY_RANGE);

	const [selectedCourts, setSelectedCourts] = useState<string[]>([]);

	const divisionsMatchesTimeRanges = useDivisionsMatchesTimeRanges();
	useLayoutEffect(() => {
		if (!divisionsMatchesTimeRanges || !eventDivisions) return;
		// Setting divisionId: query params -> store -> all divisions
		const storeDivisionId = initialFilterOptions.divisionId;
		const divisionId = queryParams?.divisionId || storeDivisionId || ALL_DIVISIONS_KEY;
		setDivisionId(divisionId);
		// Taking ranges for all divisions (for now)
		const matchesTimeRanges = divisionsMatchesTimeRanges.get(ALL_DIVISIONS_KEY)!;
		const days = Array.from(matchesTimeRanges.keys());
		setDays(days);
		// Setting day: store -> best day
		const storeDay = initialFilterOptions.day;
		const day = storeDay || selectBestDay(days);
		setDay(day);
		const dayMatchesTimeRange = matchesTimeRanges.get(day);
		const timeRangeMinMax: [string, string] = dayMatchesTimeRange
			? [dayMatchesTimeRange.start_time, dayMatchesTimeRange.end_time]
			: ALL_DAY_RANGE;
		setTimeRangeMinMax(timeRangeMinMax);
		// Setting timeRange: store -> initial time window
		const storeTimeRange = initialFilterOptions.timeRange;
		const [timeRangeMin] = timeRangeMinMax;
		const timeRange: [string, string] = storeTimeRange || [
			timeRangeMin,
			shiftTime(timeRangeMin, INITIAL_TIME_WINDOW),
		];
		setTimeRange(timeRange);
		// Setting divisions based on the event divisions (for now)
		const divisions = eventDivisions.map(({ division_id, short_name }) => ({
			division_id,
			short_name,
		}));
		divisions.unshift({ division_id: ALL_DIVISIONS_KEY, short_name: 'All Divisions' });
		setDivision(divisions);
		// Setting selected courts
		setSelectedCourts(initialFilterOptions.courts || []);
	}, [divisionsMatchesTimeRanges]); // eslint-disable-line react-hooks/exhaustive-deps

	// Saving filter options
	useEffect(() => {
		if (day && timeRange && divisionId) {
			saveFilterOptions({ divisionId, day, timeRange, courts: selectedCourts });
		}
	}, [day, timeRange, divisionId, selectedCourts, saveFilterOptions]);

	const resetFilter = () => {
		setDivisionId(ALL_DIVISIONS_KEY);
		const [timeRangeMin] = timeRangeMinMax;
		setTimeRange([timeRangeMin, shiftTime(timeRangeMin, INITIAL_TIME_WINDOW)]);
		setSelectedCourts([]);
	};

	return {
		divisions,
		divisionId,
		setDivisionId,
		days,
		day,
		setDay,
		timeRangeMinMax,
		timeRange,
		setTimeRange,
		selectedCourts,
		setSelectedCourts,
		resetFilter,
		filterReady: !!divisionsMatchesTimeRanges,
	};
};
