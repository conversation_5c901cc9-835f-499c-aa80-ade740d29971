import { useMemo } from 'react';

import { alignToStep, formatUTCTime, getIntervalsInRange } from '@/utils/time';

import { INITIAL_GRID_TIME_STEP } from '../constants';
import { GridCourtMatches, GridTimePeriod } from '../types';

const ONE_HOUR = 60 * 60 * 1000;

export const useMatchesTimeGrid = (courtsMatches: GridCourtMatches[]) => {
	return useMemo<[GridTimePeriod[], number]>(() => {
		if (!courtsMatches) return [[], INITIAL_GRID_TIME_STEP];

		let minStartTimestamp = Infinity;
		let maxStartTimestamp = -Infinity;
		let gridTimeStep = INITIAL_GRID_TIME_STEP;
		courtsMatches.forEach(({ matches }) => {
			matches.forEach(({ secs_start, secs_end }) => {
				if (secs_start && secs_end) {
					minStartTimestamp = Math.min(minStartTimestamp, secs_start);
					maxStartTimestamp = Math.max(maxStartTimestamp, secs_start);
					const startMinutes = new Date(secs_start).getUTCMinutes();
					// Detecting 15 minutes step
					gridTimeStep = gridTimeStep > 15 && [15, 45].includes(startMinutes) ? 15 : gridTimeStep;
				}
			});
		});

		if (!isFinite(minStartTimestamp) || !isFinite(maxStartTimestamp)) {
			return [[], gridTimeStep];
		}

		// Choosing the end time based on the maximum start timestamp, taking into account that
		// the match can last more than an hour but should be displayed in an hour-ranged cell
		const alignedStartTime = alignToStep(new Date(minStartTimestamp), gridTimeStep);
		const alignedEndTime = alignToStep(new Date(maxStartTimestamp + ONE_HOUR), gridTimeStep);

		const gridTimesPeriods = getIntervalsInRange(
			alignedStartTime,
			alignedEndTime,
			gridTimeStep,
		).reduce((acc, date, i, allDates) => {
			const nextDate = allDates[i + 1];
			acc.push({
				periodStart: new Date(date).getTime(),
				periodEnd: nextDate ? new Date(nextDate).getTime() - 1 : Infinity, // -1 to avoid overlapping
				periodDisplayName: formatUTCTime(new Date(date), 'hh:mm'),
			});
			return acc;
		}, [] as GridTimePeriod[]);

		gridTimesPeriods.pop(); // Remove last period

		return [gridTimesPeriods, gridTimeStep];
	}, [courtsMatches]);
};
