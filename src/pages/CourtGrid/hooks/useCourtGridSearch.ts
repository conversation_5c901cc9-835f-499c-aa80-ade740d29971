import { useCallback, useEffect, useMemo, useState } from 'react';

import { GridCourtMatches } from '../types';

export const useCourtGridSearch = (courtsMatches: GridCourtMatches[]) => {
	const [search, setSearch] = useState('');
	const [focusedMatchIndex, setFocusedMatchIndex] = useState(-1);

	const foundMatchesIds = useMemo(() => {
		if (!search) return [];
		const ids: string[] = [];
		const lowerSearch = search.toLowerCase();
		courtsMatches.forEach(({ matches }) => {
			matches.forEach((m) => {
				if (
					m.external.team_display_name?.toLowerCase().includes(lowerSearch) ||
					m.external.opponent_display_name?.toLowerCase().includes(lowerSearch)
				) {
					ids.push(m.match_id);
				}
			});
		});
		return ids;
	}, [search, courtsMatches]);

	const navigateFoundMatches = useCallback(
		(type: 'prev' | 'next') => {
			setFocusedMatchIndex((prev) => {
				let id = '';
				let index = prev;
				if (type === 'prev' && foundMatchesIds[prev - 1]) {
					index = prev - 1;
				}

				if (type === 'next' && foundMatchesIds[prev + 1]) {
					index = prev + 1;
				}

				id = foundMatchesIds[index];

				if (id) {
					const element = document.getElementById(id);
					if (element) {
						element.scrollIntoView({
							behavior: 'instant',
							block: 'center',
							inline: 'center',
						});
					}
				}
				return index;
			});
		},
		[foundMatchesIds],
	);

	const resetMatchFocus = () => setFocusedMatchIndex(-1);

	useEffect(() => {
		resetMatchFocus();
	}, [search]);

	return {
		search,
		setSearch,
		foundMatchesCount: foundMatchesIds.length,
		focusedMatchIndex,
		focusedMatchId: foundMatchesIds[focusedMatchIndex] || null,
		navigateFoundMatches,
		resetMatchFocus,
	};
};
