import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useMemo } from 'react';

import { useCourtsRangeMatchesQuery } from '@/generated/graphql';

import { getMatchAlert } from '../helpers';
import { AlertType, GridCourtMatches } from '../types';

export const useCourtsMatches = (
	day: string,
	timeRange: [string, string] | null,
	divisionId: string | null,
) => {
	const { eswId } = useEventDetails();
	const [after, before] = useMemo<[string, string]>(() => {
		if (!day || !timeRange) return ['', ''];
		return [`${day}T${timeRange[0]}`, `${day}T${timeRange[1]}`];
	}, [day, timeRange]);

	const { data, loading } = useCourtsRangeMatchesQuery({
		variables: {
			eswId,
			after,
			before,
			divisionId,
			uniqKey: `${eswId}-${after}-${before}-${divisionId}`, // Unique key for cache related courts
		},
		skip: !(eswId && after && before),
	});

	const [courtsMatches, hasMoreCourtsMatches] = useMemo<[GridCourtMatches[], boolean]>(() => {
		if (!data) return [[], false];
		const { dayRangeMatches, relatedCourts } = data;
		const courtsMatches = relatedCourts.map((court) => {
			const { uuid } = court;
			const matches = dayRangeMatches.items.filter((match) => match.court_id === uuid);

			let courtTbMatchesCount = 0;
			let courtAlert: AlertType = null;
			matches.forEach((match) => {
				// Counting tie-breaker matches
				if (match.is_tb) {
					courtTbMatchesCount += 1;
				}
				// Evaluating merged alert for all matches in court
				const matchAlert = getMatchAlert(match);
				if (matchAlert === 'error') {
					courtAlert = 'error';
				} else if (matchAlert === 'warning' && courtAlert !== 'error') {
					courtAlert = 'warning';
				}
			});

			return { court, matches, courtAlert, courtTbMatchesCount };
		});
		return [courtsMatches, dayRangeMatches.filter_info.is_filtered];
	}, [data]);

	return {
		courtsMatches,
		// The flag to indicate if there are more matches outside the selected "timeRange" for the selected "day"
		hasMoreCourtsMatches,
		courtsMatchesLoading: loading,
	};
};
