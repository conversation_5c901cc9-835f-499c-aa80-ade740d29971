import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useCallback, useMemo } from 'react';

import { FilterOptions, courtGridService } from '@/services/courtGrid.service';

export const useCourtGridService = () => {
	const { eswId } = useEventDetails();

	const initialFilterOptions = useMemo(() => courtGridService.getFilterOptions(eswId), [eswId]);

	const saveFilterOptions = useCallback(
		(options: FilterOptions) => {
			courtGridService.saveFilterOptions(eswId, options);
		},
		[eswId],
	);

	return {
		initialFilterOptions,
		saveFilterOptions,
	};
};
