import { addOrdinalSuffix } from '@/utils';

import {
	Alert,
	Paper,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
} from '@mui/material';

import { EventPool } from '@/generated/graphql';

type PropsT = {
	standings: EventPool['standings'];
};
export const PoolStandings = ({ standings }: PropsT) => {
	const data = standings && standings[0].pb_stats;

	return (
		<div>
			<Alert severity="info">Standigs</Alert>
			<TableContainer component={Paper}>
				<Table>
					<TableHead>
						<TableRow>
							<TableCell></TableCell>
							<TableCell>
								<b>Team Name</b>
							</TableCell>
							<TableCell>
								<b>Match W-L</b>
							</TableCell>
							<TableCell>
								<b>Set W-L</b>
							</TableCell>
							<TableCell>
								<b>Point Ration</b>
							</TableCell>
						</TableRow>
					</TableHead>
					<TableBody>
						{data &&
							[...data]
								.sort((a, b) => Number(a.rank) - Number(b.rank))
								.map((item) => (
									<TableRow
										key={item.team_id}
										sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
									>
										<TableCell component="th" scope="row">
											{addOrdinalSuffix(Number(item.rank))}
										</TableCell>
										<TableCell>{item.name}</TableCell>
										<TableCell>
											{item.matches_won} - {item.matches_lost}
										</TableCell>
										<TableCell>
											{item.sets_won} - {item.sets_lost} ({parseInt(`${item.sets_pct}`)}%)
										</TableCell>
										<TableCell>{item.points_ratio?.toFixed(3)}</TableCell>
									</TableRow>
								))}
					</TableBody>
				</Table>
			</TableContainer>
		</div>
	);
};
