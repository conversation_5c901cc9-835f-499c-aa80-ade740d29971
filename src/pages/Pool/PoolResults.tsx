import {
	Alert,
	Paper,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
} from '@mui/material';

import { EventPool } from '@/generated/graphql';

type PropsT = {
	results: EventPool['results'];
};
export const PoolResults = ({ results }: PropsT) => {
	return (
		<div>
			<Alert severity="info">Results</Alert>
			<TableContainer component={Paper}>
				<Table>
					<TableHead>
						<TableRow>
							<TableCell>
								<b>Team 1</b>
							</TableCell>
							<TableCell>
								<b>Team 2</b>
							</TableCell>
							<TableCell>
								<b>Score</b>
							</TableCell>
						</TableRow>
					</TableHead>
					<TableBody>
						{results &&
							results.map((result) => (
								<TableRow
									key={result.match_id}
									sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
								>
									<TableCell component="th" scope="row">
										{result.team1_name}
									</TableCell>
									<TableCell>{result.team2_name}</TableCell>
									<TableCell style={{ position: 'relative' }}>
										{result.results.team1?.scores}
									</TableCell>
								</TableRow>
							))}
					</TableBody>
				</Table>
			</TableContainer>
		</div>
	);
};
