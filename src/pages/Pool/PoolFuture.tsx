import { addOrdinalSuffix } from '@/utils';

import { Alert, Paper, Table, TableBody, TableCell, TableContainer, TableRow } from '@mui/material';

import { EventPool } from '@/generated/graphql';

type PropsT = {
	future: EventPool['pb_finishes'];
};

export const PoolFuture = ({ future }: PropsT) => {
	return (
		<div>
			<Alert severity="info">Future</Alert>
			<TableContainer component={Paper}>
				<Table>
					<TableBody>
						{future &&
							future.teams.map((team, index) => (
								<TableRow
									key={team.team_id}
									sx={{ '&:last-child td, &:last-child th': { border: 0 } }}
								>
									<TableCell component="th" scope="row">
										<b>
											({addOrdinalSuffix(index + 1)}) {team.team_name}
										</b>{' '}
										Plays {team.next_match?.display_name} {team.next_match?.week_day}{' '}
										{team.next_match?.start_time_string} {team.next_match?.court}
									</TableCell>
								</TableRow>
							))}
					</TableBody>
				</Table>
			</TableContainer>
		</div>
	);
};
