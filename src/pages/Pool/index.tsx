import { useParams } from 'react-router-dom';
import { Link } from 'react-router-dom';

import { Loader } from '@/components/Loader';

import { usePoolQuery } from '@/generated/graphql';
import { EventPool as PoolT } from '@/generated/graphql';

import { PoolFuture } from './PoolFuture';
import { PoolResults } from './PoolResults';
import { PoolStandings } from './PoolStandings';

const Pool = () => {
	const { id, poolId, divisionId } = useParams();

	const { loading, data } = usePoolQuery({
		variables: {
			id: id!,
			poolId: poolId!,
		},
	});

	const pool = data?.pool;

	return (
		<div>
			{loading && <Loader />}
			{pool && (
				<>
					<h1>
						{pool?.prev && (
							<Link to={`/events/${id}/${divisionId}/pools/${pool.prev.uuid}`}>
								{pool.prev.name}
							</Link>
						)}
						{pool.division_short_name} {pool.display_name}
						{pool?.next && (
							<Link to={`/events/${id}/${divisionId}/pools/${pool.next.uuid}`}>
								{pool.next.name}
							</Link>
						)}
					</h1>
					{pool.results && <PoolResults results={pool.results as PoolT['results']} />}
					{pool.standings && <PoolStandings standings={pool.standings as PoolT['standings']} />}
					{pool.pb_finishes && <PoolFuture future={pool.pb_finishes as PoolT['pb_finishes']} />}
				</>
			)}
		</div>
	);
};

export default Pool;
