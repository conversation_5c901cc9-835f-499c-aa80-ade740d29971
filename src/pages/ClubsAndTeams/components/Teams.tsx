import { PaginatedClub, PaginatedClubTeam } from '@/shared/types/club.types';
import expandLessIcon from '@assets/expandLess-icon.svg';
import expandMoreIcon from '@assets/expandMore.icon.svg';
import inFavoriteSmallIcon from '@assets/inFavoriteSmall-icon.svg';
import outFavoriteSmallIcon from '@assets/outFavoriteSmall-icon.svg';
import { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { StringParam, useQueryParams } from 'use-query-params';

import { StandingResult } from '@/components/StandingResult';

import { addShortCourtNamePrefix } from '@/utils/prefixer';
import { getFormateTime } from '@/utils/time';

import {
	ClubsAndTeams__Code,
	ClubsAndTeams__Count,
	ClubsAndTeams__FavoriteIcon,
	ClubsAndTeams__Footer,
	ClubsAndTeams__ShadowBottom,
	ClubsAndTeams__ShadowTop,
	ClubsAndTeams__TeamCourt,
	ClubsAndTeams__TeamDescription,
	ClubsAndTeams__TeamDescriptionCourtSection,
	ClubsAndTeams__TeamDescriptionInfoSection,
	ClubsAndTeams__TeamItem,
	ClubsAndTeams__TeamList,
	ClubsAndTeams__TeamWrapper,
} from '../styled';

type Props = {
	club: PaginatedClub;
	favoriteTeamsIds: string[];
	toggleTeamFavorite: (_team: PaginatedClubTeam) => void;
	openTeamModal: (_teamId: string) => void;
	openPoolModal: (_teamId: string) => void;
	openBracketModal: (_team: PaginatedClubTeam) => void;
};

export const Teams = ({
	club,
	favoriteTeamsIds,
	toggleTeamFavorite,
	openTeamModal,
	openPoolModal,
	openBracketModal,
}: Props) => {
	const { teams } = club;
	const { search } = useLocation();
	const [isShowTeams, setIsShowTeams] = useState(false);

	useEffect(() => {
		const searchValue = search.split('=')[1];

		if (searchValue) {
			setIsShowTeams(true);
		} else {
			setIsShowTeams(false);
		}
	}, [search]);

	const showTeamsHandler = () => {
		setIsShowTeams(!isShowTeams);
	};

	const [, setQueryParams] = useQueryParams({
		divisionId: StringParam,
	});

	return (
		<>
			<ClubsAndTeams__Footer $isOpen={isShowTeams}>
				<ClubsAndTeams__Code>{club.club_code}</ClubsAndTeams__Code>
				<ClubsAndTeams__Count onClick={showTeamsHandler}>
					{club.teams_count} Teams
					<img src={isShowTeams ? expandLessIcon : expandMoreIcon} alt="" />
				</ClubsAndTeams__Count>
			</ClubsAndTeams__Footer>
			{isShowTeams && (
				<ClubsAndTeams__TeamWrapper>
					<ClubsAndTeams__TeamList>
						{teams.map((team) => (
							<div key={team.team_id}>
								{!team.next_match ? (
									<div>
										<ClubsAndTeams__TeamItem>
											<ClubsAndTeams__TeamDescription
												$isPlayed={true}
												onClick={() => openTeamModal(team.team_id)}
											>
												<ClubsAndTeams__TeamDescriptionCourtSection>
													{team.division_name}
												</ClubsAndTeams__TeamDescriptionCourtSection>
												<ClubsAndTeams__TeamDescriptionInfoSection>
													<div>
														<ClubsAndTeams__FavoriteIcon
															onClick={(e) => {
																e.stopPropagation();
																toggleTeamFavorite(team);
															}}
															src={
																favoriteTeamsIds.includes(team.team_id!)
																	? inFavoriteSmallIcon
																	: outFavoriteSmallIcon
															}
															alt=""
														/>
														<strong>{team.team_name}</strong>
													</div>
												</ClubsAndTeams__TeamDescriptionInfoSection>
											</ClubsAndTeams__TeamDescription>
											<ClubsAndTeams__TeamCourt $isPlayed={true}>
												<p
													onClick={() => {
														openBracketModal(team);
													}}
												>
													<strong>
														<StandingResult standing={team.division_standing} />
													</strong>
												</p>
											</ClubsAndTeams__TeamCourt>
										</ClubsAndTeams__TeamItem>
									</div>
								) : (
									<ClubsAndTeams__TeamItem>
										<ClubsAndTeams__TeamDescription
											$isPlayed={false}
											onClick={() => openTeamModal(team.team_id)}
										>
											<ClubsAndTeams__TeamDescriptionCourtSection>
												{team.division_name}
											</ClubsAndTeams__TeamDescriptionCourtSection>
											<ClubsAndTeams__TeamDescriptionInfoSection>
												<div>
													<ClubsAndTeams__FavoriteIcon
														onClick={(e) => {
															e.stopPropagation();
															toggleTeamFavorite(team);
														}}
														src={
															favoriteTeamsIds.includes(team.team_id)
																? inFavoriteSmallIcon
																: outFavoriteSmallIcon
														}
														alt=""
													/>
													<strong>{team.team_name}</strong>
												</div>
												<div>vs {team.next_match?.external.opponent_display_name}</div>
											</ClubsAndTeams__TeamDescriptionInfoSection>
										</ClubsAndTeams__TeamDescription>
										<ClubsAndTeams__TeamCourt
											$isPlayed={false}
											onClick={() => {
												setQueryParams({ divisionId: team?.division_id });
												openPoolModal(team.team_id);
											}}
										>
											<div>
												<p>
													{getFormateTime({
														time: team.next_match.secs_start!,
														format: 'EEE, h:mmaaa',
													})}
												</p>
												<p>
													{addShortCourtNamePrefix(
														team.next_match.external.court_info?.short_name || '',
													)}
												</p>
											</div>
										</ClubsAndTeams__TeamCourt>
									</ClubsAndTeams__TeamItem>
								)}
							</div>
						))}
					</ClubsAndTeams__TeamList>
				</ClubsAndTeams__TeamWrapper>
			)}
			{!isShowTeams && (
				<>
					<ClubsAndTeams__ShadowTop />
					<ClubsAndTeams__ShadowBottom />
				</>
			)}
		</>
	);
};
