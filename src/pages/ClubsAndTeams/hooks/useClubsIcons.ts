import { countCommonElements } from '@/utils';
import fullFavoriteBigIcon from '@assets/favoriteCheckedBig-icon.svg';
import outFavoriteBigIcon from '@assets/favoriteUnchecked-icon.svg';
import halfFavoriteBigIcon from '@assets/halfFavoriteBig-icon.svg';
import { useMemo } from 'react';

import { Club, Team } from '@/generated/graphql';

type TeamWithId = Pick<Team, 'team_id'>;
type ClubWithTeamsIds = Pick<Club, 'roster_club_id'> & { teams: TeamWithId[] };
type ClubsIcons = Record<string, string>;

export const useClubsIcons = (clubs: ClubWithTeamsIds[], favoriteTeamsIds: string[]) => {
	const clubsIcons = useMemo(() => {
		return clubs.reduce<ClubsIcons>((acc, club) => {
			const teamsIds = club.teams.map((team) => team.team_id);
			const commonIdsCount = countCommonElements(teamsIds, favoriteTeamsIds);
			if (favoriteTeamsIds.length && commonIdsCount && commonIdsCount === teamsIds.length) {
				acc[club.roster_club_id] = fullFavoriteBigIcon; // If all teams are favorite
			} else if (!commonIdsCount) {
				acc[club.roster_club_id] = outFavoriteBigIcon; // If no teams are favorite
			} else {
				acc[club.roster_club_id] = halfFavoriteBigIcon; // If some teams are favorite
			}
			return acc;
		}, {});
	}, [clubs, favoriteTeamsIds]);

	return (club: ClubWithTeamsIds) => clubsIcons[club.roster_club_id] || outFavoriteBigIcon;
};
