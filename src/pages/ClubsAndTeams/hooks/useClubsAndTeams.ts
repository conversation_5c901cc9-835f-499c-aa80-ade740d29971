import { SEARCH_DEBOUNCE_TIME } from '@/config';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { PaginatedClub, PaginatedClubTeam } from '@/shared/types/club.types';
import { useSearchResultCounterStore } from '@/store/searchResultCounter.store';
import { sanitizeSearchInput } from '@/utils';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDebounce } from 'use-debounce';
import { StringParam, useQueryParams } from 'use-query-params';

import { usePaginatedClubsAndTeams } from './usePaginatedClubsAndTeams';

export const useClubsAndTeams = () => {
	const { eswId, event, loading: eventLoading } = useEventDetails();
	const [queryParams] = useQueryParams({ search: StringParam });
	const { setCount } = useSearchResultCounterStore();

	const [search, setSearch] = useState(queryParams.search || '');
	const [searchDebounce] = useDebounce(sanitizeSearchInput(search), SEARCH_DEBOUNCE_TIME);
	const onChangeSearch = useCallback((value: string) => {
		setSearch(value);
	}, []);

	const {
		fetchNext,
		clubs,
		clubsCount,
		loading: clubsLoading,
		isFetched,
		pagesResponses,
	} = usePaginatedClubsAndTeams({
		eswId,
		search: searchDebounce,
	});

	const loading = clubsLoading || eventLoading;

	const [processedClubs, processedClubsCount] = useMemo(() => {
		if (!event) return [[], 0];
		if (!event.teams_settings?.manual_club_names) return [clubs, clubsCount];
		// If manual club names are enabled, we need to reconstruct the clubs array using manual club names from the teams of the first club
		const [virtualClub] = clubs;
		if (!virtualClub) return [[], 0];
		const allTeams = virtualClub.teams;
		const manualClubsTeamsMap = allTeams.reduce<Map<string, PaginatedClubTeam[]>>((acc, team) => {
			const { manual_club_name } = team;
			if (!manual_club_name) return acc;
			if (!acc.has(manual_club_name)) {
				acc.set(manual_club_name, []);
			}
			acc.get(manual_club_name)?.push(team);
			return acc;
		}, new Map());
		// Extract clubs names from the manualClubsTeamsMap, and repack clubs array
		const state = virtualClub.state;
		const clubsNames = Array.from(manualClubsTeamsMap.keys()).toSorted();
		const processedClubs = clubsNames.map<PaginatedClub>((club_name: string) => {
			// Using manual club name as the club ID for teams with manual club names
			const teams = manualClubsTeamsMap
				.get(club_name)!
				.map((team) => ({ ...team, club_id: club_name }));
			return {
				roster_club_id: club_name,
				club_name,
				club_code: '',
				teams,
				teams_count: teams.length,
				state,
			};
		});
		return [processedClubs, processedClubs.length];
	}, [clubs, clubsCount, event]);

	useEffect(() => {
		setSearch(queryParams.search || '');
	}, [queryParams.search]);
	useEffect(() => {
		if (!isFetched || loading) return;

		pagesResponses?.length && setCount({ clubsCount: processedClubsCount });
	}, [processedClubsCount, isFetched, loading, pagesResponses, setCount]);

	return {
		clubs: processedClubs,
		loading,
		search,
		onChangeSearch,
		fetchNext,
	};
};
