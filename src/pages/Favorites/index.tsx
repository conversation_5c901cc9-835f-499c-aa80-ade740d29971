import { useAnalytics } from '@/shared/hooks/useAnalytics';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';

import { SearchBar } from '@/components/SearchBar';
import { Tabs } from '@/components/Tabs';

import { placeholders } from '@/config/searchPlaceholder';

import { useFavorites } from './hooks/useFavorites';
import { Favorites__Wrapper } from './styled';

type Props = {
	isTab?: boolean;
};

const Favorites = ({ isTab }: Props) => {
	const { search, onChangeSearch, activeTab, setActiveTab, tabs } = useFavorites(isTab);
	useAnalytics(`Favorites (${activeTab})`, search);

	const { browserHeight } = useCurrentSize();
	const { isDesktopFixed } = useDesktopSticky({ page: 'favorites' });

	return (
		<>
			{!isTab && (
				<SearchBar
					placeholder={
						activeTab === 'Teams' ? placeholders.favorites_standings : placeholders.favorites_teams
					}
					search={search}
					onChangeSearch={onChangeSearch}
				/>
			)}
			<Favorites__Wrapper
				$browserHeight={browserHeight}
				$isDesktopFixed={isDesktopFixed}
				$isTab={isTab}
			>
				<Tabs activeTab={activeTab} handleChange={(_e, value) => setActiveTab(value)} tabs={tabs} />
			</Favorites__Wrapper>
		</>
	);
};

export default Favorites;
