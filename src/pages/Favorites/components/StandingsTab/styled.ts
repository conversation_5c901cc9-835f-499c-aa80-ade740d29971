import { Link } from 'react-router-dom';
import styled from 'styled-components';

export const StandingsTab__Wrapper = styled.div`
	padding: 16px 0 40px 0;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 0;
	}
`;
export const StandingsTab__Group = styled.div`
	&:last-child {
		padding: 0;
	}
	table {
		width: 100%;
		border-collapse: collapse;
		tbody {
			vertical-align: top;

			tr {
				border-bottom: 1px solid #dfe3e8;
				&:last-child {
					border-bottom: none;
				}
				td {
					color: #454f5b;
					position: relative;
					font-size: 14px;
					line-height: 18px;
					font-weight: 400;
					padding: 16px 4px 16px;
					@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
						font-size: 14px;
						line-height: 22px;
					}
					&:first-child {
						padding: 16px 4px 16px 16px;
						width: 50px;
						font-weight: 700;
					}
					&:nth-child(2) {
						width: 16px;
						span {
							padding-top: 2px;
							display: inline-block;
						}
					}
					&:nth-child(3) {
						max-width: 130px;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
						@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
							/* background: green; */
							min-width: 230px;
						}
					}
					&:nth-child(4) {
						white-space: nowrap;
						width: 40px;
					}
					&:nth-child(5) {
						width: 40px;
						white-space: nowrap;
					}
					&:nth-child(6) {
						width: 60px;
						white-space: nowrap;
					}
					&:last-child {
						width: 70px;
						white-space: nowrap;
						padding: 16px 16px 16px 4px;
					}
				}
			}
		}
	}
`;
export const StandingsTab__GroupTitle = styled.div`
	background: #f4f6f8;
	padding: 8px 16px;
	font-size: 14px;
	font-weight: 700;
	line-height: 18px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 14px;
		line-height: 22px;
	}
`;

export const StandingsTab__DivisionLink = styled(Link)`
	font-size: 12px;
	font-weight: 700;
	line-height: 18px;
	color: ${(props) => props.theme.colors.blue};
	text-decoration: none;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 14px;
		font-weight: 700;
		line-height: 22px;
	}
`;

export const StandingsTab__TeamName = styled.span`
	cursor: pointer;
	&:hover {
		text-decoration: underline;
	}
`;
