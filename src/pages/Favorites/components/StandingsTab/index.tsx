import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useTeamModal } from '@/shared/hooks/useTeamModal';
import { FavoriteTeam } from '@/shared/types/team.types';
import { addOrdinalSuffix } from '@/utils';
import inFavoriteSmallIcon from '@assets/inFavoriteSmall-icon.svg';
import { StringParam, useQueryParams } from 'use-query-params';

import { Loader } from '@/components/Loader';
import { NotFound } from '@/components/NotFound';
import { StandingMatchResult } from '@/components/StandingMatchResult';
import { StandingPointsRatio } from '@/components/StandingPointsRatio';
import { StandingSetsPercentage } from '@/components/StandingSetsPercentage';
import { StandingSetsResult } from '@/components/StandingSetsResult';

import { UNKNOWN_STANDING_GROUP } from '../../constants';
import { StandingGroup } from '../../hooks/useTeamsStandingGroups';
import {
	StandingsTab__DivisionLink,
	StandingsTab__Group,
	StandingsTab__GroupTitle,
	StandingsTab__TeamName,
	StandingsTab__Wrapper,
} from './styled';

type Props = {
	standings: StandingGroup[];
	standingsLoading: boolean;
	toggleTeamFavorite: (_team: FavoriteTeam) => void;
};

export const StandingsTab = ({ standings, standingsLoading, toggleTeamFavorite }: Props) => {
	const { eswId, event } = useEventDetails();
	const { teamModalElement, openTeamModal, teamDataLoading } = useTeamModal();
	const hideSeeds = !!event?.hide_seeds;

	const loading = standingsLoading || teamDataLoading;

	const renderRankOrSeed = ({ division_standing }: FavoriteTeam) => {
		const hasRank = !!division_standing?.rank;
		if (hasRank || !hideSeeds) {
			const value = (hasRank ? division_standing?.rank : division_standing?.seed) || 0;
			return <span>{addOrdinalSuffix(value)}</span>;
		}
		return <span>-</span>;
	};

	const [, setQueryParams] = useQueryParams({
		divisionId: StringParam,
	});

	return (
		<>
			{loading && <Loader />}
			{!standingsLoading && !standings.length && <NotFound type="favorites" />}
			{teamModalElement}
			<StandingsTab__Wrapper>
				{standings.map((data) => {
					return (
						<StandingsTab__Group key={data.heading}>
							{data.heading !== UNKNOWN_STANDING_GROUP && (
								<StandingsTab__GroupTitle>{data.heading}</StandingsTab__GroupTitle>
							)}
							<table>
								<tbody>
									{data.teams.map((team) => {
										return (
											<tr key={team.team_id}>
												<td>{renderRankOrSeed(team)}</td>
												<td>
													<span>
														<img
															src={inFavoriteSmallIcon}
															alt=""
															onClick={(e) => {
																e.stopPropagation();
																toggleTeamFavorite(team);
															}}
														/>
													</span>
												</td>
												<td>
													<StandingsTab__TeamName
														onClick={() => {
															setQueryParams({ divisionId: team?.division_id });
															openTeamModal(team.team_id);
														}}
													>
														{team.team_name}
													</StandingsTab__TeamName>
												</td>
												<td>
													<span>
														<StandingMatchResult standing={team.division_standing} />
													</span>
												</td>
												<td>
													<StandingSetsResult standing={team.division_standing} />
												</td>
												<td>
													<StandingSetsPercentage standing={team.division_standing} />
												</td>
												<td>
													<StandingPointsRatio standing={team.division_standing} />
												</td>
												<td>
													<StandingsTab__DivisionLink
														to={`/events/${eswId}/divisions/${team.division_id}/standings`}
													>
														{team.division_name}
													</StandingsTab__DivisionLink>
												</td>
											</tr>
										);
									})}
								</tbody>
							</table>
						</StandingsTab__Group>
					);
				})}
			</StandingsTab__Wrapper>
		</>
	);
};
