import { useBracketModal } from '@/shared/hooks/useBracketModal';
import { usePoolModal } from '@/shared/hooks/usePoolModal';
import { useTeamModal } from '@/shared/hooks/useTeamModal';
import { FavoriteTeam } from '@/shared/types/team.types';
import inFavoriteSmallIcon from '@assets/favorite-24.svg';
import { StringParam, useQueryParams } from 'use-query-params';

import { Loader } from '@/components/Loader';
import { NotFound } from '@/components/NotFound';
import { StandingResult } from '@/components/StandingResult';

import { addShortCourtNamePrefix } from '@/utils/prefixer';
import { getFormateTime } from '@/utils/time';

import {
	ClubsAndTeams__FavoriteIcon,
	ClubsAndTeams__TeamCourt,
	ClubsAndTeams__TeamDescription,
	ClubsAndTeams__TeamDescriptionCourtSection,
	ClubsAndTeams__TeamDescriptionInfoSection,
	ClubsAndTeams__TeamItem,
} from '@/pages/ClubsAndTeams/styled';

import { Favorites__List } from '../../styled';

type Props = {
	isTab?: boolean;
	teams: FavoriteTeam[];
	teamsLoading: boolean;
	toggleTeamFavorite: (_team: FavoriteTeam) => void;
};

export const TeamsTab = ({ isTab, teams, teamsLoading, toggleTeamFavorite }: Props) => {
	const { teamModalElement, openTeamModal, teamDataLoading } = useTeamModal();
	const { poolModalElement, openPoolModal, poolDataLoading } = usePoolModal();
	const { bracketModalElement, openBracketModal, bracketDataLoading } =
		useBracketModal(openPoolModal);

	const loading = teamsLoading || teamDataLoading || poolDataLoading || bracketDataLoading;

	const [, setQueryParams] = useQueryParams({
		divisionId: StringParam,
	});

	return (
		<>
			{loading && <Loader />}
			{teamModalElement}
			{poolModalElement}
			{bracketModalElement}
			{!teamsLoading && !teams.length && <NotFound type="favorites" />}{' '}
			<Favorites__List $isTab={isTab}>
				{teams.map((team) => (
					<div key={team.team_id}>
						{!team.next_match ? (
							<ClubsAndTeams__TeamItem>
								<ClubsAndTeams__TeamDescription $isPlayed $isFavorite>
									<ClubsAndTeams__TeamDescriptionCourtSection $isFavorite>
										{team.division_name}
									</ClubsAndTeams__TeamDescriptionCourtSection>
									<ClubsAndTeams__TeamDescriptionInfoSection>
										<div>
											<ClubsAndTeams__FavoriteIcon
												onClick={(e) => {
													e.stopPropagation();
													toggleTeamFavorite(team);
												}}
												src={inFavoriteSmallIcon}
												alt=""
											/>
											<strong
												onClick={() => {
													setQueryParams({ divisionId: team?.division_id });
													openTeamModal(team.team_id);
												}}
											>
												{team.team_name}
											</strong>
										</div>
										<div>{team.team_code}</div>
									</ClubsAndTeams__TeamDescriptionInfoSection>
								</ClubsAndTeams__TeamDescription>
								<ClubsAndTeams__TeamCourt $isPlayed>
									<p onClick={() => openBracketModal(team)}>
										<strong>
											<StandingResult standing={team.division_standing} />
										</strong>
									</p>
								</ClubsAndTeams__TeamCourt>
							</ClubsAndTeams__TeamItem>
						) : (
							<ClubsAndTeams__TeamItem>
								<ClubsAndTeams__TeamDescription $isPlayed={false}>
									<ClubsAndTeams__TeamDescriptionCourtSection>
										{team.division_name}
									</ClubsAndTeams__TeamDescriptionCourtSection>
									<ClubsAndTeams__TeamDescriptionInfoSection>
										<div>
											<ClubsAndTeams__FavoriteIcon
												onClick={(e) => {
													e.stopPropagation();
													toggleTeamFavorite(team);
												}}
												src={inFavoriteSmallIcon}
												alt=""
											/>

											<strong
												onClick={() => {
													setQueryParams({ divisionId: team?.division_id });
													openTeamModal(team.team_id);
												}}
											>
												{team.team_name}
											</strong>
										</div>
										<div>{team.team_code}</div>
									</ClubsAndTeams__TeamDescriptionInfoSection>
								</ClubsAndTeams__TeamDescription>
								<ClubsAndTeams__TeamCourt $isPlayed={false}>
									<div>
										<p
											onClick={() => {
												setQueryParams({ divisionId: team?.division_id });
												openBracketModal(team);
											}}
										>
											{getFormateTime({
												time: team.next_match.secs_start!,
												format: 'EEE, h:mmaaa',
											})}
										</p>
										<p>
											{addShortCourtNamePrefix(
												team.next_match.external.court_info?.short_name || '',
											)}
										</p>
									</div>
								</ClubsAndTeams__TeamCourt>
							</ClubsAndTeams__TeamItem>
						)}
					</div>
				))}
			</Favorites__List>
		</>
	);
};
