import { SEARCH_DEBOUNCE_TIME } from '@/config';
import { useFavoriteTeams } from '@/shared/hooks/useFavoriteTeams';
import { useSearchResultCounterStore } from '@/store/searchResultCounter.store';
import { sanitizeSearchInput } from '@/utils';
import { useCallback, useEffect, useState } from 'react';
import { useDebounce } from 'use-debounce';
import { StringParam, useQueryParams } from 'use-query-params';

import { StandingsTab } from '../components/StandingsTab';
import { TeamsTab } from '../components/TeamsTab';
import { useFavoriteTeamsStanding } from './useFavoriteTeamsStanding';
import { useTeamsStandingGroups } from './useTeamsStandingGroups';

export const useFavorites = (isTab?: boolean) => {
	const [activeTab, setActiveTab] = useState('Teams');
	const [queryParams] = useQueryParams({ search: StringParam });
	const { setCount } = useSearchResultCounterStore();

	const [search, setSearch] = useState(queryParams.search || '');
	const [searchDebounce] = useDebounce(sanitizeSearchInput(search), SEARCH_DEBOUNCE_TIME);
	const onChangeSearch = useCallback((value: string) => {
		setSearch(value);
	}, []);

	const { favoriteTeamsIds, toggleTeamFavorite } = useFavoriteTeams();

	const { teams, loading, isFetched } = useFavoriteTeamsStanding(favoriteTeamsIds, searchDebounce);
	const standings = useTeamsStandingGroups(teams);

	useEffect(() => {
		setSearch(queryParams.search || '');
	}, [queryParams.search]);

	useEffect(() => {
		if (!isFetched || loading) return;

		setCount({ favoritesCount: teams.length });
	}, [teams.length, loading, setCount, isFetched]);

	const TABS = {
		Teams: (
			<TeamsTab
				isTab={isTab}
				teams={teams}
				teamsLoading={loading}
				toggleTeamFavorite={toggleTeamFavorite}
			/>
		),
		Standings: (
			<StandingsTab
				standings={standings}
				standingsLoading={loading}
				toggleTeamFavorite={toggleTeamFavorite}
			/>
		),
	};

	return {
		activeTab,
		setActiveTab,
		tabs: Object.entries(TABS),
		search,
		onChangeSearch,
	};
};
