import { Link } from 'react-router-dom';
import styled from 'styled-components';

export const BracketTemplates__Wrapper = styled.div<{ $browserHeight: number }>`
	background: #f9fafb;

	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding-bottom: 55px;
		overflow: hidden;
		width: calc(100% - 120px);
		width: 100%;
		margin: 0 auto;
		border-radius: 4px;
		background: #fff;
		min-height: ${(props) => props.$browserHeight - 304}px;
		background: #fff;
		z-index: 9;
		box-shadow:
			0px 12px 24px -4px rgba(145, 158, 171, 0.12),
			0px 0px 2px 0px rgba(145, 158, 171, 0.2);
	}
`;

export const BracketTemplates__List = styled.ul`
	padding: 16px 16px 66px 16px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		width: 600px;
		margin: auto;
		padding: 0 0 66px;
	}
`;
export const BracketTemplates__Item = styled.li`
	position: relative;
	margin: 0 0 16px;
	border-radius: 8px;
	border: 1px solid #f4f6f8;
	padding: 24px 16px;
`;

export const BracketTemplates__Link = styled(Link)`
	text-decoration: none;
	display: flex;
	justify-content: space-between;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		&:hover {
			div:first-child {
				text-decoration: underline;
			}
		}
	}
`;
export const BracketTemplates__Title = styled.div`
	font-size: 14px;
	line-height: 22px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
	}
`;
export const BracketTemplates__Header = styled.div`
	padding: 16px;
	display: flex;
	justify-content: center;
`;
export const BracketTemplates__BackLinkWrapper = styled.div`
	padding: 20px 0 0;
	a {
		display: flex;
		align-items: center;
		font-size: 14px;
		line-height: 22px;
		text-decoration: none;
		&:hover {
			text-decoration: underline;
		}
	}
`;
export const BracketTemplates__MainTitle = styled.h1`
	text-align: center;
	margin-bottom: 16px;
	font-size: 22px;
`;
export const BracketTemplates__BracketSingleWrapper = styled.div``;
export const BracketTemplates__HeaderWrapper = styled.div`
	background: #fff;
	position: relative;
	z-index: 9;
`;
