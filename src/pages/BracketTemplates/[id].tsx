import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import axios from 'axios';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Link } from 'react-router-dom';

import arrowLeft from '@/assets/arrowLeftBlack.svg';

import { BracketSingle } from '@/components/BracketModal/components/BracketSingle';
import { IBracketTemplate } from '@/components/BracketModal/types';
import { Loader } from '@/components/Loader';

import { DivisionPool } from '@/generated/graphql';

import {
	BracketTemplates__BackLinkWrapper,
	BracketTemplates__BracketSingleWrapper,
	BracketTemplates__Header,
	BracketTemplates__HeaderWrapper,
	BracketTemplates__MainTitle,
	BracketTemplates__Wrapper,
} from './styled';

const BracketTemplate = () => {
	const { browserHeight } = useCurrentSize();
	const [bracketTemplate, setBracketTemplate] = useState<IBracketTemplate>();
	const [isLoading, setIsLoading] = useState(false);
	const { id } = useParams();

	useEffect(() => {
		const getTemplate = async () => {
			setIsLoading(true);
			const response = await axios.get(`${import.meta.env.VITE_BRACKETS_API_URL}/bracket/${id}`);
			setBracketTemplate(response.data);
			setIsLoading(false);
		};

		getTemplate();
	}, [id]);

	return (
		<>
			{isLoading && <Loader />}
			<BracketTemplates__Wrapper $browserHeight={browserHeight}>
				<BracketTemplates__HeaderWrapper>
					<BracketTemplates__BackLinkWrapper>
						<Link to="/templates">
							<img src={arrowLeft} alt="arrow left" />
							Templates
						</Link>
					</BracketTemplates__BackLinkWrapper>
					<BracketTemplates__Header>
						{bracketTemplate && (
							<BracketTemplates__MainTitle>
								Bracket name: {bracketTemplate?.name}
							</BracketTemplates__MainTitle>
						)}
					</BracketTemplates__Header>
				</BracketTemplates__HeaderWrapper>
				<BracketTemplates__BracketSingleWrapper>
					{bracketTemplate && (
						<BracketSingle
							isTemplate
							close={close}
							bracketTemplate={bracketTemplate}
							currentRound={{} as DivisionPool}
							bracketRounds={[] as DivisionPool[]}
							setCurrentPoolId={() => null}
						/>
					)}
				</BracketTemplates__BracketSingleWrapper>
			</BracketTemplates__Wrapper>
		</>
	);
};

export default BracketTemplate;
