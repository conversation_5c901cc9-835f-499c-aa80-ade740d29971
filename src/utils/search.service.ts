import { ClubsWithTeamsArray, ClubsWithTeamsTeam } from '@/shared/types/clubWithTeams.types';

import { IMatchData } from '@/components/BracketModal/types';

import {
	CourtMatchesCourtMatch,
	DivisionPoolTeam,
	EventDivision, // EventQualifiedTeam,
	EventTeamRosterAthlete,
	EventTeamRosterStaff,
	TeamDetails,
} from '@/generated/graphql';

type RosterExtraT = {
	club_name: string;
	team_name: string;
	state: string;
	organization_code: string;
};
type SearchT =
	| { page: 'favorites'; data: ClubsWithTeamsTeam[] }
	| { page: 'clubs-teams'; data: ClubsWithTeamsArray[] }
	| {
			page: 'roster-athlete';
			data: (EventTeamRosterAthlete & RosterExtraT)[];
	  }
	| {
			page: 'roster-staff';
			data: (EventTeamRosterStaff & RosterExtraT)[];
	  }
	| { page: 'divisions'; data: EventDivision[] }
	| { page: 'division-teams'; data: ClubsWithTeamsTeam[] }
	| { page: 'division-pools-brackets'; data: DivisionPoolTeam[] }
	| { page: 'division-standings'; data: TeamDetails[] }
	| { page: 'court-grid'; data: CourtMatchesCourtMatch[] }
	// | { page: 'previously-qualified'; data: EventQualifiedTeam[] }
	| { page: 'brackets'; data: IMatchData }
	| { page: 'global-favorites'; data: ClubsWithTeamsTeam[] }
	| { page: 'global-clubs-teams'; data: ClubsWithTeamsArray[] }
	| { page: 'global-divisions'; data: EventDivision[] }
	| { page: 'global-roster'; data: (EventTeamRosterAthlete & RosterExtraT)[] };

export class SearchService {
	private STATE_LENGTH = 2;

	private prepareSearchData(search: string) {
		return search.trim().toLowerCase();
	}

	private searchByFavorites(search: string, data: ClubsWithTeamsTeam[]) {
		if (search.length === this.STATE_LENGTH) {
			return data.filter((item) => item.club_state?.toLowerCase().includes(search));
		}
		return data.filter(
			(item) =>
				item.club_name?.toLowerCase().includes(search) ||
				item.team_name?.toLowerCase().includes(search) ||
				item.team_code?.toLowerCase().includes(search),
		);
	}

	private searchByClubsTeams(search: string, data: ClubsWithTeamsArray[]) {
		if (search.length === this.STATE_LENGTH) {
			return data.filter((item) => {
				return item.club_state?.toLowerCase().includes(search);
			});
		}
		return data.filter((item) => {
			return (
				item.club_name?.toLowerCase().includes(search) ||
				item.club_code?.toLowerCase().includes(search) ||
				!!item.teams.find(
					(team) =>
						team.team_name?.toLowerCase().includes(search) ||
						team.team_code?.toLowerCase().includes(search),
				)
			);
		});
	}

	private searchByDivisions(search: string, data: EventDivision[]) {
		return data.filter((item) => item.name?.toLowerCase().includes(search));
	}
	private searchByDivisionTeams(search: string, data: ClubsWithTeamsTeam[]) {
		if (search.length === this.STATE_LENGTH) {
			return data.filter((item) => item.club_state?.toLowerCase().includes(search));
		}
		return data.filter((item) => item.team_name.toLowerCase().includes(search));
	}

	private searchByDivisionStandings(search: string, data: TeamDetails[]) {
		return data.filter(
			(item) => item.team_name?.toLowerCase().includes(search) && item.rank !== null,
		);
	}

	// private searchByPreviouslyQualified(search: string, data: EventQualifiedTeam[]) {
	// 	return data.filter((item) => {
	// 		return (
	// 			item.team_name?.toLowerCase().includes(search) ||
	// 			item.division?.toLowerCase().includes(search) ||
	// 			item.bid_earned?.toLowerCase().includes(search) ||
	// 			item.earned_at?.toLowerCase().includes(search)
	// 		);
	// 	});
	// }

	private searchByCourtGrid(search: string, data: CourtMatchesCourtMatch[]) {
		return data.filter((item) => {
			return (
				item?.team_1_name?.toLowerCase().includes(search) ||
				item?.team_2_name?.toLowerCase().includes(search)
			);
		});
	}

	private searchByRosterAthletes(search: string, data: (EventTeamRosterAthlete & RosterExtraT)[]) {
		if (search.length === this.STATE_LENGTH) {
			return data.filter((item) => item.state?.toLowerCase().includes(search));
		}
		return (data || []).filter(
			(item) =>
				item?.first?.toLowerCase().includes(search) ||
				item?.last?.toLowerCase().includes(search) ||
				`${item?.first} ${item?.last}`.toLowerCase().includes(search) ||
				`${item?.last} ${item?.first}`.toLowerCase().includes(search) ||
				item?.club_name?.toLowerCase().includes(search) ||
				item?.team_name?.toLowerCase().includes(search) ||
				item?.club_name?.toLowerCase().includes(search) ||
				item?.organization_code?.toLowerCase().includes(search),
		);
	}

	private searchByRosterStaff(search: string, data: (EventTeamRosterStaff & RosterExtraT)[]) {
		if (search.length === this.STATE_LENGTH) {
			return data.filter((item) => item.state?.toLowerCase().includes(search));
		}
		return (data || []).filter(
			(item) =>
				item?.first?.toLowerCase().includes(search) ||
				item?.last?.toLowerCase().includes(search) ||
				`${item?.first} ${item?.last}`.toLowerCase().includes(search) ||
				`${item?.last} ${item?.first}`.toLowerCase().includes(search) ||
				item?.club_name?.toLowerCase().includes(search) ||
				item?.team_name?.toLowerCase().includes(search) ||
				item?.club_name?.toLowerCase().includes(search) ||
				item?.organization_code?.toLowerCase().includes(search),
		);
	}

	private searchByDivisionPoolsBrackets(search: string, data: DivisionPoolTeam[]) {
		return data?.filter((item) => item.opponent_team_name?.toLowerCase().includes(search));
	}

	private searchByBrackets(search: string, data: IMatchData) {
		return (
			!data.participants[0].name.toLowerCase().includes(search) &&
			!data.participants[1].name.toLowerCase().includes(search)
		);
	}

	private searchByGlobalFavorites(search: string, data: ClubsWithTeamsTeam[]) {
		if (search.length === this.STATE_LENGTH) {
			return data.filter((item) => item.club_state?.toLowerCase().includes(search));
		}
		return data.filter(
			(item) =>
				item.team_name?.toLowerCase().includes(search) ||
				item.club_name?.toLowerCase().includes(search) ||
				item.team_code?.toLowerCase().includes(search),
		);
	}

	private searchByGlobalClubsTeams(search: string, data: ClubsWithTeamsArray[]) {
		if (search.length === this.STATE_LENGTH) {
			return data.filter((item) => item.club_state?.toLowerCase().includes(search));
		}
		return data.filter((item) => {
			return (
				item.club_name?.toLowerCase().includes(search) ||
				item.club_code?.toLowerCase().includes(search) ||
				!!item.teams.find(
					(team) =>
						team.team_name?.toLowerCase().includes(search) ||
						team.team_code?.toLowerCase().includes(search),
				)
			);
		});
	}
	private searchByGlobalDivisions(search: string, data: EventDivision[]) {
		return data.filter((item) => item.name?.toLowerCase().includes(search));
	}

	private searchByGlobalRoster(search: string, data: (EventTeamRosterAthlete & RosterExtraT)[]) {
		if (search.length === this.STATE_LENGTH) {
			return data.filter((item) => item.state?.toLowerCase().includes(search));
		}
		return data.filter(
			(athlete) =>
				athlete?.first?.toLowerCase().includes(search) ||
				athlete?.last?.toLowerCase().includes(search) ||
				athlete?.organization_code?.toLowerCase().includes(search) ||
				athlete?.team_name?.toLowerCase().includes(search) ||
				athlete?.club_name?.toLowerCase().includes(search),
		);
	}

	performSearch(config: SearchT, searchTerm: string) {
		const search = this.prepareSearchData(searchTerm);
		if (search.length < this.STATE_LENGTH) {
			return config.data;
		}
		switch (config.page) {
			case 'favorites':
				return this.searchByFavorites(search, config.data);
			case 'clubs-teams':
				return this.searchByClubsTeams(search, config.data);
			case 'divisions':
				return this.searchByDivisions(search, config.data);
			case 'division-teams':
				return this.searchByDivisionTeams(search, config.data);
			case 'division-standings':
				return this.searchByDivisionStandings(search, config.data);
			// case 'previously-qualified':
			// 	return this.searchByPreviouslyQualified(search, config.data);
			case 'court-grid':
				return this.searchByCourtGrid(search, config.data);
			case 'roster-athlete':
				return this.searchByRosterAthletes(search, config.data);
			case 'roster-staff':
				return this.searchByRosterStaff(search, config.data);
			case 'division-pools-brackets':
				return this.searchByDivisionPoolsBrackets(search, config.data);
			case 'brackets':
				return this.searchByBrackets(search, config.data);
			case 'global-favorites':
				return this.searchByGlobalFavorites(search, config.data);
			case 'global-clubs-teams':
				return this.searchByGlobalClubsTeams(search, config.data);
			case 'global-divisions':
				return this.searchByGlobalDivisions(search, config.data);
			case 'global-roster':
				return this.searchByGlobalRoster(search, config.data);
			default:
				return [];
		}
	}
}
