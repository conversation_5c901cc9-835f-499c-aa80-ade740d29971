import { EventDetails } from '@/shared/hooks/useEventDetails';

import { theme } from '@/styles/theme';

export const addOrdinalSuffix = (num: number) => {
	if (isNaN(num) || num < 1) {
		return '-';
	}

	const remainder10 = num % 10;
	const remainder100 = num % 100;

	let suffix = 'th';

	if (remainder10 === 1 && remainder100 !== 11) {
		suffix = 'st';
	} else if (remainder10 === 2 && remainder100 !== 12) {
		suffix = 'nd';
	} else if (remainder10 === 3 && remainder100 !== 13) {
		suffix = 'rd';
	}

	return `${num}${suffix}`;
};

export const countCommonElements = (arr1: (string | number)[], arr2: (string | number)[]) => {
	const sortedArr1 = arr1.slice().sort();
	const sortedArr2 = arr2.slice().sort();

	let count = 0;
	let index1 = 0;
	let index2 = 0;

	while (index1 < sortedArr1.length && index2 < sortedArr2.length) {
		if (sortedArr1[index1] === sortedArr2[index2]) {
			count++;
			index1++;
			index2++;
		} else if (sortedArr1[index1] < sortedArr2[index2]) {
			index1++;
		} else {
			index2++;
		}
	}

	return count;
};

export const getTicketsDirectLink = (event?: EventDetails): string => {
	if (!event) return '';
	// Check if point-of-sales flow should be used
	if (event.allow_point_of_sales) {
		return event.sales_hub_point_of_sale_id
			? `${import.meta.env.VITE_SWT_URL}/sell/point-of-sales/${event.sales_hub_point_of_sale_id}/purchase-tickets`
			: '';
	}
	// Dealing with the default flow
	if (event.tickets_code) {
		return event.is_require_recipient_name_for_each_ticket
			? `${import.meta.env.VITE_SWT_URL}/buy?event=${event.tickets_code}`
			: `${import.meta.env.VITE_SWT_URL}/#/events/${event.tickets_code}`;
	}
	return '';
};

export const getNumbersArray = (count: number) => Array.from(Array(count).keys());

export const getDeepClone = <T>(data: T): T => JSON.parse(JSON.stringify(data)) as T;

export const isEven = (num: number) => num % 2 === 0;

export const getCurrentBreakpoint = () => {
	const currentWidth = window.innerWidth;
	const { breakpoints } = theme;
	let result = 'large';
	for (const key of Object.keys(breakpoints)) {
		const breakpoint = parseInt(breakpoints[key as keyof typeof breakpoints]);
		if (currentWidth <= breakpoint) {
			result = key;
			break;
		}
	}
	return result;
};

export const openLinkInNewTab = (path: string) => {
	window.open(path, '_blank');
};

export function sanitizeSearchInput(input: string) {
	input = input.trim();
	return input.length < 2 ? '' : input;
}
