import { UTC_TIME_ZONE } from '@/config';
import * as DateFns from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';

import { DivisionPool } from '@/generated/graphql';

const _isTimestamp = (value: number | string): boolean => {
	if (typeof value === 'number') {
		const date = DateFns.fromUnixTime(value);
		return DateFns.isValid(date);
	}

	return false;
};

export const getFormateTime = ({ time, format }: { time: number | string; format: string }) => {
	const value = _isTimestamp(+time) ? +time : DateFns.parseISO(time.toString());
	return DateFns.format(utcToZonedTime(value, UTC_TIME_ZONE), format);
};

export const getUniqueDaysForRound = (round: DivisionPool[]) => {
	const days = new Set();
	round.forEach((pool) => {
		if (pool.date_start) {
			days.add(DateFns.format(utcToZonedTime(pool.date_start * 1000, UTC_TIME_ZONE), 'EEE'));
		}
	});
	return [...days].join(' & ');
};

export const isDateWithinDaysRange = ({
	providedDateString,
	range,
}: {
	providedDateString: string;
	range: number;
}) => {
	let providedDate;
	if (Number(providedDateString)) {
		providedDate = new Date(parseInt(providedDateString, 10));
	} else {
		providedDate = DateFns.parseISO(providedDateString);
	}
	const currentDate = new Date();
	const sevenDaysLater = DateFns.addDays(currentDate, range);
	const dateRange = {
		start: currentDate,
		end: sevenDaysLater,
	};
	return (
		DateFns.isWithinInterval(providedDate, dateRange) &&
		!DateFns.isAfter(providedDate, sevenDaysLater)
	);
};

export const isTimestampInPast = (providedDateString: string) => {
	let providedDate;
	if (Number(providedDateString)) {
		providedDate = new Date(parseInt(providedDateString, 10));
	} else {
		providedDate = DateFns.parseISO(providedDateString);
	}
	const currentTimestamp = new Date().getTime();

	return DateFns.isBefore(providedDate, currentTimestamp);
};

export const isStartTimeAfterOrEqualEndTime = (start: string, end: string) => {
	if (!start || !end) {
		return false;
	}
	const time1 = DateFns.parse(start.toLowerCase(), 'h a', new Date());
	const time2 = DateFns.parse(end.toLowerCase(), 'h a', new Date());
	return DateFns.isAfter(time2, time1) || DateFns.isEqual(time1, time2);
};

export const getDifferenceInHours = (start: string, end: string) => {
	if (!start || !end) {
		return 0;
	}
	const time1 = DateFns.parse(start.toLowerCase(), 'h a', new Date());
	const time2 = DateFns.parse(end.toLowerCase(), 'h a', new Date());
	return DateFns.differenceInHours(time2, time1) + 1;
};

export const formatDays = (day: string) => {
	const inputDate = DateFns.parseISO(day);

	return DateFns.format(inputDate, 'eee MM/dd');
};

export const getGridDayToStorage = (eventId: string) => {
	const gridDate = localStorage.getItem('gridDate');
	if (!gridDate) {
		return null;
	}
	try {
		return JSON.parse(gridDate)[eventId];
	} catch (error) {
		return null;
	}
};
export const setGridDayToStorage = (eventId: string, day: string) => {
	const gridDate = localStorage.getItem('gridDate');

	if (!gridDate) {
		localStorage.setItem('gridDate', JSON.stringify({ [eventId]: day }));
		return;
	}
	try {
		const gridDateObject = JSON.parse(gridDate);
		gridDateObject[eventId] = day;
		localStorage.setItem('gridDate', JSON.stringify(gridDateObject));
	} catch (error) {
		localStorage.setItem('gridDate', JSON.stringify({ [eventId]: day }));
	}
};

export const getDaysBetweenDates = ({
	startDate,
	endDate,
	format = 'short',
}: {
	startDate: Date | null;
	endDate: Date | null;
	format: 'short' | 'full';
}): string[] => {
	const formatTemplate = format === 'short' ? 'EEE' : 'EEEE';
	const isStartValid = startDate && DateFns.isValid(startDate);
	const isEndValid = endDate && DateFns.isValid(endDate);

	if (isStartValid && isEndValid) {
		// Both dates are valid, generate range
		const daysInRange = DateFns.eachDayOfInterval({ start: startDate, end: endDate });
		return daysInRange.map((date) => DateFns.format(date, formatTemplate));
	} else if (isStartValid) {
		// Only the start date is valid
		return [DateFns.format(startDate, formatTemplate)];
	} else if (isEndValid) {
		// Only the end date is valid
		return [DateFns.format(endDate, formatTemplate)];
	} else {
		return [];
	}
};

// Parses a day string in the format "yyyy-MM-dd" to a Date object set to 00:00:00 UTC
export const parseUTCDay = (day: string): Date => {
	// This ensures the day's Date is set specifically to 00:00:00 UTC
	return new Date(`${day}T00:00:00Z`);
};

export const currentUTCDay = (): Date => {
	const now = new Date();
	return new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate()));
};

export const formatUTCTime = (date: Date, toFormat = 'HH:mm'): string => {
	return DateFns.format(utcToZonedTime(date, 'UTC'), toFormat);
};

export const convertTimeFormat = (time: string, format: string, toFormat: string): string => {
	const date = DateFns.parse(time, format, new Date());
	return DateFns.format(date, toFormat);
};

/**
 * Shifts the time by the given number of minutes
 * @param time - the time to shift
 * @param offset - the number of minutes to shift the time by
 * @param preventOverflow - if true, the time will not go past the start or end of the day
 * @param format - the format of the input time, e.g. "HH:mm"
 */
export function shiftTime<T extends string | Date>(
	time: T,
	offset: number,
	preventOverflow = true,
	format: string = 'HH:mm',
): T extends string ? string : Date {
	const baseDate = new Date();
	let date: Date;

	if (typeof time === 'string') {
		date = DateFns.parse(time, format, baseDate);
	} else {
		date = new Date(time);
	}

	date = DateFns.addMinutes(date, offset);

	if (preventOverflow) {
		const minDate = DateFns.startOfDay(baseDate);
		const maxDate = DateFns.endOfDay(baseDate);

		if (date < minDate) {
			date = minDate;
		} else if (date > maxDate) {
			date = maxDate;
		}
	}

	return (typeof time === 'string' ? DateFns.format(date, format) : date) as T extends string
		? string
		: Date;
}

// Generates an array of times between the given start and end times
export const getIntervalsInRange = <T extends string | Date>(
	rangeStart: T,
	rangeEnd: T,
	step: number,
	format = 'HH:mm',
): T extends string ? string[] : Date[] => {
	let start: Date;
	let end: Date;

	if (typeof rangeStart === 'string') {
		// Enforce that both args are strings
		const baseDate = new Date();
		start = DateFns.parse(rangeStart, format, baseDate);
		end = DateFns.parse(rangeEnd as string, format, baseDate);
	} else {
		// Enforce that both args are Dates
		start = new Date(rangeStart);
		end = new Date(rangeEnd);
	}

	const intervals = DateFns.eachMinuteOfInterval({ start, end }, { step });

	return (
		typeof rangeStart === 'string'
			? intervals.map((date) => DateFns.format(date, format))
			: intervals
	) as T extends string ? string[] : Date[];
};

export const alignToStep = <T extends string | Date>(
	dateOrTime: T,
	stepMinutes: number,
	toFloor: boolean = true,
	timeFormat = 'HH:mm',
): T extends string ? string : Date => {
	const date =
		typeof dateOrTime === 'string'
			? DateFns.parse(dateOrTime, timeFormat, new Date())
			: new Date(dateOrTime);

	const totalMinutes = date.getHours() * 60 + date.getMinutes();
	const remainder = totalMinutes % stepMinutes;

	if (remainder !== 0) {
		if (toFloor) {
			// round down
			const floored = totalMinutes - remainder;
			date.setHours(Math.floor(floored / 60), floored % 60, 0, 0);
		} else {
			// round up
			const needed = stepMinutes - remainder;
			const ceiled = totalMinutes + needed;
			date.setHours(Math.floor(ceiled / 60), ceiled % 60, 0, 0);
		}
	}

	return (
		typeof dateOrTime === 'string' ? DateFns.format(date, timeFormat) : date
	) as T extends string ? string : Date;
};

export const getMinTime = (...times: (number | null | undefined)[]): number | null => {
	const validTimes = times.filter((t) => t != null && !isNaN(t)) as number[];
	return validTimes.length > 0 ? Math.min(...validTimes) : null;
};

export const getMaxTime = (...times: (number | null | undefined)[]): number | null => {
	const validTimes = times.filter((t) => t != null && !isNaN(t)) as number[];
	return validTimes.length > 0 ? Math.max(...validTimes) : null;
};
