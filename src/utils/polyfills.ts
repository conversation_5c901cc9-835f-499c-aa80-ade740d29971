if (!Array.prototype.at) {
	Object.defineProperty(Array.prototype, 'at', {
		value: function (n: number) {
			// Convert n to integer
			n = Math.trunc(n) || 0;
			// Allow negative indexing from the end
			if (n < 0) n += this.length;
			// Return undefined if out of bounds
			if (n < 0 || n >= this.length) return undefined;
			// Return the element
			return this[n];
		},
		writable: true,
		enumerable: false,
		configurable: true,
	});
}

if (!Array.prototype.toSorted) {
	Object.defineProperty(Array.prototype, 'toSorted', {
		value: function <T>(compareFn?: ((_a: T, _b: T) => number) | undefined): T[] {
			// Create a new array with the same elements
			return [...this].sort(compareFn);
		},
		writable: true,
		enumerable: false,
		configurable: true,
	});
}
