type EventStorage = {
	date: string | number;
	startTime: string;
	endTime: string;
	divisionId: string | number;
};
export type GridFilterStorage = {
	[eventId: string]: EventStorage;
}[];

const EMPTY_STATE = { date: '', startTime: '', endTime: '', divisionId: '' };

export const getGridFilterStorageByEventId = (eventId: string) => {
	const gridFilter = localStorage.getItem('gridFilter');
	if (!gridFilter) {
		return null;
	}
	try {
		return JSON.parse(gridFilter)[eventId];
	} catch (error) {
		return null;
	}
};
export const initGridFilterStorage = (eventId: string) => {
	const gridFilter = localStorage.getItem('gridFilter');
	if (!gridFilter) {
		localStorage.setItem('gridFilter', JSON.stringify({ [eventId]: EMPTY_STATE }));
		return;
	}
	try {
		const gridFilterObject = JSON.parse(gridFilter);
		if (!gridFilterObject[eventId]) {
			gridFilterObject[eventId] = EMPTY_STATE;
			localStorage.setItem('gridFilter', JSON.stringify(gridFilterObject));
		}
	} catch (error) {
		localStorage.setItem('gridFilter', JSON.stringify({ [eventId]: EMPTY_STATE }));
	}
};

export const setGridFilterStorage = ({
	eventId,
	date = '',
	startTime = '',
	endTime = '',
	divisionId = '',
}: {
	eventId: string;
	date?: string | number;
	startTime?: string;
	endTime?: string;
	divisionId?: string | number;
}) => {
	const gridFilter = localStorage.getItem('gridFilter');
	if (!gridFilter) {
		initGridFilterStorage(eventId);
		return;
	}
	try {
		const gridFilterObject = JSON.parse(gridFilter);
		gridFilterObject[eventId] = {
			date: date || gridFilterObject[eventId].date,
			startTime: startTime || gridFilterObject[eventId].startTime,
			endTime: endTime || gridFilterObject[eventId].endTime,
			divisionId: divisionId || gridFilterObject[eventId].divisionId,
		};
		localStorage.setItem('gridFilter', JSON.stringify(gridFilterObject));
	} catch (error) {
		localStorage.setItem(
			'gridFilter',
			JSON.stringify({ [eventId]: { date, startTime, endTime, divisionId } }),
		);
	}
};
