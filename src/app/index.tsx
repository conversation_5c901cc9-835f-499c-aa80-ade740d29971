import { Navigate, Route, Routes } from 'react-router-dom';

import { EventLayout } from '@/layouts/EventLayout.tsx';
import { MainLayout } from '@/layouts/MainLayout';

import About from '@/pages/About';
import BracketTemplates from '@/pages/BracketTemplates';
import BracketTemplate from '@/pages/BracketTemplates/[id]';
import ClubsAndTeams from '@/pages/ClubsAndTeams';
import CourtGrid from '@/pages/CourtGrid';
import Division from '@/pages/Division';
import Pools from '@/pages/Division/components/Pools';
import { POOL_TABS } from '@/pages/Division/constants';
import DivisionNext from '@/pages/DivisionNext';
import Divisions from '@/pages/Divisions';
import { Events } from '@/pages/Events';
import Favorites from '@/pages/Favorites';
import Pool from '@/pages/Pool';
import Qualified from '@/pages/Qualified';
import Roster from '@/pages/Roster';
import SearchResult from '@/pages/SearchResult';

import Providers from './Providers';
import { Redirect } from './Redirect';
import { EventScheduleGuard } from './components/EventScheduleGuard';

export const App = () => {
	return (
		<Providers>
			<Routes>
				<Route path="/" element={<Navigate to="/events" replace />} />

				<Route path="/templates" element={<BracketTemplates />} />
				<Route path="/templates/:id" element={<BracketTemplate />} />

				<Route path="/events" element={<MainLayout />}>
					<Route index element={<Events />} />
					<Route path=":id" element={<Navigate to="about" replace />} />
					<Route path=":id/*" element={<EventLayout />}>
						<Route path="about" element={<About />} />
						<Route element={<EventScheduleGuard />}>
							<Route path="roster" element={<Roster />} />
							<Route path="favorites" element={<Favorites />} />
							<Route path="court-grid" element={<CourtGrid />} />
							<Route path="qualified" element={<Qualified />} />
							<Route path="divisions" element={<Divisions />} />
							<Route path="clubs-teams" element={<ClubsAndTeams />} />
							<Route
								path="divisions/:divisionId/teams"
								element={<Division activeTab={POOL_TABS.TEAMS} />}
							/>
							<Route
								path="divisions/:divisionId/pools"
								element={<Division activeTab={POOL_TABS.POOLS} />}
							/>
							<Route path="divisions2/:divisionId/pools" element={<DivisionNext />} />
							<Route
								path="divisions/:divisionId/standings"
								element={<Division activeTab={POOL_TABS.STANDINGS} />}
							/>
							<Route
								path="divisions/:divisionId/flowChart"
								element={<Division activeTab={POOL_TABS.FLOW_CHART} />}
							/>
							<Route path="divisions/:divisionId/pools" element={<Pools />} />
							<Route path="divisions/:divisionId/pools/:poolId" element={<Pool />} />
							<Route
								path="search-result"
								element={<Redirect to="/events/:id/search-result/favorites" />}
							/>
							<Route
								path="search-result/favorites"
								element={<SearchResult activeTab="favorites" />}
							/>
							<Route path="search-result/best" element={<SearchResult activeTab="best" />} />
							<Route path="search-result/clubs" element={<SearchResult activeTab="clubs" />} />
							<Route path="search-result/teams" element={<SearchResult activeTab="teams" />} />
							<Route path="search-result/roster" element={<SearchResult activeTab="roster" />} />
							<Route
								path="search-result/divisions"
								element={<SearchResult activeTab="divisions" />}
							/>
						</Route>
					</Route>
				</Route>
			</Routes>
		</Providers>
	);
};
