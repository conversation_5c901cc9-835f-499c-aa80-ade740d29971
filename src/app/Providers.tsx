import { Helmet } from 'react-helmet';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import { QueryParamProvider } from 'use-query-params';
import { ReactRouter6Adapter } from 'use-query-params/adapters/react-router-6';

import { GlobalStyle } from '@/styles/globalStyles';
import { theme } from '@/styles/theme';

const Providers = ({ children }: { children: React.ReactNode }) => (
	<BrowserRouter>
		<QueryParamProvider adapter={ReactRouter6Adapter}>
			<ThemeProvider theme={theme}>
				<GlobalStyle theme={theme} />
				<Helmet>
					<link
						href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
						rel="stylesheet"
					/>
				</Helmet>
				{children}
			</ThemeProvider>
		</QueryParamProvider>
	</BrowserRouter>
);

export default Providers;
