import styled from 'styled-components';

export const MultiSelect__Wrapper = styled.div`
	position: relative;
`;

export const MultiSelect__Select = styled.div<{ $isBlue?: boolean }>`
	max-width: calc(100vw - 32px);
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		max-width: calc(680px - 48px);
	}
	p {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		background: ${({ $isBlue }) => ($isBlue ? 'transparent' : '#f4f6f8')};
		border-radius: ${({ $isBlue }) => ($isBlue ? '4px' : '2px')};
		border: 1px solid ${({ $isBlue, theme }) => ($isBlue ? `${theme.colors.blue}` : '#f4f6f8')};
		color: ${({ $isBlue, theme }) => ($isBlue ? `${theme.colors.blue}` : '#454f5b')};
		font-size: ${({ $isBlue }) => ($isBlue ? '12px' : '14px')};
		font-weight: ${({ $isBlue }) => ($isBlue ? '400' : '600')};
		line-height: 22px;
		position: relative;
		padding: 0 25px 0 12px;
		min-width: 124px;
		height: 40px;
		font-size: 14px;
		line-height: 37px;
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			padding: 0 35px 0 12px;
			min-width: 136px;
		}
	}
	cursor: pointer;
	img {
		position: absolute;
		right: 7px;
		top: 50%;
		transform: translateY(-50%);
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			width: 24px;
			height: 24px;
		}
	}
`;
export const MultiSelect__DropDownWrapper = styled.div<{ $type?: string }>`
	position: ${({ $type }) => ($type === 'courtGrid' ? 'absolute' : 'fixed')};
	width: 344px;
	padding: 20px 0 0;
	background: #fff;
	z-index: 6;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		left: auto;
		top: auto;
		transform: none;
		width: 254px;
	}
`;
export const MultiSelect__DropDownHeader = styled.header`
	border-bottom: 1px solid #dfe3e8;
	padding-bottom: 14px;
	font-size: 14px;
	line-height: 22px;
	text-align: center;
`;
export const MultiSelect__DropDownList = styled.ul`
	max-height: 310px;
	overflow: hidden;
	overflow-y: scroll;
`;
export const MultiSelect__DropDownItem = styled.li`
	border-bottom: 1px solid #dfe3e8;

	padding: 19px 24px;
`;
export const MultiSelect__DropDownItemWrapper = styled.label`
	font-size: 14px;
	line-height: 22px;
	display: flex;
	align-items: center;
	justify-content: space-between;
`;
export const MultiSelect__DropDownItemCheckBox = styled.input`
	width: 20px;
	height: 20px;
`;
export const MultiSelect__DropDownFooter = styled.footer`
	display: flex;
	justify-content: space-between;
	padding: 24px 13px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 16px 13px;
	}
`;
export const Overlay = styled.div<{ $isDesktop?: boolean }>`
	background: ${(props) =>
		props.$isDesktop ? 'rgba(217, 217, 217, 0.7)' : 'rgba(223, 227, 232, 0.4)'};
	width: 100%;
	height: 100%;
	position: fixed;
	left: 0;
	z-index: 5;
	top: 0px;
`;
export const MultiSelect__ButtonWrapper = styled.div`
	width: 150px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		width: 106px;
	}
`;

export const MultiSelect__Button = styled.button<{ $variant?: 'contained' | 'outlined' }>`
	background: ${(props) => (props.$variant === 'contained' ? props.theme.colors.blue : '#fff')};
	color: ${(props) => (props.$variant === 'contained' ? '#fff' : '#637381')};
	border: 0.5px solid ${(props) => (props.$variant === 'contained' ? 'transparent' : '#637381')};
	width: 100%;
	border-radius: 4px;
	height: 44px;
	padding: 6px 16px;
	text-align: center;
	line-height: 22px;
	font-size: 14px;
	cursor: pointer;
`;
