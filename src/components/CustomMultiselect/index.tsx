import arrowBottom from '@assets/arrowBottomBlack.svg';
import arrowBottomBlue from '@assets/arrowBottomBlue-icon.svg';
import { useEffect, useState } from 'react';

import { filterService } from '@/services/filters.service';

import {
	MultiSelect__Button,
	MultiSelect__ButtonWrapper,
	MultiSelect__DropDownFooter,
	MultiSelect__DropDownHeader,
	MultiSelect__DropDownItem,
	MultiSelect__DropDownItemCheckBox,
	MultiSelect__DropDownItemWrapper,
	MultiSelect__DropDownList,
	MultiSelect__DropDownWrapper,
	MultiSelect__Select,
	MultiSelect__Wrapper,
	Overlay,
} from './styled';

type PropsT = {
	selectName: string;
	dropDownName: string;
	data: Record<string, string>[];
	setSelectedData: (_options: string[]) => void;
	isBlue?: boolean;
	type?: string;
	initialSelectedData?: string[];
};
// TODO write story and rename component
export const CustomMultiSelect = ({
	selectName,
	dropDownName,
	data,
	setSelectedData,
	isBlue,
	type,
	initialSelectedData,
}: PropsT) => {
	const [isOpen, setIsOpen] = useState(false);

	// TODO Refactor this. The selected options should come from the parent component
	const initialOptions = initialSelectedData
		? initialSelectedData.reduce<Record<string, string>>((acc, item) => {
				acc[item] = item;
				return acc;
			}, {})
		: filterService.getFilter(type || '') || {};

	const [selected, setSelected] = useState<Record<string, string>>(initialOptions);

	useEffect(() => {
		setSelectedData(Object.values(initialOptions));
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		if (e.target.checked) {
			setSelected({ ...selected, [e.target.value]: e.target.value });
		} else {
			setSelected((prev) => {
				const copy = { ...prev };
				delete copy[e.target.value];
				return copy;
			});
		}
	};

	const clear = () => {
		setSelected({});
	};

	const apply = () => {
		setIsOpen(false);
		setSelectedData(Object.values(selected));
		if (type === 'pastEventsYear') {
			filterService.setFilter('pastEventsYear', selected);
		}
	};

	useEffect(() => {
		if (isOpen) {
			document.body.classList.add('block-scroll');
		} else {
			document.body.classList.remove('block-scroll');
		}
		return () => {
			document.body.classList.remove('block-scroll');
		};
	}, [isOpen]);

	return (
		<MultiSelect__Wrapper>
			<MultiSelect__Select onClick={() => setIsOpen(true)} $isBlue={isBlue}>
				<p>{selectName}</p>
				<img src={isBlue ? arrowBottomBlue : arrowBottom} alt="court" />
			</MultiSelect__Select>
			{isOpen && <Overlay onClick={() => setIsOpen(false)} />}

			{isOpen && (
				<MultiSelect__DropDownWrapper $type={type}>
					<MultiSelect__DropDownHeader>{dropDownName}</MultiSelect__DropDownHeader>
					<MultiSelect__DropDownList>
						{[...data].map(({ key, value }) => (
							<MultiSelect__DropDownItem key={value}>
								<MultiSelect__DropDownItemWrapper>
									{key}
									<MultiSelect__DropDownItemCheckBox
										value={value}
										checked={!!selected[value]}
										type="checkbox"
										name=""
										id=""
										onChange={onChange}
									/>
								</MultiSelect__DropDownItemWrapper>{' '}
							</MultiSelect__DropDownItem>
						))}
					</MultiSelect__DropDownList>
					<MultiSelect__DropDownFooter>
						<MultiSelect__ButtonWrapper>
							<MultiSelect__Button onClick={clear}>Clear all</MultiSelect__Button>
						</MultiSelect__ButtonWrapper>
						<MultiSelect__ButtonWrapper>
							<MultiSelect__Button $variant="contained" onClick={apply}>
								Apply
							</MultiSelect__Button>
						</MultiSelect__ButtonWrapper>
					</MultiSelect__DropDownFooter>
				</MultiSelect__DropDownWrapper>
			)}
		</MultiSelect__Wrapper>
	);
};
