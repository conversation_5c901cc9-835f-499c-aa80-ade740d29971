import { ASIDE_MENU_POSITION } from '@/config';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { getTicketsDirectLink } from '@/utils';
import ballerLogoIcon from '@assets/baller-logo.svg';
import navAboutEventIcon from '@assets/navAboutEventIcon.svg';
import navCalendarIcon from '@assets/navCalendarIcon.svg';
import navCameraIcon from '@assets/navCameraIcon.svg';
import navClubsTeamsIcon from '@assets/navClubsTeamsIcon.svg';
import navCourtGridIcon from '@assets/navCourtGridIcon.svg';
import navDivisionsIcon from '@assets/navDivisionsIcon.svg';
import navFavoritesIcon from '@assets/navFavoritesIcon.svg';
import navPreviouslyQualifiedIcon from '@assets/navPreviouslyQualifiedIcon.svg';
import navRosterIcon from '@assets/navRosterIcon.svg';
import * as React from 'react';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

import asideCloseIcon from '@/assets/aside-close-icon.svg';
import asideIcon from '@/assets/aside-icon.svg';
import ticketIcon from '@/assets/navTicketIcon.svg';

import Drawer from '@mui/material/Drawer';

import { useEventDetailsQuery } from '@/generated/graphql';

import { OldEventsButton } from '../OldEventsButton';
import { EVENT_MENU_LIST } from './menuList';
import { Aside__IconButton, Aside__NavItem, Aside__NavList, Aside__Wrapper } from './styled';

interface MenuItem {
	key: string;
	title: string;
	path: string;
	icon?: string;
	externalLink?: string;
	isExternal?: boolean;
	isVisible?: boolean;
}

export const AsideMenu = () => {
	const [isOpen, setIsOpen] = React.useState(false);

	const eswId = useParams().id!;
	const navigate = useNavigate();
	const location = useLocation();
	const { data } = useEventDetailsQuery({
		skip: !eswId,
		variables: {
			eswId,
		},
	});
	const isHasRosters = !!data?.event?.has_rosters;
	const isPreviouslyQualified = !!data?.event?.is_with_prev_qual;
	const isSchedulePublished = !!data?.event?.schedule_published;
	const isShowBuyAdmission = !!data?.event?.tickets_published;
	const { breakPont } = useCurrentSize();
	const toggleDrawer = (open: boolean) => (event: React.KeyboardEvent | React.MouseEvent) => {
		if (
			event.type === 'keydown' &&
			((event as React.KeyboardEvent).key === 'Tab' ||
				(event as React.KeyboardEvent).key === 'Shift')
		) {
			return;
		}
		setIsOpen(open);
	};

	const iconMap: Record<string, string> = {
		favorites: navFavoritesIcon,
		'clubs-teams': navClubsTeamsIcon,
		roster: navRosterIcon,
		divisions: navDivisionsIcon,
		'court-grid': navCourtGridIcon,
		'previously-qualified': navPreviouslyQualifiedIcon,
		'about-event': navAboutEventIcon,
		'all-events': navCalendarIcon,
		'online-streaming': navCameraIcon,
		'buy-admission': ticketIcon,
	};

	const getMenuList = (): MenuItem[] => {
		return EVENT_MENU_LIST.map((item) => {
			const baseItem = {
				key: item.key,
				title: item.title,
				path: item.path,
				icon: iconMap[item.key],
			};
			if (item.key === 'sw-home') return baseItem;
			if (location.pathname === '/events' || location.pathname === '/events/') {
				return { ...baseItem, isVisible: false };
			}
			if (!isSchedulePublished && item.key !== 'about-event') {
				return { ...baseItem, isVisible: false };
			}
			if (item.key === 'roster') {
				return { ...baseItem, isVisible: isHasRosters };
			}
			if (item.key === 'previously-qualified') {
				return { ...baseItem, isVisible: isPreviouslyQualified };
			}
			if (item.key === 'buy-admission') {
				const ticketLink = getTicketsDirectLink(data?.event);
				return {
					...baseItem,
					isVisible: isShowBuyAdmission && !!ticketLink,
					externalLink: ticketLink,
					isExternal: true,
				};
			}
			if (item.key === 'online-streaming') {
				return {
					...baseItem,
					isVisible: !!data?.event?.teams_settings?.baller_tv_available,
					externalLink: `${import.meta.env.VITE_BALLERTV_URL}/events?sport_wrench_event_id=${eswId}`,
					isExternal: true,
				};
			}
			return baseItem;
		}).filter((item: MenuItem) => item.isVisible !== false);
	};

	const handleNavigation = (item: MenuItem) => {
		if (item.key === 'all-events') {
			navigate(`/${item.path}`);
		} else {
			navigate(`/events/${eswId}/${item.path}`);
		}
	};

	return (
		<div>
			<Aside__IconButton onClick={toggleDrawer(true)} data-testid="aside-button">
				{!isOpen && <img src={asideIcon} alt="aside" loading="lazy" />}
				{isOpen && <img src={asideCloseIcon} alt="aside-close" loading="lazy" />}
			</Aside__IconButton>
			<Drawer anchor={ASIDE_MENU_POSITION} open={isOpen} onClose={toggleDrawer(false)}>
				<Aside__Wrapper
					onClick={toggleDrawer(false)}
					onKeyDown={toggleDrawer(false)}
					data-testid="drawer"
				>
					<Aside__NavList>
						{getMenuList().map((item) => (
							<Aside__NavItem
								key={item.title}
								data-testid="navItem"
								onClick={item.isExternal ? undefined : () => handleNavigation(item)}
							>
								{item.isExternal ? (
									<a href={item.externalLink} target="_blank" rel="noopener noreferrer">
										{item.icon && <img src={item.icon} alt={`${item.title} icon`} />}
										{item.title}
										{item.key === 'online-streaming' && <img src={ballerLogoIcon} alt="baller" />}
									</a>
								) : (
									<>
										{item.icon && <img src={item.icon} alt={`${item.title} icon`} />}
										{item.title}
									</>
								)}
							</Aside__NavItem>
						))}
						{breakPont === 'small' && (
							<Aside__NavItem>
								<OldEventsButton />
							</Aside__NavItem>
						)}
					</Aside__NavList>
				</Aside__Wrapper>
			</Drawer>
		</div>
	);
};
