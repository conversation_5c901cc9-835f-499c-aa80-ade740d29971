import arrowBottomBlue from '@assets/arrowBottomBlue-icon.svg';
import arrowClose from '@assets/arrowCloseWhite.svg';
import { useLayoutEffect, useState } from 'react';

import { CustomSelectWrapper, CustomSelect__Select } from './styled';

type PropsT = {
	width?: number;
	value?: string | number;
	options: Record<string | number, string | number>[];
	isBlue?: boolean;
	onChange: (_value: string | number) => void;
};

export const Select = ({ width, value, options, onChange, isBlue }: PropsT) => {
	const [currentValue, setCurrentValue] = useState('');

	useLayoutEffect(() => {
		setCurrentValue(value?.toString() || '');
	}, [value]);

	return (
		<CustomSelectWrapper className="custom_select" $width={width} $isBlue={isBlue}>
			<span>
				{options.find((o) => o.value.toString() === currentValue.toString())?.key ||
					options.find((o) => o.value.toString() === value?.toString())?.key ||
					options[0]?.key}
			</span>
			<CustomSelect__Select
				value={value}
				onChange={(event) => {
					onChange(event.target.value);
					setCurrentValue(event.target.value);
				}}
			>
				{options.map((o) => (
					<option key={o.key} value={o.value}>
						{o.key}
					</option>
				))}
			</CustomSelect__Select>
			<img src={isBlue ? arrowBottomBlue : arrowClose} alt="close" />
		</CustomSelectWrapper>
	);
};
