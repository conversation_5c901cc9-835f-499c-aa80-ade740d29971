import styled from 'styled-components';

export const StyledPreviouslyQualifiedInfoWrapper = styled.section`
	border-radius: 6px;
	background: #fb5;
	width: 20px;
	height: 20px;
	min-width: 20px;
	min-height: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	img {
		width: 10px;
		height: 10px;
	}
`;
export const StyledPreviouslyQualifiedInfoWrapperOpen = styled.section`
	padding: 5px 10px;
	width: max-content;
	border-radius: 6px;
	background: #ffa31a;
	cursor: pointer;
	p {
		color: #fff;
		font-size: 10px;
		font-weight: 700;
		line-height: 10px;
	}
`;

// * Earned Bid Info
export const StyledBidEarnedInfoWrapper = styled.section`
	border-radius: 6px;
	background: #26c920;
	width: 20px;
	height: 20px;
	min-width: 20px;
	min-height: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	img {
		width: 10px;
		height: 10px;
	}
`;
export const StyledBidEarnedInfoWrapperOpen = styled.section`
	padding: 5px 10px;
	width: max-content;
	border-radius: 6px;
	background: #26c920;
	cursor: pointer;
	display: flex;
	align-items: center;
	gap: 3px;
	p {
		color: #fff;
		font-size: 10px;
		font-weight: 700;
		line-height: 10px;
	}
`;
