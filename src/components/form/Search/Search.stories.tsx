import Providers from '@/app/Providers';
import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { ChangeEvent } from 'react';

import { Box } from '@mui/material';

import { Search } from '.';

const meta = {
	title: 'Form/Search',
	component: Search,
	parameters: {
		layout: 'padded',
		docs: {
			description: {
				component: 'Search component',
			},
		},
	},
	argTypes: {
		onChange: {
			control: null,
		},
	},
	tags: ['autodocs'],
	decorators: [
		(Story) => {
			const [value, setValue] = useState('');
			return (
				<Providers>
					<Box sx={{ background: '#3366ff', padding: '50px 16px' }}>
						<Story
							onChange={(e: ChangeEvent<HTMLInputElement>) => setValue(e.target.value)}
							value={value}
						/>
					</Box>
				</Providers>
			);
		},
	],
} satisfies Meta<typeof Search>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Outlined: Story = {
	args: {
		placeholder: 'Club, team, athlete, division',
		variant: 'outlined',
	},
};
export const Contained: Story = {
	args: {
		placeholder: 'Search',
		variant: 'contained',
	},
};
