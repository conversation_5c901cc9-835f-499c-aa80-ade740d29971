import { ChangeEvent } from 'react';

import closeIcon from '@/assets/close-icon.svg';
import searchContainedIcon from '@/assets/search-icon.svg';
import searchOutlinedIcon from '@/assets/searchAbout-icon.svg';

import { Search__Field, Search__Icon, Search__Wrapper } from './styled';

export type PropsT = {
	placeholder?: string;
	onChange?: (_e: ChangeEvent<HTMLInputElement>) => void;
	value?: string;
	variant?: 'outlined' | 'contained';
};
export const Search = ({ placeholder, onChange, value, variant = 'outlined' }: PropsT) => {
	return (
		<Search__Wrapper>
			{value && (
				<Search__Icon
					$isClose
					src={closeIcon}
					alt="search"
					onClick={() => onChange?.({ target: { value: '' } } as ChangeEvent<HTMLInputElement>)}
				/>
			)}
			{variant === 'outlined' && !value && <Search__Icon src={searchContainedIcon} alt="search" />}
			{variant === 'contained' && !value && <Search__Icon src={searchOutlinedIcon} alt="search" />}
			<Search__Field
				$variant={variant}
				type="text"
				placeholder={placeholder}
				onChange={onChange}
				value={value}
			/>
		</Search__Wrapper>
	);
};
