import { Link } from 'react-router-dom';
import styled, { css } from 'styled-components';

const wrapper = css`
	background: #fff;
	box-shadow: 0px 16px 32px -4px rgba(145, 158, 171, 0.16);
	padding: 16px;
	display: flex;
	justify-content: space-between;
`;

export const ShadowItem__WrapperLink = styled(Link)`
	${wrapper}
	text-decoration: none;
`;

export const ShadowItem__Wrapper = styled.div`
	${wrapper}
`;
export const ShadowItem__Title = styled.span`
	font-size: 12px;
	line-height: 18px;
`;
export const ShadowItem__SubTitle = styled.span`
	color: #919eab;
	font-size: 12px;
	line-height: 18px;
`;
