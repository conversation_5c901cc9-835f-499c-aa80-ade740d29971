import {
	LazyScrollTriggerContext,
	LazyScrollTriggerContextValue,
} from '@/shared/contexts/lazyScrollTrigger.context';

type Props = LazyScrollTriggerContextValue & {
	children: React.ReactNode | React.ReactNode[];
};

export const LazyScrollTriggerControl = ({ children, ...value }: Props) => {
	return (
		<LazyScrollTriggerContext.Provider value={value}>{children}</LazyScrollTriggerContext.Provider>
	);
};
