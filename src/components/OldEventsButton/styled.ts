import styled from 'styled-components';

export const StyledOldEventsButtonWrapper = styled.button<{ $isMobile: boolean }>`
	font-size: 14px;
	border-radius: 6px;
	background: #fff;
	border: none;
	cursor: pointer;
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 0;
	color: #454f5b;
	${({ $isMobile }) =>
		!$isMobile &&
		`
      font-size: 16px;
	    line-height: 18px;
      min-height: 38px;
      padding: 10px 15px;
    	box-shadow: 0px 1px 2px 0px rgba(0, 0, 0, 0.16);
      position: fixed;
      bottom: 66px;
      right: 30px;
      z-index: 99;
  `}
`;
