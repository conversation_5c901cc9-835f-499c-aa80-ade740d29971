import { useBlockScroll } from '@/shared/hooks/useBlockScroll';
import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useFavoriteTeams } from '@/shared/hooks/useFavoriteTeams';
import { useUpcomingMappedMatches } from '@/shared/hooks/useUpcommingMappedMatches';
import { modalTeamIdSignal } from '@/signals/modalTeamIdSignal';
import { uniqBy } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { StringParam, useQueryParams } from 'use-query-params';

import {
	DivisionPool,
	EventPool,
	PoolsQuery,
	TeamSingle,
	TeamSingleQuery,
	usePoolLazyQuery,
	usePoolsLazyQuery,
} from '@/generated/graphql';

import { PoolAnchorHeader } from '../headers/PoolAnchorHeader';
import { TeamAnchorHeader } from '../headers/TeamAnchorHeader';
import refreshIcon from '../img/refresh.icon.svg';
import { StyledAnchorModalRefreshButton } from '../styled';
import { PoolFutureTab } from '../tabs/pool/PoolFutureTab';
import { PoolResultsTab } from '../tabs/pool/PoolResultsTab';
import { PoolScheduleTab } from '../tabs/pool/PoolScheduleTab';
import { PoolStandingsTab } from '../tabs/pool/PoolStandingsTab';
// import { TeamFeatureTab } from '../tabs/team/TeamFeatureTab';
import { TeamResultTab } from '../tabs/team/TeamResultTab';
import { TeamRosterTab } from '../tabs/team/TeamRosterTab';
import { TeamScheduleTab } from '../tabs/team/TeamScheduleTab';
import { ModalType, PoolBracketUUID } from '../types';

const OFFSET_ANCHOR = 85;

type PropsT = {
	closeTeamModal: () => void;
	teamPoolData: TeamSingleQuery;
	poolOfTeam?: DivisionPool | null;
	isPool?: boolean;
	teamRefetch?: () => void;
	matchesRefetch?: () => void;
};

export const useAnchorModal = ({
	closeTeamModal,
	teamPoolData,
	poolOfTeam,
	teamRefetch,
	matchesRefetch,
	isPool = false,
}: PropsT) => {
	const [typeModal, setTypeModal] = useState<ModalType>(isPool ? ModalType.Pool : ModalType.Team);
	const { id, divisionId } = useParams();
	const { favoriteTeamsIds, toggleTeamFavorite } = useFavoriteTeams();

	const PREFIX = 'section';
	useBlockScroll();
	const { breakPont } = useCurrentSize();
	const [activeSection, setActiveSection] = useState<number | null>(0);
	const sectionRefs = useRef<Array<HTMLDivElement | null>>([]);
	const wrapper = useRef<HTMLDivElement | null>(null);

	useEffect(() => {
		if (breakPont) {
			const handleScroll = () => {
				const currentWrapper = wrapper.current;
				const scrollPosition = currentWrapper?.scrollTop || 0;

				sectionRefs.current.forEach((section, index) => {
					const top = (section as HTMLElement)?.offsetTop - OFFSET_ANCHOR;
					const bottom = top + (section as HTMLElement)?.clientHeight;

					if (scrollPosition >= top && scrollPosition < bottom) {
						setActiveSection(index);
					}
				});
			};

			const currentWrapper = wrapper.current;
			currentWrapper?.addEventListener('scroll', handleScroll);

			return () => {
				currentWrapper?.removeEventListener('scroll', handleScroll);
			};
		}
	}, [breakPont]);

	const scrollToSection = (index: number) => {
		const TIME_TO_SCROLL = 0;
		const targetRef = sectionRefs.current[index];
		const targetSection = targetRef;

		if (targetSection && wrapper.current) {
			wrapper.current.scrollTo({
				top: targetSection.offsetTop - OFFSET_ANCHOR,
				behavior: 'instant',
			});
		}

		setTimeout(() => {
			setActiveSection(index);
		}, TIME_TO_SCROLL);
	};
	const [modalTeamScrollPosition, setModalTeamScrollPosition] = useState(0);

	const closePoolModal = (isButtonClosed?: boolean, isBackButton?: boolean) => {
		if (!isBackButton) {
			modalTeamIdSignal.value = '';
		}
		isButtonClosed && closeTeamModal();
		setTypeModal(ModalType.Team);
		if (wrapper.current) {
			wrapper.current.scrollTop = modalTeamScrollPosition || 0;
		}
	};

	const [queryParams] = useQueryParams({
		divisionId: StringParam,
	});

	const [getPools, { loading: loadingPools, refetch: poolsRefetch }] = usePoolsLazyQuery();
	const [currentBracket, setCurrentBracket] = useState<DivisionPool | null>(null);
	const [isShowBracketModal, setIsShowBracketModal] = useState(false);
	const [rounds, setRounds] = useState<PoolsQuery['pools'][]>([]);
	const getBracketsData = useCallback(
		(uuid: string) => {
			const idDivision = queryParams.divisionId || divisionId;
			if (id && idDivision) {
				setCurrentBracket(null);
				getPools({
					variables: {
						id: id,
						divisionId: idDivision,
					},
					fetchPolicy: 'no-cache',
					onCompleted(data) {
						if (data) {
							const groupedRounds = data.pools.reduce(
								(acc, el) => {
									const { r_uuid, ...rest } = el;
									if (!acc[r_uuid!] && el.uuid) {
										acc[r_uuid!] = [];
									}
									if (el.uuid) {
										acc[r_uuid!].push({ r_uuid, ...rest });
									}
									return acc;
								},
								{} as Record<string, PoolsQuery['pools']>,
							);

							const result = Object.values(groupedRounds);
							setRounds(result);

							const pools: DivisionPool[] = [];
							result.forEach((round) => {
								round.forEach((pool) => {
									if (pool.uuid === uuid) {
										pools.push(pool as DivisionPool);
									}
								});
							});
							const pool = pools[pools.length - 1];
							setCurrentBracket(pool);
							setIsShowBracketModal(true);
						}
					},
				});
			}
		},
		[divisionId, getPools, id, queryParams.divisionId],
	);

	const closeBracketModal = (isButtonClosed?: boolean) => {
		setIsShowBracketModal(false);
		isButtonClosed && closeTeamModal();
	};

	const [getPoolData, { data: poolData, loading: loadingPool, refetch: poolRefetch }] =
		usePoolLazyQuery();
	const updatePoolData = useCallback(
		(poolData: PoolBracketUUID) => {
			//* Show Bracket
			// ! Move between pool and bracket
			if (poolData.uuid && !poolData.is_pool) {
				getBracketsData(poolData.uuid);
			}
			//* Update pool
			if (poolData.uuid && poolData.is_pool) {
				getPoolData({
					variables: {
						id: id!,
						poolId: poolData.uuid,
					},
				});
			}
		},
		[getBracketsData, getPoolData, id],
	);

	const openPoolModal = (poolData?: PoolBracketUUID) => {
		// ! first open pool
		if (poolData && poolData.uuid) {
			getPoolData({
				variables: {
					id: id!,
					poolId: poolData.uuid,
				},
			});
		}
		if (poolData) {
			updatePoolData({
				uuid: poolData.uuid,
				is_pool: !!poolData.is_pool,
			});
		}
		setModalTeamScrollPosition(wrapper.current?.scrollTop || 0);
		setTypeModal(ModalType.Pool);
		if (wrapper.current) {
			wrapper.current.scrollTop = 0;
		}
	};

	useEffect(() => {
		if (poolOfTeam?.uuid && isPool) {
			getPoolData({
				variables: {
					id: id!,
					poolId: poolOfTeam.uuid,
				},
			});
		}
	}, [getPoolData, id, isPool, poolOfTeam]);

	const { athletes, staff, results, upcoming, pb_info, bracket_finishes, division_id } =
		(teamPoolData.teamSingle || {}) as TeamSingle;

	const { pool } = poolData || {};

	const poolInfo = pool || (poolOfTeam as EventPool);

	const futureTeamPoolData = poolInfo?.pb_finishes?.teams;
	// const futureTeamUpcoming = teamPoolData.teamSingle.upcoming;
	const schedulePoolData = poolInfo?.upcoming_matches;
	const resultsPoolData = poolInfo?.results;
	const standingsTeamPoolData = poolData?.pool?.standings;
	const resultsOfRound = teamPoolData.teamSingle?.results;
	const { upcomingMatches: clubsWithTeams } = useUpcomingMappedMatches({ eventId: id! });

	const isFuturePool = !!upcoming?.[0]?.is_pool;
	const schedulePoolBracketId = upcoming?.[0].pool_bracket_id;
	const futurePool = pb_info?.find((pool) => pool.uuid === schedulePoolBracketId);

	const futureBracket = bracket_finishes
		? [{ ...bracket_finishes.winner }, { ...bracket_finishes.looser }]
		: [];
	const TEAM_TABS = {
		...(upcoming?.length &&
			poolOfTeam && {
				Schedule: (
					<TeamScheduleTab
						openPoolModal={openPoolModal}
						upcomingList={upcoming}
						poolOfTeam={poolOfTeam}
					/>
				),
			}),
		...(results?.length && {
			Results: (
				<TeamResultTab
					poolOfTeam={poolOfTeam as DivisionPool}
					resultsOfRound={resultsOfRound}
					openPoolModal={openPoolModal}
					resultList={results}
					currentTeamId={teamPoolData.teamSingle.roster_team_id}
				/>
			),
		}),
		//* Bracket future
		...(futureBracket.length &&
			!isFuturePool && {
				Future: (
					<PoolFutureTab
						teamList={futureBracket}
						isBracket
						clubsWithTeams={clubsWithTeams}
						divisionId={division_id}
					/>
				),
			}),
		//* Pool future
		...(futurePool?.pb_finishes?.teams?.length &&
			isFuturePool && {
				Future: (
					<PoolFutureTab
						teamList={futurePool.pb_finishes.teams}
						clubsWithTeams={clubsWithTeams}
						divisionId={division_id}
					/>
				),
			}),
		...(athletes?.length && {
			Roster: <TeamRosterTab athleteList={athletes} staff={staff || []} />,
		}),
	};

	const POOL_TABS = {
		...(standingsTeamPoolData?.length && {
			Standings: <PoolStandingsTab standingList={standingsTeamPoolData} />,
		}),
		...(schedulePoolData?.length && {
			Schedule: (
				<PoolScheduleTab
					scheduleList={schedulePoolData}
					teamSingle={teamPoolData.teamSingle as TeamSingle}
				/>
			),
		}),
		...(resultsPoolData?.length && {
			Results: <PoolResultsTab resultList={resultsPoolData} />,
		}),
		...(futureTeamPoolData?.length && {
			Future: (
				<PoolFutureTab
					teamList={futureTeamPoolData}
					clubsWithTeams={clubsWithTeams}
					divisionId={division_id || Number(divisionId)}
				/>
			),
		}),
	};

	const tabs = typeModal === ModalType.Team ? TEAM_TABS : POOL_TABS;

	const {
		team_name,
		organization_code,
		club_name,
		state,
		roster_team_id,
		roster_club_id,
		master_team_id,
	} = teamPoolData.teamSingle || {};

	const getClubTeams = (clubId: number) => {
		const club = clubsWithTeams?.filter((club) => club.club_id === clubId)?.[0];
		if (!club) {
			return [];
		}
		return uniqBy(club.teams, 'team_id');
	};

	const isFavorite = favoriteTeamsIds.includes(String(roster_team_id!));
	const toggleFavorite = () => {
		const team = getClubTeams(roster_club_id!).find((t) => t.team_id === roster_team_id)!;
		if (!team) return;
		toggleTeamFavorite({
			club_id: String(team.club_id),
			team_id: String(team.team_id),
		});
	};

	const refreshHandler = () => {
		if (typeModal === ModalType.Team) {
			teamRefetch?.();
			matchesRefetch?.();
		}

		if (typeModal === ModalType.Pool) {
			poolRefetch();
			poolsRefetch();
		}
	};

	const { event } = useEventDetails();

	const refreshButton = () => {
		return (
			<StyledAnchorModalRefreshButton
				onClick={refreshHandler}
				$isTeamModal={typeModal === ModalType.Team}
				$isShowStreamForMobile={
					!!event?.teams_settings?.baller_tv_available && breakPont === 'small'
				}
			>
				<img src={refreshIcon} alt="refresh" />
			</StyledAnchorModalRefreshButton>
		);
	};

	const header =
		typeModal === ModalType.Team ? (
			<TeamAnchorHeader
				masterTeamId={master_team_id}
				toggleFavorite={toggleFavorite}
				isFavorite={isFavorite}
				close={closeTeamModal}
				teamName={team_name!}
				code={organization_code!}
				clubName={club_name!}
				state={state!}
				refreshButton={refreshButton}
			/>
		) : (
			poolInfo?.division_short_name &&
			poolInfo?.display_name_short && (
				<PoolAnchorHeader
					next={poolInfo?.next}
					prev={poolInfo?.prev}
					refreshButton={refreshButton}
					close={
						isPool
							? (isButtonClosed, isBackButton) => {
									closeTeamModal();
									closePoolModal(isButtonClosed, isBackButton);
								}
							: closePoolModal
					}
					updatePoolData={updatePoolData}
					name={`${poolInfo?.division_short_name}${poolInfo?.display_name_short}`}
				/>
			)
		);

	return {
		activeSection,
		scrollToSection,
		wrapper,
		sectionRefs,
		PREFIX,
		tabs,
		header,
		isLoading: loadingPool || loadingPools,
		isTeamModal: typeModal === ModalType.Team,
		isLarge: breakPont !== 'small',
		breakPont,
		currentBracket,
		isShowBracketModal,
		closeBracketModal,
		rounds,
		typeModal,
	};
};
