import styled from 'styled-components';

import { ThemeT } from '@/styles/theme';

export const Tab__Wrapper = styled.div``;
export const Tab__NextModalLink = styled.div<ThemeT>`
	padding: 16px 46px 16px 16px;
	border-bottom: 1px solid #f4f6f8;
	display: flex;
	justify-content: space-between;
	cursor: pointer;
	span {
		color: ${(props) => props.theme.colors.blue};
		font-size: 14px;
		line-height: 22px;
		font-weight: 700;

		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			font-size: 16px;
			font-weight: 700;
			line-height: 24px;
		}
	}
	position: relative;
	img {
		position: absolute;
		right: 30px;
		top: 50%;
		transform: translateY(-50%);
	}
`;

//* Roster
export const RosterTab__Container = styled.div`
	padding-bottom: 22px;
`;
export const RosterTab__TheadOverlay = styled.div`
	background: #f4f6f8;
	width: 100%;
	height: 50px;
	position: absolute;
	z-index: -1;
`;
export const RosterTab__RosterTable = styled.table<{ $isStaff?: boolean }>`
	border-collapse: collapse;
	margin-bottom: ${(props) => (props.$isStaff ? '30px' : '0')};
	width: ${(props) => (props.$isStaff ? '100%' : 'auto')};
	tr {
	}
	thead {
		background: #f4f6f8;
		tr {
			th {
				padding: 16px 0;
				font-size: 14px;
				font-style: normal;
				font-weight: 400;
				line-height: 18px;
				&:first-child {
					padding: 0 0 0 16px;
					text-align: left;
					/* width: 20%; */
					width: ${(props) => (props.$isStaff ? '25%' : '100px')};
					@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
						width: ${(props) => (props.$isStaff ? 'auto' : '100px')};
					}
				}
				&:nth-child(2) {
					text-align: left;
					/* width: ${(props) => (props.$isStaff ? '68%' : '60%')}; */
					width: ${(props) => (props.$isStaff ? '68%' : '200px')};
					@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
						width: ${(props) => (props.$isStaff ? 'auto' : '200px')};
					}
				}
				&:last-child {
					text-align: left;
					/* width: 20%; */
					width: ${(props) => (props.$isStaff ? '25%' : '100px')};
					@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
						width: ${(props) => (props.$isStaff ? 'auto' : '100px')};
					}
				}
			}
		}
	}
	tbody {
		tr {
			td {
				padding: 16px 0;
				font-size: 14px;
				font-style: normal;
				font-weight: 400;
				line-height: 18px;
				&:first-child {
					/* width: 20%; */
					width: ${(props) => (props.$isStaff ? '20%' : '100px')};
					@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
						width: ${(props) => (props.$isStaff ? 'auto' : '100px')};
					}
					padding: 0 0 0 16px;
				}
				&:nth-child(2) {
					text-align: left;
					/* max-width: 200px;
					width: 60%; */
					width: ${(props) => (props.$isStaff ? '68%' : '200px')};
					@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
						width: ${(props) => (props.$isStaff ? 'auto' : '200px')};
					}
				}
				&:last-child {
					/* width: 20%; */
					width: ${(props) => (props.$isStaff ? '20%' : '100px')};
					@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
						width: ${(props) => (props.$isStaff ? 'auto' : '100px')};
					}
					text-align: left;
				}
			}
		}
	}
`;

//* Feature
export const FeatureTab__Container = styled.div`
	padding: 0;
`;
//* Schedule
export const ScheduleFeatureTab__Container = styled.div`
	padding-top: 30px;
`;
export const ScheduleFeatureTab__List = styled.ul``;
export const ScheduleFeatureTab__Item = styled.li<{ $isWork?: boolean }>`
	display: flex;
	justify-content: space-between;
	padding: 16px;
	border-bottom: 1px solid #f4f6f8;
	white-space: nowrap;
	span {
		font-size: 14px;
		line-height: 18px;
		font-weight: 400;
		&:first-child {
			font-weight: ${(props) => (props.$isWork ? 600 : 400)};
			font-style: ${(props) => (props.$isWork ? 'italic' : 'normal')};
		}
		&:last-child {
		}
	}
`;
//* Result
export const ResultTab__Container = styled.div``;
export const ResultTab__List = styled.ul``;
export const ResultTab__Item = styled.li`
	display: flex;
	justify-content: space-between;
	width: 100%;
	align-items: center;
	flex-wrap: nowrap;
	padding: 16px;
	border-bottom: 1px solid #f4f6f8;
	overflow: hidden;
	span,
	strong {
		font-size: 14px;
		line-height: 18px;
		font-weight: 400;
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			font-size: 16px;
			font-weight: 400;
			line-height: 24px;
		}
	}
	strong {
		font-weight: 700;
	}

	p:last-child {
		text-align: right;
		width: max-content;
		min-width: 250px;
		@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
			min-width: 190px;
		}
	}

	p:first-child {
		min-width: 240px;
		max-width: 240px;
		@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
			min-width: 140px;
			max-width: 140px;
		}
	}
	p:nth-child(2) {
		min-width: 50px;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			text-align: center;
			min-width: 100px;
		}
	}
	p:nth-child(3) {
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
	p:nth-child(3) {
	}
	p:nth-child(4) {
		width: 140px;
		white-space: nowrap;
	}
`;
// ! Pool Window Modal
export const StandingsTab__Header = styled.div<ThemeT>`
	padding: 16px;
	background: ${(props) => props.theme.colors.blue};
	span {
		font-size: 16px;
		font-weight: 600;
		line-height: 22px;
		color: #fff;
	}
`;
//* Standings
export const StandingsTab__Container = styled.div`
	padding-bottom: 16px;
	table {
		width: 100%;
		border-collapse: collapse;
		thead {
			tr {
				max-height: 54px;
				height: 54px;
				background: ${(props) => props.theme.colors.blue};
				td {
					white-space: nowrap;
					text-align: center;
					padding: 14px 8px;
					font-size: 14px;
					font-weight: 700;
					line-height: 22px;
					background: ${(props) => props.theme.colors.blue};
					color: #fff;
					@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
						font-size: 16px;
						font-weight: 600;
						line-height: 24px;
					}
					&:first-child {
						text-align: left;
						padding: 0 8px 0 16px;
					}
					&:last-child {
						padding: 0 16px 0 8px;
					}
				}
			}
		}
		tbody {
			tr {
				border-bottom: 1px solid #f4f6f8;
				border-top: 1px solid #f4f6f8;
				td {
					position: relative;
					text-align: center;
					font-size: 14px;
					line-height: 18px;
					font-weight: 400;
					padding: 16px 8px 16px;
					color: ${(props) => props.theme.colors.main};
					@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
						font-size: 16px;
						font-weight: 400;
						line-height: 24px;
					}
					&:first-child {
						text-align: left;
						padding: 16px;
						vertical-align: top;
					}
					&:nth-child(2) {
						text-align: left;
						padding: 16px 0 16px 0;
						@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
							text-overflow: ellipsis;
							overflow: hidden;
							white-space: nowrap;
							max-width: 90px;
							padding-left: 20px;
						}
						@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
							padding: 16px 0 16px 58px;
						}
					}
					&:nth-child(3) {
						font-weight: 700;
						@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
							padding: 16px 0 16px 25px;
						}
					}
					&:nth-child(4) {
						white-space: nowrap;
					}
					&:last-child {
						padding: 0 16px 0 8px;
					}
				}
			}
		}
	}
`;
export const StandingsTab__FavoriteIcon = styled.img`
	position: absolute;
	left: -18px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.medium}) {
		left: 0;
		bottom: 18px;
	}
`;

//* Schedule Future
export const FutureTab__Container = styled.div``;
export const ScheduleFutureTab__List = styled.ul`
	padding: 16px 0;
`;
export const ScheduleFutureTab_Item = styled.li`
	display: flex;
	gap: 10px;
	padding: 16px 16px;
	background: #fff;
	margin: 0 0 16px;
	border-top: 1px solid #f4f6f8;
	border-bottom: 1px solid #f4f6f8;
	&:last-child {
		margin: 0;
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		align-items: center;
	}
`;
export const ScheduleFutureTab__ItemCell = styled.div<{ $isBracket?: boolean }>`
	font-family: Public Sans;
	font-size: 14px;
	line-height: 18px;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
		font-weight: 400;
		line-height: 24px;
	}
	&:last-child {
		margin-left: auto;
		text-align: right;
	}
	&:first-child {
		width: ${(props) => (props.$isBracket ? 'auto' : '27px')};
		/* align-self: flex-start; */
		a {
			padding: 0 0 0 3px;
		}
	}
	&:nth-child(2) {
		width: 50%;
	}
`;

export const ScheduleTab__Container = styled.div`
	${ScheduleFutureTab__ItemCell} {
		&:first-child {
			align-self: flex-start;
		}
	}
`;

export const ScheduleFutureTab__MatchNumber = styled.strong<{ $isActive: boolean }>`
	span {
		font-weight: 400;
		padding: 0px 3px;
		border-radius: 3px;
		display: inline-block;
		border: 1px solid
			${(props) => (props.$isActive ? (props) => props.theme.colors.blue : 'transparent')} !important;
	}
`;
export const ScheduleFutureTab__TeamNameWrapper = styled.div``;
export const ScheduleFutureTab__CourtName = styled.div``;
// * Result
export const ResultPoolTab__Container = styled.div`
	padding: 0 0 16px;
	table {
		width: 100%;
		border-collapse: collapse;
		thead {
			tr {
				background: ${(props) => props.theme.colors.blue};
				max-height: 54px;
				height: 54px;

				td {
					white-space: nowrap;
					text-align: center;
					padding: 16px 8px;
					font-size: 14px;
					font-weight: 700;
					line-height: 22px;
					background: ${(props) => props.theme.colors.blue};
					color: #fff;
					&:first-child {
						text-align: left;
						padding: 0 8px 0 16px;
						font-size: 16px;
						font-weight: 600;
					}

					&:last-child {
						padding: 0 16px 0 8px;
					}
				}
			}
		}
		tbody {
			tr {
				/* border-bottom: 1px solid #f4f6f8; */
				border-top: 1px solid #f4f6f8;
				td {
					position: relative;
					text-align: center;
					font-size: 14px;
					line-height: 18px;
					font-weight: 400;
					padding: 16px 0 16px;

					@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
						font-size: 16px;
						font-weight: 400;
						line-height: 24px;
					}
					&:first-child {
						text-align: left;
						vertical-align: top;
						padding: 16px 8px 0 16px;
						width: 70px;
						@media screen and (max-width: ${(props) => props.theme.breakpoints.medium}) {
							width: 60px;
						}
					}
					&:nth-child(2) {
						text-align: left;
						max-width: 200px;
						@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
							max-width: 120px;
						}
						p {
						}
					}
					&:nth-child(3) {
						min-width: 20px;
					}
					&:nth-child(4) {
						min-width: 20px;
					}
					&:nth-child(5) {
						min-width: 20px;
					}
					&:nth-child(6) {
						min-width: 20px;
					}
					&:nth-child(7) {
						min-width: 20px;
					}
					&:nth-child(8) {
						min-width: 20px;
					}
					&:last-child {
						padding: 0 16px 0 8px;
					}
				}
			}
		}
	}
`;
export const ResultPoolTab__ShowMoreTr = styled.tr`
	border: none !important;
	&:last-child {
		border-bottom: 1px solid #f4f6f8 !important;
	}
`;
export const ResultPoolTab__ShowAllDetailsButton = styled.span`
	font-size: 14px;
	font-weight: 400;
	line-height: 18px;
	text-decoration-line: underline;
	text-underline-position: from-font;
	cursor: pointer;
	color: #fff;
	margin: 0 0 0 7px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 12px;
	}
	&:hover {
		text-decoration: none;
	}
`;
export const ResultPoolTab__ShowMoreButton = styled.div`
	padding: 0 30px 6px 70px;
	text-align: center;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.medium}) {
		padding: 0 25px 6px 60px;
	}
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
	}
	span {
		color: #637381;
		font-size: 14px;
		line-height: 18px;
		text-decoration-line: underline;
		@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
			font-weight: 400;
			line-height: 24px;
		}
	}
	i {
		font-style: normal;
	}
`;
export const ResultPoolTab__ShowMoreContent = styled.div`
	overflow: hidden;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 12px;
		border-radius: 0px 0px 8px 8px;
		background: #f4f6f8;
		box-shadow: 0px 16px 32px -4px rgba(145, 158, 171, 0.16);
		position: relative;
		top: -15px;
		left: -60px;
		padding: 10px 16px 0 19px;
		width: calc(100% + 86px);
	}
	div:first-child {
		float: left;
	}
	div:last-child {
		float: right;
		text-align: right;
	}
	i {
		text-align: left;
		font-style: italic;
	}
`;
export const ResultPoolTab__CellWrapper = styled.div<{ $isLeft?: boolean; $right?: number }>`
	top: 0;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
`;
export const ResultPoolTab__StreamWrapper = styled.div`
	position: relative;
	a {
		position: absolute;
		left: -52px;

		@media screen and (max-width: ${(props) => props.theme.breakpoints.medium}) {
			left: -42px;
		}
	}
`;
export const ResultPoolTab__MatchNumber = styled.b<{ $isActive: boolean }>`
	span {
		font-weight: 400;
		padding: 0px 3px;
		border-radius: 3px;
		display: block;
		border: 1px solid
			${(props) => (props.$isActive ? (props) => props.theme.colors.blue : 'transparent')} !important;
	}
	a {
		padding: 0 0 0 3px;
	}
`;
