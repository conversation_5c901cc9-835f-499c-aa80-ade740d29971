import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { bracketGridMatchNameSignal } from '@/signals/bracketGridMatchNameSignal';
import { Fragment, useEffect, useRef, useState } from 'react';

import { IsBold } from '@/styles/shared';

import { PoolResults } from '@/generated/graphql';

import {
	ResultPoolTab__CellWrapper,
	ResultPoolTab__Container,
	ResultPoolTab__MatchNumber,
	ResultPoolTab__ShowAllDetailsButton,
	ResultPoolTab__ShowMoreTr,
	ResultPoolTab__StreamWrapper,
	Tab__Wrapper,
} from '../../styled';
import { EmptyTableRow } from '../components/EmptyTableRow';
import poolCameraIcon from '../img/pool-camera.svg';
import { PoolResultItem } from './PoolResultItem';

type PropsT = {
	resultList: PoolResults[];
};
export const PoolResultsTab = ({ resultList }: PropsT) => {
	const [isShowAllDetails, setIsShowAllDetails] = useState(false);
	const { breakPont } = useCurrentSize();
	const isSmall = breakPont === 'small';
	const { event } = useEventDetails();
	const matchGridNumber = bracketGridMatchNameSignal.value.match(/M(\d+)/)?.[0];
	const tableBodyRef = useRef<HTMLTableSectionElement>(null);

	useEffect(() => {
		if (!matchGridNumber || !tableBodyRef.current) return;

		const activeMatchRow = Array.from(tableBodyRef.current.children).find((row) => {
			const matchNumberCell = row.querySelector('[data-match-number]') as HTMLElement;
			return matchNumberCell?.dataset.matchNumber === matchGridNumber;
		});

		if (activeMatchRow) {
			activeMatchRow.scrollIntoView({
				behavior: 'smooth',
				block: 'center',
			});
		}
	}, [matchGridNumber]);

	const is4SetExist = resultList.some((result) => result.results.set4);
	const is5SetExist = resultList.some((result) => result.results.set5);

	const getColSpan = () => {
		if (is4SetExist && is5SetExist) {
			return 8;
		}
		if (is4SetExist) {
			return 7;
		}
		if (is5SetExist) {
			return 7;
		}
		return 6;
	};
	return (
		<Tab__Wrapper>
			<ResultPoolTab__Container>
				<table>
					<thead>
						<tr>
							<td colSpan={2}>
								Results
								{isSmall && (
									<ResultPoolTab__ShowAllDetailsButton
										onClick={() => setIsShowAllDetails(!isShowAllDetails)}
									>
										{isShowAllDetails ? 'Hide' : 'Show'} All Details
									</ResultPoolTab__ShowAllDetailsButton>
								)}
							</td>
							<td>Set W-L</td>
							<td>&nbsp;</td>
							<td>Scores</td>
							<td>&nbsp;</td>
							{is4SetExist && <td>&nbsp;</td>}
							{is5SetExist && <td>&nbsp;</td>}
						</tr>
					</thead>
					<tbody ref={tableBodyRef}>
						{resultList.map((result, index) => {
							const set1 = result.results.set1?.split('-') || [];
							const set2 = result.results.set2?.split('-') || [];
							const set3 = result.results.set3?.split('-') || [];

							const set4 = result.results.set4?.split('-') || [];
							const set5 = result.results.set5?.split('-') || [];

							const scores = result.results.team1?.scores?.split(' ') || [];
							const matchName = `M${index + 1}`;
							return (
								<Fragment key={`pool-result-${result.match_id}`}>
									<EmptyTableRow />
									<tr>
										<td>
											<ResultPoolTab__MatchNumber
												$isActive={matchGridNumber === matchName}
												data-match-number={matchName}
											>
												<span>{matchName}</span>
												{event?.teams_settings?.baller_tv_available && (
													<a
														target="_blank"
														href={`${import.meta.env.VITE_BALLERTV_URL}/streams?sport_wrench_match_uuid=${result.match_id}`}
														rel="noreferrer"
													>
														<img src={poolCameraIcon} alt="" />
													</a>
												)}
											</ResultPoolTab__MatchNumber>{' '}
										</td>
										<td>
											<IsBold $isBold={(result?.results?.team1?.matches_won || 0) > 0}>
												{result.team1_name}
											</IsBold>
											<ResultPoolTab__StreamWrapper>
												<IsBold $isBold={(result?.results?.team2?.matches_won || 0) > 0}>
													{result.team2_name}
												</IsBold>
											</ResultPoolTab__StreamWrapper>
										</td>
										<td>
											<ResultPoolTab__CellWrapper $isLeft>
												<p>
													<b>
														{scores[0].split('-')[0]} - {scores[0].split('-')[1]}
													</b>
												</p>
												<p>
													<b>
														{scores[0].split('-')[1]} - {scores[0].split('-')[0]}
													</b>
												</p>
											</ResultPoolTab__CellWrapper>
										</td>
										<td>
											<ResultPoolTab__CellWrapper>
												<p>
													<IsBold $isBold={Number(set1[0]) > Number(set1[1])}>{set1[0]}</IsBold>
												</p>
												<p>
													<IsBold $isBold={Number(set1[1]) > Number(set1[0])}>{set1[1]}</IsBold>
												</p>
											</ResultPoolTab__CellWrapper>
										</td>
										<td>
											<ResultPoolTab__CellWrapper $right={set3.length ? 35 : -25}>
												<p>
													<IsBold $isBold={Number(set2[0]) > Number(set2[1])}>{set2[0]}</IsBold>
												</p>
												<p>
													<IsBold $isBold={Number(set2[1]) > Number(set2[0])}>{set2[1]}</IsBold>
												</p>
											</ResultPoolTab__CellWrapper>
										</td>
										{!!set3.length && (
											<td>
												<ResultPoolTab__CellWrapper $right={40}>
													<p>
														<IsBold $isBold={Number(set3[0]) > Number(set3[1])}>{set3[0]}</IsBold>
													</p>
													<p>
														<IsBold $isBold={Number(set3[1]) > Number(set3[0])}>{set3[1]}</IsBold>
													</p>
												</ResultPoolTab__CellWrapper>
											</td>
										)}
										{!!set4.length && (
											<td>
												<ResultPoolTab__CellWrapper>
													<p>
														<IsBold $isBold={Number(set4[0]) > Number(set4[1])}>{set4[0]}</IsBold>
													</p>
													<p>
														<IsBold $isBold={Number(set4[1]) > Number(set4[0])}>{set4[1]}</IsBold>
													</p>
												</ResultPoolTab__CellWrapper>
											</td>
										)}
										{!!set5.length && (
											<td>
												<ResultPoolTab__CellWrapper>
													<p>
														<IsBold $isBold={Number(set5[0]) > Number(set5[1])}>{set5[0]}</IsBold>
													</p>
													<p>
														<IsBold $isBold={Number(set5[1]) > Number(set5[0])}>{set5[1]}</IsBold>
													</p>
												</ResultPoolTab__CellWrapper>
											</td>
										)}
									</tr>
									<ResultPoolTab__ShowMoreTr>
										<td colSpan={getColSpan()} style={{ padding: 0 }}>
											<PoolResultItem
												isShowAllDetails={isSmall ? isShowAllDetails : !isSmall}
												courtName={result.court_name!}
												refName={result.ref_team_name!}
												date={result.date_start!}
											/>
										</td>
									</ResultPoolTab__ShowMoreTr>
								</Fragment>
							);
						})}
					</tbody>
				</table>
			</ResultPoolTab__Container>
		</Tab__Wrapper>
	);
};
