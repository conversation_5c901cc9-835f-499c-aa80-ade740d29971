import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { bracketGridMatchNameSignal } from '@/signals/bracketGridMatchNameSignal';
import { useEffect, useRef } from 'react';

import { PoolUpcomingMatches, TeamSingle } from '@/generated/graphql';

import { getFormateTime } from '@/utils/time';

import {
	ScheduleFutureTab_Item,
	ScheduleFutureTab__ItemCell,
	ScheduleFutureTab__List,
	ScheduleFutureTab__MatchNumber,
	ScheduleFutureTab__TeamNameWrapper,
	ScheduleTab__Container,
	StandingsTab__Header,
	Tab__Wrapper,
} from '../styled';
import poolCameraIcon from './img/pool-camera.svg';

type PropsT = {
	scheduleList: PoolUpcomingMatches[];
	teamSingle: TeamSingle;
};

export const PoolScheduleTab = ({ scheduleList }: PropsT) => {
	const { event } = useEventDetails();
	const matchGridNumber = bracketGridMatchNameSignal.value.match(/M(\d+)/)?.[0];
	const listRef = useRef<HTMLUListElement>(null);

	useEffect(() => {
		if (!matchGridNumber || !listRef.current) return;

		const activeMatchElement = Array.from(listRef.current.children).find((item) => {
			const matchNumberElement = item.querySelector('[data-match-number]') as HTMLElement;
			return matchNumberElement?.dataset.matchNumber === matchGridNumber;
		});

		if (activeMatchElement) {
			activeMatchElement.scrollIntoView({
				behavior: 'smooth',
				block: 'center',
			});
		}
	}, [matchGridNumber]);

	return (
		<Tab__Wrapper>
			<ScheduleTab__Container>
				<StandingsTab__Header>
					<span>Schedule</span>
				</StandingsTab__Header>
				<ScheduleFutureTab__List ref={listRef}>
					{scheduleList.map(
						({
							team1_name,
							team2_name,
							match_id,
							court_name,
							ref_team_name,
							date_start,
							display_name,
						}) => {
							const displayMatchNumber = display_name?.match(/M(\d+)/)?.[0];

							return (
								<ScheduleFutureTab_Item key={match_id}>
									<ScheduleFutureTab__ItemCell>
										<ScheduleFutureTab__MatchNumber
											$isActive={matchGridNumber === displayMatchNumber}
											data-match-number={displayMatchNumber}
										>
											<span>{displayMatchNumber}</span>
											{event?.teams_settings?.baller_tv_available && (
												<a
													target="_blank"
													href={`${import.meta.env.VITE_BALLERTV_URL}/streams?sport_wrench_match_uuid=${match_id}`}
													rel="noreferrer"
												>
													<img src={poolCameraIcon} alt="" />
												</a>
											)}
										</ScheduleFutureTab__MatchNumber>
									</ScheduleFutureTab__ItemCell>
									<ScheduleFutureTab__ItemCell>
										<ScheduleFutureTab__TeamNameWrapper>
											<strong> {team1_name}</strong>
										</ScheduleFutureTab__TeamNameWrapper>
										<ScheduleFutureTab__TeamNameWrapper>
											<strong>{team2_name}</strong>
										</ScheduleFutureTab__TeamNameWrapper>

										<ScheduleFutureTab__TeamNameWrapper>
											<i>Work: {ref_team_name}</i>
										</ScheduleFutureTab__TeamNameWrapper>
									</ScheduleFutureTab__ItemCell>
									<ScheduleFutureTab__ItemCell>
										<div>
											{date_start && getFormateTime({ time: date_start, format: 'EEE, h:mmaaa' })}
										</div>
										<div>{court_name}</div>
									</ScheduleFutureTab__ItemCell>
								</ScheduleFutureTab_Item>
							);
						},
					)}
				</ScheduleFutureTab__List>
			</ScheduleTab__Container>
		</Tab__Wrapper>
	);
};
