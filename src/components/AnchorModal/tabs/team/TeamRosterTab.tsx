import { useState } from 'react';

import { ModalTitle } from '@/components/ModalTitle';

import { TeamSingleAthlete, TeamSingleStaff } from '@/generated/graphql';

import {
	RosterTab__Container,
	RosterTab__RosterTable,
	RosterTab__TheadOverlay,
	Tab__Wrapper,
} from '../styled';

type PropsT = {
	athleteList: TeamSingleAthlete[];
	staff: TeamSingleStaff[];
};

export const TeamRosterTab = ({ athleteList, staff }: PropsT) => {
	const [isOpen, setIsOpen] = useState(true);
	return (
		<Tab__Wrapper>
			<RosterTab__Container>
				<ModalTitle title="Roster" isOpen={isOpen} setIsOpen={setIsOpen} isShowAccordion />
				{isOpen && (
					<>
						<RosterTab__TheadOverlay />
						<RosterTab__RosterTable>
							<thead>
								<tr>
									<th>Uniform</th>
									<th>Player name</th>
									<th>Position</th>
								</tr>
							</thead>
							<tbody>
								{athleteList.map((athlete) => {
									const fullName = `${athlete.first} ${athlete.last}`;
									return (
										<tr key={`anchor_${fullName}`}>
											<td>{athlete.uniform || 'unknown'}</td>
											<td>{fullName}</td>
											<td>{athlete.short_position || 'unknown'}</td>
										</tr>
									);
								})}
							</tbody>
						</RosterTab__RosterTable>
						{staff.length > 0 && (
							<>
								<RosterTab__TheadOverlay />
								<RosterTab__RosterTable $isStaff>
									<thead>
										<tr>
											<th>Staff name</th>
											<th>Role</th>
											<th>&nbsp;</th>
										</tr>
									</thead>
									<tbody>
										{staff.map((item) => {
											const fullName = `${item.first} ${item.last}`;
											return (
												<tr key={`anchor_${fullName}`}>
													<td>{fullName}</td>
													<td>{item.role_name}</td>
													<td>&nbsp;</td>
												</tr>
											);
										})}
									</tbody>
								</RosterTab__RosterTable>
							</>
						)}
					</>
				)}
			</RosterTab__Container>
		</Tab__Wrapper>
	);
};
