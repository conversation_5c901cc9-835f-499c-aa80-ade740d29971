import arrowRight from '@assets/arrowRightBlue-icon.svg';
import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { StringParam, useQueryParams } from 'use-query-params';

import { ModalTitle } from '@/components/ModalTitle';

import { DivisionPool, EventUpcoming } from '@/generated/graphql';

import { getMatchName } from '@/services/teams.service';

import { getFormateTime } from '@/utils/time';

import {
	AnchorModal__ContentItem,
	AnchorModal__ContentLeft,
	AnchorModal__ContentRight,
} from '../../styled';
import { PoolBracketUUID } from '../../types';
import {
	ScheduleFeatureTab__Container,
	ScheduleFeatureTab__Item,
	ScheduleFeatureTab__List,
	Tab__NextModalLink,
	Tab__Wrapper,
} from '../styled';

type PropsT = {
	openPoolModal: (_data?: PoolBracketUUID) => void;
	upcomingList: EventUpcoming[];
	poolOfTeam: (DivisionPool & { division_id?: string }) | null;
};
export const TeamScheduleTab = ({ openPoolModal, upcomingList, poolOfTeam }: PropsT) => {
	const { pathname } = useLocation();

	const [queryParams, setQueryParams] = useQueryParams({
		divisionId: StringParam,
	});

	useEffect(() => {
		if (pathname.includes('clubs-teams')) {
			setQueryParams({ divisionId: poolOfTeam?.division_id });
		}
	}, [pathname, poolOfTeam, queryParams, setQueryParams]);
	const scheduleData = upcomingList[0];

	return (
		<Tab__Wrapper>
			<ScheduleFeatureTab__Container>
				<ModalTitle title="Schedule" />
				<Tab__NextModalLink
					onClick={() =>
						openPoolModal(
							scheduleData
								? {
										uuid: scheduleData.pool_bracket_id || '',
										is_pool: !!scheduleData.is_pool || false,
									}
								: undefined,
						)
					}
				>
					<span>
						{scheduleData?.round_name} {scheduleData?.pb_name}-{' '}
						{scheduleData?.settings && getMatchName(scheduleData?.settings)}
					</span>
					<img src={arrowRight} alt="" />
				</Tab__NextModalLink>
				<ScheduleFeatureTab__List>
					<AnchorModal__ContentItem>
						<AnchorModal__ContentLeft>
							{upcomingList.map((item) => {
								const isRef = item.match_type === 'ref';
								return (
									<ScheduleFeatureTab__Item key={`anchor_${item.match_id}`} $isWork={isRef}>
										{isRef ? <span>Work</span> : <span>vs {item.opponent_team_name}</span>}
									</ScheduleFeatureTab__Item>
								);
							})}
						</AnchorModal__ContentLeft>
						<AnchorModal__ContentRight>
							{upcomingList.map((item) => {
								const isRef = item.match_type === 'ref';
								return (
									<ScheduleFeatureTab__Item key={`anchor_${item.match_id}`} $isWork={isRef}>
										<span>
											{getFormateTime({ time: item.date_start!, format: 'EEE, h:mmaaa' })}-{' '}
											{item.court_name}
										</span>
									</ScheduleFeatureTab__Item>
								);
							})}
						</AnchorModal__ContentRight>
					</AnchorModal__ContentItem>
				</ScheduleFeatureTab__List>
			</ScheduleFeatureTab__Container>
		</Tab__Wrapper>
	);
};
