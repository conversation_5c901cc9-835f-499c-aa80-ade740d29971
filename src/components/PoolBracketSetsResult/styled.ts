import styled from 'styled-components';

export const PoolBracketSetsResult__Container = styled.section`
	display: flex;
	align-items: center;
	gap: 5px;
	padding: 0 0 0 60px;
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
		padding: 0 0 0;
	}
`;
export const PoolBracketSetsResult__Sets = styled.span<{ $rightPadding?: boolean }>`
	margin: ${({ $rightPadding }) => ($rightPadding ? '0 53px 0 0' : '0')};
	font-weight: 700;
`;
export const PoolBracketSetsResult__Percentage = styled.span<{ $rightPadding?: boolean }>`
	font-style: italic;
	margin: ${({ $rightPadding }) => (!$rightPadding ? '0 12px 0 0' : '0')};
`;
