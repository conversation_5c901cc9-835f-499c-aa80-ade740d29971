import { DivisionStanding } from '@generated/graphql';

type Standing = Pick<DivisionStanding, 'points_ratio'>;

type Props = {
	standing?: Standing | null;
};

export const StandingPointsRatio = ({ standing }: Props) => {
	const { points_ratio } = standing || {};
	return (
		<>
			{points_ratio !== undefined
				? points_ratio > 0
					? points_ratio.toFixed(2)
					: points_ratio
				: '-'}
		</>
	);
};
