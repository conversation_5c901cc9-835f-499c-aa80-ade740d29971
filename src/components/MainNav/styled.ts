import styled from 'styled-components';

export const MainNav__Wrapper = styled.div`
	padding: 13px 0;
	margin: 0 0 10px;
`;
export const MainNav__List = styled.ul`
	display: flex;
	gap: 25px;
	max-width: 700px;
`;
export const MainNav__Item = styled.li<{ $isActive?: boolean }>`
	display: flex;
	align-items: center;
	white-space: nowrap;
	font-size: 14px;
	font-weight: 400;
	line-height: 22px;
	&:hover img {
		transform: scale(0.95);
		transition: all 0.3s ease-in-out 0s;
		@media screen and (min-width: 690px) {
			transform: scale(1.075);
		}
	}
	a {
		color: #fff;
		text-decoration: none;
		position: relative;
		&:before {
			content: '';
			position: absolute;
			width: 100%;
			height: 2px;
			bottom: -11px;
			left: 0;
			background-color: #fff;
			visibility: ${(props) => (props.$isActive ? 'visible' : 'hidden')};
			transform: ${(props) => (props.$isActive ? 'scaleX(1)' : 'scaleX(0)')};
			transition: all 0.3s ease-in-out 0s;
			border-radius: 1px;
		}
		&:hover:before {
			visibility: visible;
			transform: scaleX(1);
		}
	}
`;
export const MainNav__Icon = styled.img`
	margin: 0 4px 0 0;
	transition: all 0.3s ease-in-out 0s;
`;
