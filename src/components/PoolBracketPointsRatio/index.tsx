import { PoolOrBracketStatItem } from '@generated/graphql';

type PoolOrBracketStat = Pick<PoolOrBracketStatItem, 'points_ratio'>;

type Props = {
	pbStat?: PoolOrBracketStat | null;
};

export const PoolBracketPointsRatio = ({ pbStat }: Props) => {
	if (typeof pbStat?.points_ratio !== 'number') return null;
	const { points_ratio } = pbStat;
	return <>{points_ratio % 1 !== 0 ? points_ratio.toFixed(3) : points_ratio}</>;
};
