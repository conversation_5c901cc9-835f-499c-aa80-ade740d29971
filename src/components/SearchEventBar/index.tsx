import { EventDetails } from '@/shared/hooks/useEventDetails';
import { getTicketsDirectLink } from '@/utils';
import buyAdmissionIcon from '@assets/buy-admission-icon.svg';
import { Dispatch, SetStateAction } from 'react';
import { StringParam, useQueryParams } from 'use-query-params';

import searchIcon from '@/assets/search-icon.svg';
import searchAboutIcon from '@/assets/searchAbout-icon.svg';

import {
	SearchBar__AdmissionTitle,
	SearchBar__EventDescription,
	SearchBar__EventInfo,
	SearchBar__EventTitle,
	SearchBar__FormWrapper,
	SearchBar__SearchIcon,
	SearchBar__TextField,
	SearchBar__TextFieldInner,
	SearchBar__TextFieldWrapper,
	SearchBar__TitleWrapper,
	SearchBar__Wrapper,
} from './styled';

type PropsT = {
	eventName?: string;
	isShowBuyAdmission?: boolean;
	placeholder?: string;
	description?: null | (() => JSX.Element);
	isGlobalSearch?: boolean;
	search: string;
	setSearch: Dispatch<SetStateAction<string>>;
	event?: EventDetails;
};
export const SearchEventBar = ({
	eventName,
	description,
	isGlobalSearch,
	isShowBuyAdmission,
	placeholder = 'Club, team, athlete, division',
	search,
	setSearch,
	event,
}: PropsT) => {
	const [, setQueryParams] = useQueryParams({
		teamCode: StringParam,
		gender: StringParam,
		search: StringParam,
	});

	const ticketLink = getTicketsDirectLink(event);

	const onChangeSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearch(e.target.value);
		setQueryParams({ search: e.target.value });
	};

	return (
		<SearchBar__Wrapper $isGlobalSearch={isGlobalSearch}>
			<SearchBar__FormWrapper>
				<SearchBar__TextFieldWrapper>
					<SearchBar__TitleWrapper>
						<SearchBar__EventInfo $isFullWidth={!isShowBuyAdmission}>
							<SearchBar__EventTitle>{eventName}</SearchBar__EventTitle>
							{description && (
								<SearchBar__EventDescription>{description()}</SearchBar__EventDescription>
							)}
						</SearchBar__EventInfo>
						{isShowBuyAdmission && (
							<SearchBar__AdmissionTitle href={ticketLink} target="_blank">
								<img src={buyAdmissionIcon} alt="buy admission" /> Buy Admission
							</SearchBar__AdmissionTitle>
						)}
					</SearchBar__TitleWrapper>
					<SearchBar__TextFieldInner>
						{isGlobalSearch ? (
							<SearchBar__SearchIcon src={searchAboutIcon} alt="search" />
						) : (
							<SearchBar__SearchIcon src={searchIcon} alt="search" />
						)}
						<SearchBar__TextField
							$isGlobalSearch={isGlobalSearch}
							type="text"
							placeholder={placeholder}
							onChange={onChangeSearch}
							value={search}
						/>
					</SearchBar__TextFieldInner>
				</SearchBar__TextFieldWrapper>
			</SearchBar__FormWrapper>
		</SearchBar__Wrapper>
	);
};
