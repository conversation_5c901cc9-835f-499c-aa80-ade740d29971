import styled from 'styled-components';

export const SCROLL_OFFSET = 39;
export const SCROLL_WITH_DESCRIPTION_OFFSET = 50;
const SEARCH_BAR_HEIGHT = 40;

export const SearchBar__Wrapper = styled.div<{
	$isHasDescription?: boolean;
	$isLarge?: boolean;
	$isAbout?: boolean;
	$isTextFieldHidden?: boolean;
}>`
	background: ${(props) => props.theme.colors.blue};
	padding: 16px;
	width: 100%;
	height: ${({ $isHasDescription, $isTextFieldHidden }) => {
		return ($isHasDescription ? 90 : 70) + ($isTextFieldHidden ? 0 : SEARCH_BAR_HEIGHT);
	}}px;
	z-index: 9;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		position: fixed;
		height: 200px;
		top: 62px;
		padding: ${({ $isAbout }) => ($isAbout ? '45px 60px 0 60px' : '12px 60px')};
		/* &.sticky {
			padding: ${({ $isAbout }) => ($isAbout ? '50px 60px 0 60px' : '0 60px')};
		} */
	}
`;
export const SearchBar__EventInfo = styled.div<{ $isFullWidth?: boolean }>`
	width: ${({ $isFullWidth }) => ($isFullWidth ? '100%' : 'calc(100% - 100px)')};
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		width: ${({ $isFullWidth }) => ($isFullWidth ? '100%' : 'calc(100% - 150px)')};
	}
`;
export const SearchBar__EventTitle = styled.p`
	font-size: 14px;
	line-height: 22px;
	color: #fff;
	font-weight: 600;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	a {
		color: #fff;
		text-decoration: none;
		&:hover {
			text-decoration: underline;
		}
	}
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 20px;
		font-weight: 700;
		line-height: 30px;
		margin-bottom: 4px;
	}
`;
export const SearchBar__EventDescription = styled.p`
	color: #f9fafb;
	font-size: 14px;
	line-height: 18px;
	margin: 2px 0 0;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
		font-style: normal;
		display: flex;
		img {
			padding: 0 4px;
			&:first-child {
				padding-left: 0;
			}
			&:last-child {
				padding-left: 14px;
			}
		}
	}
`;
export const SearchBar__FormWrapper = styled.div`
	display: flex;
	justify-content: space-between;
	position: relative;
`;
export const SearchBar__TextFieldWrapper = styled.div`
	width: 100%;
`;
export const SearchBar__TextFieldInner = styled.div`
	position: relative;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		max-width: 600px;
		margin: auto;
	}
`;
export const SearchBar__TextField = styled.input`
	border-radius: 4px;
	border: 1px solid #f9fafb;
	width: 100%;
	min-height: ${SEARCH_BAR_HEIGHT}px;
	background: transparent;
	padding: 0 40px 0 16px;
	font-size: 14px;
	line-height: 22px;
	color: #f9fafb;
	outline: none;
	caret-color: #f9fafb;
	&::placeholder {
		color: #fff;
	}
`;
export const SearchBar__AdmissionTitle = styled.a`
	font-size: 10px;
	text-decoration: underline;
	line-height: 18px;
	color: #f9fafb;
	text-align: right;
	max-width: 90px;
	display: flex;
	align-items: center;
	gap: 4px;
	width: 100%;
	margin: 3px 0 0;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		font-size: 16px;
		max-width: 140px;
		margin: 5px 0 0 15px;
		text-decoration: none;
		&:hover {
			text-decoration: underline;
		}
		img {
			width: 20px;
			height: 14px;
		}
	}
`;
export const SearchBar__SearchIcon = styled.img<{ $isClose?: boolean }>`
	position: absolute;
	right: 15px;
	top: 50%;
	transform: translateY(-50%);
	cursor: ${({ $isClose }) => ($isClose ? 'pointer' : 'default')};
`;
export const SearchBar__TitleWrapper = styled.div`
	display: flex;
	justify-content: space-between;
	margin: 0 0 16px;
	align-items: start;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		margin-bottom: 25px;
	}
`;
export const SearchBar__Sticky = styled.footer<{ $isFixed: boolean; $isHasDescription?: boolean }>`
	gap: 10px;
	position: ${({ $isFixed }) => ($isFixed ? 'fixed' : 'static')};
	background: ${(props) => props.theme.colors.blue};
	width: 100%;
	left: 0;
	top: 44px;
	padding: ${({ $isFixed }) => ($isFixed ? 16 : 0)}px;
	z-index: 1;
`;
