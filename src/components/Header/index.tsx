import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useEventDescription } from '@/shared/hooks/useEventDescription';
import { eventSignal } from '@/signals/eventSignal';
import { Link, useParams } from 'react-router-dom';

import backButtonIcon from '@/assets/backButton-icon.svg';
import logoMobile from '@/assets/logo-mobile.svg';

import { AsideMenu } from '../Aside';
import { useHeader } from './hooks/useHeader';
import logoDesktop from './img/logo-desktop.svg';
import {
	Header__BackLink,
	Header__DescriptionWrapper,
	Header__Divider,
	Header__EventName,
	Header__Label,
	Header__Left,
	Header__Logo,
	Header__LogoNavBox,
	Header__PageName,
	Header__Right,
	Header__Wrapper,
} from './styled';

export const Header = () => {
	const { activeTab } = useHeader();
	const { breakPont } = useCurrentSize();
	const isSmall = breakPont === 'small';
	const params = useParams();

	const description = useEventDescription();
	if (isSmall) {
		return (
			<Header__Wrapper>
				<Header__LogoNavBox>
					{activeTab?.path && (
						<Header__BackLink to={activeTab.path}>
							<img src={backButtonIcon} alt="back" />
						</Header__BackLink>
					)}
					<Header__Logo to="/">
						<img src={logoMobile} alt="SportWrench" loading="lazy" />
					</Header__Logo>
				</Header__LogoNavBox>
				<Header__Label>{activeTab?.title}</Header__Label>
				<AsideMenu />
			</Header__Wrapper>
		);
	}

	if (params.id && !eventSignal.value) {
		return null;
	}

	return (
		<Header__Wrapper>
			<Header__Left>
				<Header__LogoNavBox>
					<Header__Logo to="/">
						<img src={logoDesktop} alt="SportWrench" loading="lazy" />
					</Header__Logo>
				</Header__LogoNavBox>
				<Header__EventName>
					{params.id && <Link to={`/events/${params.id}`}>{eventSignal?.value?.long_name}</Link>}
				</Header__EventName>
				{eventSignal?.value?.long_name && params.id && <Header__Divider>/</Header__Divider>}
				<Header__PageName>{activeTab?.title}</Header__PageName>
			</Header__Left>
			<Header__Right>
				{description && params.id && (
					<Header__DescriptionWrapper>{description()}</Header__DescriptionWrapper>
				)}
			</Header__Right>
		</Header__Wrapper>
	);
};
