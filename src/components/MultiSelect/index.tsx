import { Dispatch, SetStateAction } from 'react';

import {
	Checkbox,
	FormControl,
	ListItemText,
	MenuItem,
	Select,
	SelectChangeEvent,
} from '@mui/material';

import { MenuProps } from './styled';

type PropsT = {
	data: Record<string, string>[];
	selectedData: string[];
	setSelectedData: Dispatch<SetStateAction<string[]>>;
};

export const MultiSelect = ({ data, selectedData, setSelectedData }: PropsT) => {
	const handleChange = (event: SelectChangeEvent<string[]>) => {
		const value = event.target.value as string[];
		setSelectedData(() => {
			if (value.length > 0 && value[0] === '0') {
				return value.filter((x) => x !== '0');
			}
			if (value.length > 0 && value.findIndex((el) => el === '0') > 0) {
				return value.filter((x) => x === '0');
			}
			if (value.length === 0) {
				return ['0'];
			}
			return value;
		});
	};
	return (
		<div>
			<FormControl sx={{ m: 1, width: 250 }}>
				<Select
					multiple
					value={selectedData}
					onChange={handleChange}
					renderValue={(selected) =>
						selected.map((x) => data.find((y) => y.value === x)?.key).join(', ')
					}
					MenuProps={MenuProps}
				>
					{[...data].map((item) => (
						<MenuItem key={item.value} value={item.value}>
							<>
								<Checkbox checked={selectedData.findIndex((el) => el === item.value) >= 0} />
								<ListItemText primary={item.key} />
							</>
						</MenuItem>
					))}
				</Select>
			</FormControl>
		</div>
	);
};
