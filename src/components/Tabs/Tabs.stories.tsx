import Providers from '@/app/Providers';
import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';

import { Box } from '@mui/material';

import { Tabs } from '.';

const TABS = {
	Current: <div>Current</div>,
	Future: <div>Future</div>,
	Past: <div>Past</div>,
};
const meta = {
	title: 'Navigation/Tabs',
	component: Tabs,
	parameters: {
		layout: 'padded',
		docs: {
			description: {
				component: 'Navigation Tabs',
			},
		},
	},
	tags: ['autodocs'],
	decorators: [
		(Story) => {
			const [activeTab, setActiveTab] = useState('Current');
			return (
				<Providers>
					<Box>
						<Box sx={{ display: 'none' }}>
							<Story />
						</Box>
						<Tabs
							activeTab={activeTab}
							handleChange={(_e, value) => setActiveTab(value)}
							tabs={Object.entries(TABS)}
						/>
					</Box>
				</Providers>
			);
		},
	],
} satisfies Meta<typeof Tabs>;

export default meta;
type Story = StoryObj<typeof meta>;
export const Outlined: Story = {
	args: {
		tabs: Object.entries({
			Current: <div>Current</div>,
			Future: <div>Future</div>,
			Past: <div>Past</div>,
		}),
		activeTab: 'Current',
		handleChange: () => {},
	},
};
