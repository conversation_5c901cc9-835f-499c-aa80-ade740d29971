import { useCurrentSize } from '@/shared/hooks/useCurrentSize';
import { useDesktopSticky } from '@/shared/hooks/useDesktopSticky';

import TabContext from '@mui/lab/TabContext';
import TabPanel from '@mui/lab/TabPanel';
import Tab from '@mui/material/Tab';

import {
	Tabs__ContentWrapper,
	Tabs__ItemsWrapper,
	Tabs__ItemsWrapperTabs,
	Tabs__Wrapper,
} from './styled';

type Props = {
	activeTab: string;
	handleChange: (_event: React.SyntheticEvent, _value: string) => void;
	tabs: [string, JSX.Element][];
	wrapperRef?: React.RefObject<HTMLDivElement>;
};

export const Tabs = ({ activeTab, handleChange, tabs, wrapperRef }: Props) => {
	const { breakPont } = useCurrentSize();
	const { isDesktopFixed } = useDesktopSticky({ page: 'favoritesTabs' });
	return (
		<Tabs__Wrapper ref={wrapperRef} $withScroll={activeTab === 'Athletes' || activeTab === 'Staff'}>
			{breakPont !== 'small' && (
				<Tabs__ItemsWrapperTabs
					value={activeTab}
					onChange={handleChange}
					$isShort={tabs.length < 3}
					$isDesktopFixed={isDesktopFixed}
				>
					{tabs.map(([key]) => (
						<Tab key={key} label={key} value={key} />
					))}
				</Tabs__ItemsWrapperTabs>
			)}
			<TabContext value={activeTab}>
				<Tabs__ContentWrapper $isDesktopFixed={isDesktopFixed}>
					{tabs.map(([key, value]) => (
						<TabPanel key={key} value={key}>
							{value}
						</TabPanel>
					))}
				</Tabs__ContentWrapper>

				{breakPont === 'small' && (
					<Tabs__ItemsWrapper>
						<Tabs__ItemsWrapperTabs
							value={activeTab}
							onChange={handleChange}
							$isShort={tabs.length < 3}
						>
							{tabs.map(([key]) => (
								<Tab key={key} label={key} value={key} />
							))}
						</Tabs__ItemsWrapperTabs>
					</Tabs__ItemsWrapper>
				)}
			</TabContext>
		</Tabs__Wrapper>
	);
};
