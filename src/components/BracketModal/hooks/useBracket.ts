import axios from 'axios';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { toast } from 'react-toastify';

import { useBracketQuery } from '@/generated/graphql';

import { IBracketTemplate } from '../types';

type PropsT = {
	poolId: string;
};
export const useBracket = ({ poolId }: PropsT) => {
	const [currentPoolId, setCurrentPoolId] = useState('');

	useEffect(() => {
		if (currentPoolId !== poolId) {
			setCurrentPoolId(poolId);
		}
		// * only one time update
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	const [bracketTemplate, setBracketTemplate] = useState<IBracketTemplate>();
	const { id } = useParams();
	const getTemplate = async (templateName: string) => {
		const tmpNameArr = templateName.split('.');

		const template = await axios
			.get(`${import.meta.env.VITE_BRACKETS_API_URL}/bracket/${tmpNameArr[0]}`)
			.catch(() => {
				console.error(`Failed to load template ${tmpNameArr[0]}`);
				return { data: null };
			});

		if (template.data) {
			setBracketTemplate(template.data);
		} else {
			setBracketTemplate(undefined);
			toast.warning(`Template ${tmpNameArr[0]} not found`);
		}
	};

	const { data: bracketData, loading: bracketLoading } = useBracketQuery({
		variables: {
			id: id!,
			poolId: currentPoolId,
		},
		skip: Boolean(!currentPoolId),
		fetchPolicy: 'no-cache',
		onCompleted: () => {
			if (bracketData?.bracket?.pool?.flow_chart) {
				getTemplate(bracketData.bracket.pool.flow_chart);
			}
		},
	});

	const hideDescriptionTemplates = ['tw3', 'tw8', 'tw3c', 'tfull'];

	const isTemplateNameIncludesHideDescription = (bracketTemplate: IBracketTemplate) => {
		if (bracketTemplate.name) {
			return hideDescriptionTemplates.some((template) => bracketTemplate.name.includes(template));
		}
		return false;
	};

	const updateTemplate = (bracketTemplate: IBracketTemplate) => {
		if (!bracketData) {
			return bracketTemplate;
		}

		bracketData.bracket?.matches?.forEach((match) => {
			const currentMatch = bracketTemplate?.matches?.find(
				(m) => +m.matchName === match.match_number,
			);
			if (currentMatch) {
				currentMatch.participants[0].id = match.team1_roster_id?.toString() || '';
				currentMatch.participants[1].id = match.team2_roster_id?.toString() || '';
				currentMatch.participants[0].name = match.team1_name || '';
				currentMatch.participants[1].name = match.team2_name || '';

				currentMatch.results = match.results!;
				currentMatch.ref_name = match.ref_name || match.source?.ref?.name || '';
				currentMatch.court_name = match.court_name!;
				currentMatch.date_start = match.date_start!;
				currentMatch.show_previously_accepted_bid_team1 = match.show_previously_accepted_bid_team1!;
				currentMatch.show_previously_accepted_bid_team2 = match.show_previously_accepted_bid_team2!;

				if (currentMatch.description && !isTemplateNameIncludesHideDescription(bracketTemplate)) {
					currentMatch.description = '';
				}

				currentMatch.participants.forEach((participant) => {
					delete participant['isByes'];
				});
			}
		});

		return bracketTemplate;
	};

	return {
		bracketTemplate: updateTemplate(bracketTemplate!),
		bracketData: bracketData?.bracket,
		loading: bracketLoading,
		setCurrentPoolId,
		currentPoolId,
	};
};
