import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';

import { DivisionPool } from '@/generated/graphql';

import arrowLeftIcon from './icons/arrow-left.icon.svg';
import arrowRightIcon from './icons/arrow-right.icon.svg';
import closeIcon from './icons/close.icon.svg';
import searchIcon from './icons/search-icon.svg';
import {
	BracketSearch__NavBtnWrapper,
	BracketSearch__PoolNav,
	BracketSearch__PoolNavNextBtn,
	BracketSearch__PoolNavPrevBtn,
	BracketSearch__SearchBtn,
	BracketSearch__Wrapper,
} from './styled';

type PropsT = {
	changeSearch: (_search: string) => void;
	search: string;
	currentRound: DivisionPool;
	bracketRounds: DivisionPool[];
	setCurrentPoolId: Dispatch<SetStateAction<string>>;
	openPoolBracketAnchorModal: (_teamId: number, _pool: DivisionPool) => void;
};
export const BracketSearch = ({
	changeSearch,
	search,
	currentRound,
	bracketRounds,
	setCurrentPoolId,
	openPoolBracketAnchorModal,
}: PropsT) => {
	const [isSearchOpen, setIsSearchOpen] = useState(false);
	const searchRef = useRef<HTMLInputElement | null>(null);
	useEffect(() => {
		if (isSearchOpen) {
			searchRef.current?.focus();
		}
	}, [isSearchOpen]);

	const currentRoundIndex = bracketRounds.indexOf(currentRound!);
	const prevRound = bracketRounds[currentRoundIndex - 1];
	const nextRound = bracketRounds[currentRoundIndex + 1];

	const updatePoolIdHandler = (uuid: string) => {
		setCurrentPoolId(uuid);
	};

	const getDayPrefix = (round: DivisionPool) => {
		let dayPrefix = '';
		if (round && round.is_pool) {
			const match = round.display_name?.match(/D[1-9]/);
			if (match) {
				dayPrefix = match[0];
			}
		}
		return dayPrefix;
	};

	return (
		<BracketSearch__Wrapper>
			{!isSearchOpen && (
				<BracketSearch__PoolNav>
					<BracketSearch__PoolNavPrevBtn>
						{prevRound && (
							<BracketSearch__NavBtnWrapper
								onClick={() => {
									prevRound.is_pool
										? openPoolBracketAnchorModal(
												prevRound.teams?.[0]?.opponent_team_id || 0,
												prevRound,
											)
										: updatePoolIdHandler(prevRound.uuid!);
								}}
							>
								<img src={arrowLeftIcon} alt="" />
								{getDayPrefix(prevRound)} {prevRound?.pb_name}
							</BracketSearch__NavBtnWrapper>
						)}
					</BracketSearch__PoolNavPrevBtn>
					<BracketSearch__PoolNavNextBtn>
						{nextRound && (
							<BracketSearch__NavBtnWrapper
								onClick={() => {
									nextRound.is_pool
										? openPoolBracketAnchorModal(
												nextRound.teams?.[0]?.opponent_team_id || 0,
												nextRound,
											)
										: updatePoolIdHandler(nextRound.uuid!);
								}}
							>
								{nextRound?.pb_name}
								<img src={arrowRightIcon} alt="" />
							</BracketSearch__NavBtnWrapper>
						)}
					</BracketSearch__PoolNavNextBtn>
				</BracketSearch__PoolNav>
			)}
			<BracketSearch__SearchBtn $isSearchOpen={isSearchOpen}>
				{isSearchOpen && (
					<input
						type="text"
						ref={searchRef}
						onChange={(e) => changeSearch(e.target.value.toLowerCase())}
						value={search}
					/>
				)}
				<div>
					<img
						src={isSearchOpen ? closeIcon : searchIcon}
						alt=""
						onClick={() => {
							if (isSearchOpen) {
								changeSearch('');
							}
							setIsSearchOpen(!isSearchOpen);
						}}
					/>
				</div>
			</BracketSearch__SearchBtn>
		</BracketSearch__Wrapper>
	);
};
