import styled, { css } from 'styled-components';

export const BracketSearch__Wrapper = styled.header`
	padding: 0 10px 20px 10px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: #fff;
	position: relative;
	z-index: 9;
	position: fixed;
	top: 56px;
	width: 100%;
`;
export const BracketSearch__PoolNav = styled.div`
	border-radius: 2px;
	border: 1px solid #dfe3e8;
	height: 40px;
	width: calc(100% - 48px);
	background: #fff;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 35px;
	position: relative;
`;

const commonPoolNavBtnStyles = css`
	border: none;
	background: transparent;
	position: absolute;
	top: 8px;
	cursor: pointer;
	outline: none;
`;

export const BracketSearch__PoolNavPrevBtn = styled.button`
	${commonPoolNavBtnStyles}
	left: 0;
`;
export const BracketSearch__PoolNavNextBtn = styled.button`
	${commonPoolNavBtnStyles}
	right: 0;
`;
export const BracketSearch__PoolNavTitle = styled.span`
	font-size: 12px;
	line-height: 18px;
	cursor: pointer;
	&:hover {
		text-decoration: underline;
	}
`;
export const BracketSearch__SearchBtn = styled.div<{ $isSearchOpen: boolean }>`
	border-radius: 2px;
	border: 1px solid #dfe3e8;
	height: 40px;
	display: flex;
	align-items: center;
	justify-content: center;
	width: ${(props) => (props.$isSearchOpen ? '100%' : '40px')};
	background: #fff;
	div {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 40px;
	}
	input {
		width: calc(100% - 40px);
		height: 38px;
		border: none;
		outline: none;
		padding: 0 10px;
	}
`;
export const BracketSearch__NavBtnWrapper = styled.div`
	display: flex;
	align-items: center;
`;
