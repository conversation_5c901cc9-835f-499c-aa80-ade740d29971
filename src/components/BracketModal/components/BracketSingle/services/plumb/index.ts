import { EndpointOptions, jsPlumb, jsPlumbInstance } from 'jsplumb';
import { Dispatch, SetStateAction } from 'react';
import { toast } from 'react-toastify';

import {
	OnConnection,
	SetConnectionT,
	SetPrevAndNextMatchConnectionsT,
} from '../../interfaces/plumb.interfaces';
import {
	DeleteManagedElementT,
	GetInstanceT,
	PlumbDevelopT,
	SetInitialEndpointT,
	SetListenersT,
	UpdateManagedElementOnNextTickT,
	UpdateRelationT,
} from './types';

const PLUMB_CONFIG: EndpointOptions = {
	isSource: true,
	isTarget: true,
	// connector: ['Straight', { stub: 35, gap: -10, cornerRadius: 10 }],
	// hoverPaintStyle: { stroke: '#0E71A3', strokeWidth: 5 },
	maxConnections: 3,
	endpoint: 'Dot',
	connector: ['Flowchart', { stub: 35, gap: -10, cornerRadius: 0 }],
};

export const getInstance = ({ container }: GetInstanceT) => {
	const instance = jsPlumb.getInstance({
		PaintStyle: {
			strokeWidth: 1,
			stroke: '#637381',
		},
		Connector: ['Flowchart', { curviness: 20 }],
		Endpoint: ['Dot', { radius: 1 }],
		EndpointStyle: { fill: '#fff' },
		Container: container,
	});

	instance.repaintEverything();
	return instance;
};

export const addPrefixToMatchId = (matchId: string) => `match__${matchId}`;
export const removePrefixFromMatchId = (matchId: string) => matchId.split('__')[1];

export const updateRelation = ({
	matches,
	targetId,
	sourceId,
	type,
	setMatches,
}: UpdateRelationT) => {
	const sourceMatch = matches.find((m) => m.id === sourceId);
	const targetMatch = matches.find((m) => m.id === targetId);

	setMatches((prev) => {
		return prev.map((match) => {
			if (targetMatch && sourceMatch && type === 'AddRelation') {
				if (match.id === sourceMatch.id) {
					return {
						...match,
						nextMatchId: targetMatch.id,
					};
				}
				if (match.id === targetMatch.id) {
					return {
						...match,
						prevMatchId: match.prevMatchId
							? [...new Set([...match.prevMatchId, sourceMatch.id])]
							: [...new Set([sourceMatch.id])],
					};
				}
			}
			if (targetMatch && sourceMatch && type === 'RemoveRelation') {
				if (match.id === sourceMatch.id) {
					return {
						...match,
						nextMatchId: null,
					};
				}
				if (match.id === targetMatch.id) {
					return {
						...match,
						prevMatchId:
							[...new Set(match.prevMatchId?.filter((id) => id !== sourceMatch.id))] || null,
					};
				}
			}
			return match;
		});
	});
};

export const deleteManagedElement = ({ instance, matchIdWithPrefix }: DeleteManagedElementT) => {
	const inst = instance as PlumbDevelopT;
	delete inst.getManagedElements()[matchIdWithPrefix];
};

export const unbindListeners = (instance: jsPlumbInstance) => {
	instance.unbind('connection');
	instance.unbind('click');
	instance.unbind('beforeDrop');
};

export const setListeners = ({ instance, matches, setMatches }: SetListenersT) => {
	unbindListeners(instance);

	instance.bind('connection', (data) => {
		const { targetId, sourceId } = data;
		updateRelation({
			matches,
			setMatches,
			targetId: removePrefixFromMatchId(targetId),
			sourceId: removePrefixFromMatchId(sourceId),
			type: 'AddRelation',
		});

		//* Set labels on connection
		const allConnections = instance.getAllConnections();
		const connections = allConnections.filter((c) => c.targetId === targetId);
		connections.forEach((c) => c.setLabel('X'));
	});

	instance.bind('click', (data) => {
		//* Click on remove label
		const { sourceId, targetId } = data;
		const connections = instance.getAllConnections();
		const currentConnection = connections.find((c) => c.sourceId === sourceId);

		if (currentConnection) {
			updateRelation({
				matches,
				setMatches,
				targetId: removePrefixFromMatchId(targetId),
				sourceId: removePrefixFromMatchId(sourceId),
				type: 'RemoveRelation',
			});
			instance.deleteConnection(currentConnection);
		}
	});

	instance.bind('beforeDrop', (data) => {
		const connectionInfo = data as OnConnection;
		//* Before Connection
		const { sourceId, targetId } = data;
		const sourceUUID = sourceId.split('__')[1];
		const targetUUID = targetId.split('__')[1];
		const sourceMatch = matches.find((m) => m.id === sourceUUID);
		const targetMatch = matches.find((m) => m.id === targetUUID);

		if (sourceMatch?.columnIndex === targetMatch?.columnIndex) {
			toast.warning("You can't connect two matches in the same round");
			return false;
		}

		if (connectionInfo.dropEndpoint.type === 'Dot') {
			toast.warning('You can connect only from left side of match');
			return false;
		}
		return true;
	});
};

export const updateManagedElementOnNextTick = ({
	matchIdWithPrefix,
	instance,
}: UpdateManagedElementOnNextTickT) => {
	const inst = instance as PlumbDevelopT;
	const data = inst.getManagedElements()[matchIdWithPrefix];
	if (data) {
		data.el = document.getElementById(matchIdWithPrefix);
		instance.revalidate(matchIdWithPrefix);
	}
};

export const setEndpoint = ({
	instance,
	match,
	isLastColumn,
	matchRef,
	matchIdWithPrefix,
}: SetInitialEndpointT) => {
	const id = matchRef || matchIdWithPrefix || '';

	if (match.columnIndex > 0 && !isLastColumn) {
		instance.addEndpoint(
			id,
			{ anchor: 'Left', maxConnections: 2 },
			{ ...PLUMB_CONFIG, endpoint: 'Blank', isSource: false },
		);
		instance.addEndpoint(
			id,
			{ anchor: 'Right', maxConnections: 2 },
			{ ...PLUMB_CONFIG, endpoint: 'Dot', isSource: true, enabled: !match.nextMatchId },
		); // Dot
	}

	if (!match.columnIndex && !isLastColumn) {
		instance.addEndpoint(
			id,
			{ anchor: 'Right', maxConnections: 2 },
			{ ...PLUMB_CONFIG, endpoint: 'Dot', enabled: !match.nextMatchId },
		); // Dot
	}

	if (match.columnIndex > 0 && isLastColumn) {
		instance.addEndpoint(
			id,
			{ anchor: 'Left', maxConnections: 2 },
			{ ...PLUMB_CONFIG, isSource: false, endpoint: 'Blank' },
		);
	}
};

export const setConnection = ({
	instance,
	sourceMatchIdWithoutPrefix,
	targetMatchIdWithoutPrefix,
}: SetConnectionT) => {
	instance.connect({
		source: instance
			.getEndpoints(addPrefixToMatchId(sourceMatchIdWithoutPrefix))
			.find((el) => el.type === 'Dot'),
		target: instance
			.getEndpoints(addPrefixToMatchId(targetMatchIdWithoutPrefix))
			.find((el) => el.type === 'Blank'),
		cssClass: `connector-${sourceMatchIdWithoutPrefix}`,
	});
};

export const setPrevAndNextMatchConnections = ({
	instance,
	matches,
	draggableMatch,
}: SetPrevAndNextMatchConnectionsT) => {
	if (!draggableMatch) {
		return;
	}
	const prevMatchId = matches.find((match) => match.prevMatchId?.includes(draggableMatch?.id))?.id;
	const nextMatchIdFirst = matches.find((match) => match.nextMatchId === draggableMatch?.id)?.id;
	const nextMatchIdLast = matches.findLast((match) => match.nextMatchId === draggableMatch?.id)?.id;

	if (prevMatchId && draggableMatch) {
		setConnection({
			instance,
			targetMatchIdWithoutPrefix: prevMatchId,
			sourceMatchIdWithoutPrefix: draggableMatch.id,
		});
	}

	if (nextMatchIdFirst && draggableMatch) {
		setConnection({
			instance,
			targetMatchIdWithoutPrefix: draggableMatch.id,
			sourceMatchIdWithoutPrefix: nextMatchIdFirst,
		});
	}
	if (nextMatchIdLast && draggableMatch) {
		setConnection({
			instance,
			targetMatchIdWithoutPrefix: draggableMatch.id,
			sourceMatchIdWithoutPrefix: nextMatchIdLast,
		});
	}
};

export const removeDynamicConnectorStyles = () => {
	// * Remove styels
	[...document.querySelectorAll('.jtk-connector')].forEach((connector) => {
		connector.querySelector('path')?.setAttribute('stroke', '#637381');
		connector.querySelector('path')?.setAttribute('stroke-width', '1px');
		(connector as HTMLElement).style.zIndex = 'auto';
		(connector as HTMLElement).style.background = 'transparent';
		connector.removeAttribute('stroke-dasharray');
		connector.querySelector('path')?.setAttribute('stroke-dasharray', '0');
	});
};

export const addDynamicConnectorStyles = ({
	matchesIds,
	setIntervals,
	activeTeamId,
}: {
	matchesIds: Set<string>;
	setIntervals: Dispatch<SetStateAction<NodeJS.Timeout[]>>;
	activeTeamId: string;
}) => {
	[...matchesIds].forEach((id, index) => {
		const svgLine = document.querySelector(`.connector-${id}`) as HTMLElement;

		const matchEl = document.getElementById(`match__${[...matchesIds][index + 1]}`);
		const isParticipantActive = matchEl?.querySelector(`.participant__${activeTeamId}`);

		if (svgLine && isParticipantActive) {
			// svgLine.querySelector('path')?.setAttribute('stroke', 'green');
			// svgLine.querySelector('path')?.setAttribute('stroke-width', '1px');
			svgLine.setAttribute('stroke-dasharray', '5 5');
			setIntervals((prev) => {
				return [
					...prev,
					setInterval(() => {
						svgLine
							.querySelector('path')
							?.setAttribute('stroke-dasharray', `${3 + Math.random() * (2 - 1)}`);
						svgLine.style.background = '#fff';
						svgLine.style.zIndex = '1';
					}, 150),
				];
			});
		}
	});
};

export const changeZoom = (zoom: number, instance: jsPlumbInstance | null) => {
	if (!instance) {
		return;
	}
	const transformOrigin = [0.5, 0.5];
	const el = instance.getContainer() as HTMLElement;
	const s = 'scale(' + zoom + ')';
	const oString = transformOrigin[0] * 100 + '% ' + transformOrigin[1] * 100 + '%';

	el.style['transform'] = s;
	el.style['transformOrigin'] = oString;
	instance.setZoom(zoom);
};
