import { getDeepClone, getNumbersArray } from '@/utils';
import { jsPlumbInstance } from 'jsplumb';
import { Fragment, RefObject, useCallback, useEffect, useRef, useState } from 'react';

import { IBracketTemplate, IMatchData } from '@/components/BracketModal/types';

import { EmptyMatch } from '../components/EmptyMatch';
import { EmptyMatch__WrapperStyled } from '../components/EmptyMatch/styled';
import { Match } from '../components/Match';
import { Match__ContainerStyled, Match__WrapperStyled } from '../components/Match/styled';
import { COUNT_EMPTY_BLOCKS } from '../config';
import { IColumn } from '../interfaces/column.interface';
import { IMatch } from '../interfaces/match.interface';
import { isFinalMatch } from '../services/match.service';
import {
	addDynamicConnectorStyles,
	getInstance,
	removeDynamicConnectorStyles,
	setConnection,
} from '../services/plumb';

type PropsT = {
	container: RefObject<HTMLDivElement>;
	bracketTemplate: IBracketTemplate;
	isTemplate?: boolean;
};

type useSingleResult = {
	changeSearch: (_search: string) => void;
	search: string;
	isZoomUp: boolean;
	isShowZoom: boolean;
	activeColumn: number;
	scale: number;
	columns: IColumn[];
	matches: IMatchData[];
	renderMatch: (_data: RenderMatchT) => JSX.Element[];
	handleResize: () => void;
	zoomToggle: () => void;
	nextColumnHandler: () => void;
	prevColumnHandler: () => void;
};

type RenderMatchT = { matches: IMatchData[]; column: IColumn };

export const useBracketSingle = ({
	container,
	bracketTemplate,
	isTemplate,
}: PropsT): useSingleResult => {
	const [, setSingleBracket] = useState<IBracketTemplate | null>(null);
	const [columns, setColumns] = useState<IColumn[]>([]);
	const [matches, setMatches] = useState<IMatchData[]>([]);
	const [instance, setInstance] = useState<jsPlumbInstance | null>(null);
	const [highlightTeamId, setHighlightTeamId] = useState<string[]>([]);
	const [intervals, setIntervals] = useState<NodeJS.Timeout[]>([]);

	const [search, setSearch] = useState('');
	const changeSearch = (val: string) => {
		setSearch(val);
	};

	useEffect(() => {
		const templateMatches = bracketTemplate?.matches as unknown as IMatchData[];
		const templateColumns = bracketTemplate?.columns as unknown as IColumn[];
		setSingleBracket(bracketTemplate);
		templateMatches && setColumns(getDeepClone(templateColumns));
		if (templateColumns) {
			const clonedMatches = getDeepClone(templateMatches);

			clonedMatches.forEach((match) => {
				if (!match.ref_name) {
					match.ref_name = match?.source?.ref.name || 'Loser Previous Match from Same Court';
				}
			});

			setMatches(clonedMatches);
		}
		instance?.reset();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [bracketTemplate]);

	useEffect(() => {
		if (instance && matches.length) {
			// * Add connection after render
			matches.forEach((match) => {
				if (match.nextMatchId) {
					setConnection({
						instance,
						sourceMatchIdWithoutPrefix: match.id,
						targetMatchIdWithoutPrefix: match.nextMatchId,
					});
				}
			});
		}
	}, [instance, matches, matches.length]);

	const handleMouseEnter = ({
		match,
		participantIndex,
	}: {
		match: IMatch;
		participantIndex: number;
	}) => {
		const highlightTeamId = match.participants[participantIndex].id.toString();
		if (highlightTeamId) {
			setHighlightTeamId([highlightTeamId]);
		}
	};

	useEffect(() => {
		let clear;
		clearInterval(clear);
		const matchesIds = new Set<string>();
		highlightTeamId.forEach((id) => {
			matches.find((match) => {
				if (
					match.participants[0].id.toString() === id ||
					match.participants[1].id.toString() === id
				) {
					matchesIds.add(match.id);
				}
			});
		});
		if (intervals.length) {
			intervals.forEach((interval) => {
				clearInterval(interval);
			});
			setIntervals([]);
		}

		removeDynamicConnectorStyles();
		intervals.forEach((interval) => {
			clearInterval(interval);
		});
		setIntervals([]);

		if (highlightTeamId.length) {
			addDynamicConnectorStyles({
				matchesIds,
				setIntervals,
				activeTeamId: highlightTeamId[0],
			});
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [highlightTeamId]);

	const handleMouseLeave = () => {
		setHighlightTeamId([]);
	};

	const renderMatch = ({ matches, column }: RenderMatchT) => {
		let countEmptyBlocks = 0;
		return getNumbersArray(COUNT_EMPTY_BLOCKS).map((index) => {
			const match = matches.find(
				(match) => match.columnId === column.id && match.matchNumber === index,
			);
			if (match) {
				isFinalMatch({ match, columns });
				countEmptyBlocks = 0;
				return (
					<Match__WrapperStyled key={match.id}>
						<Match__ContainerStyled>
							<Match
								isTemplate={isTemplate}
								search={search}
								highlightTeamId={highlightTeamId}
								handleMouseEnter={handleMouseEnter}
								handleMouseLeave={handleMouseLeave}
								isLastColumn={isFinalMatch({ match, columns })}
								match={match}
								instance={instance}
							/>
						</Match__ContainerStyled>
					</Match__WrapperStyled>
				);
			}
			countEmptyBlocks++;
			if (countEmptyBlocks > 5) {
				return <Fragment key={`index__${index}__col__${column.id}`}></Fragment>;
			}

			return (
				<EmptyMatch__WrapperStyled key={`index__${index}__col__${column.id}`}>
					<EmptyMatch />
				</EmptyMatch__WrapperStyled>
			);
		});
	};
	const [isZoomUp, setZoomUp] = useState(false);
	const [scale, setScale] = useState(1);
	const handlerZoom = useCallback(() => {
		if (container.current && isZoomUp && instance) {
			setTimeout(() => {
				const element = container.current;
				if (element) {
					const scale = window.innerWidth / element.scrollWidth;
					setScale(scale);
					element.style.transform = 'scale(' + scale + ')';
					element.style.width = 'fit-content';
				}
				instance?.repaintEverything();
			});
		}
		if (container.current && !isZoomUp && instance) {
			setTimeout(() => {
				const element = container.current;
				if (element) {
					element.style.transform = 'none';
					element.style.width = '100%';
				}
				setScale(1);
				instance?.repaintEverything();
			});
		}
	}, [container, instance, isZoomUp]);
	useEffect(() => {
		handlerZoom();
	}, [container, handlerZoom, instance, isZoomUp]);

	useEffect(() => {
		if (container.current && !instance) {
			setInstance(getInstance({ container: container.current }));
		}
	}, [container, instance, matches.length]);

	// * Scroll Logic
	const columnWidth = 280;
	const [activeColumn, setActiveColumn] = useState(0);
	const nextColumnHandler = useCallback(() => {
		if (bracketTemplate && activeColumn < bracketTemplate['columns'].length! - 1) {
			setActiveColumn((prev) => prev + 1);
			container.current?.scrollTo({
				left: columnWidth * (activeColumn + 1) - 20,
				behavior: 'smooth',
			});
		}
	}, [activeColumn, bracketTemplate, container]);

	const prevColumnHandler = useCallback(() => {
		if (bracketTemplate && activeColumn > 0) {
			setActiveColumn((prev) => prev - 1);
			container.current?.scrollTo({
				left: columnWidth * (activeColumn - 1) - 20,
				behavior: 'smooth',
			});
		}
	}, [activeColumn, bracketTemplate, container]);
	const scrollTimeoutRef = useRef<number | null>(null);
	// window.instance = instance;
	const handleResize = useCallback(() => {
		const connectors = [...document.querySelectorAll('.jtk-connector')] as HTMLDivElement[];
		if (container.current) {
			setIsShowZoom(!isZoomUp && container.current?.scrollWidth > container.current?.clientWidth);
		}
		const offset = container.current?.scrollLeft;

		if (offset) {
			const activeColumn = Math.ceil(+(offset / columnWidth).toFixed(1) + 1);
			setActiveColumn(activeColumn - 1);
		}

		// * start
		if (scrollTimeoutRef.current !== null) {
			clearTimeout(scrollTimeoutRef.current);
		} else {
			console.log('Scroll started');
			if (instance) {
				// TODO: bracket
				connectors.map((el) => (el.style.display = 'none'));
			}
		}

		// * end
		scrollTimeoutRef.current = window.setTimeout(() => {
			scrollTimeoutRef.current = null;
			console.log('Scroll ended');
			if (instance) {
				// TODO: bracket
				instance.revalidate(container.current as HTMLElement);
				connectors.map((el) => (el.style.display = 'block'));
			}
		}, 150);
	}, [container, instance, isZoomUp]);

	useEffect(() => {
		window.addEventListener('resize', handleResize);
		window.addEventListener('resize', handlerZoom);

		return () => {
			window.removeEventListener('resize', handleResize);
			window.removeEventListener('resize', handlerZoom);
		};
	}, [handleResize, handlerZoom, instance]);

	const [isShowZoom, setIsShowZoom] = useState(false);

	useEffect(() => {
		// * To show bottom button
		setTimeout(() => {
			if (container.current) {
				setIsShowZoom(container.current?.scrollWidth > container.current?.clientWidth);
			}
		}, 100);
	}, [container, isZoomUp]);

	useEffect(() => {
		// * repaint bracket because search block on the top has if statement render
		setTimeout(() => {
			instance?.repaintEverything();
		}, 100);
	}, [instance]);

	const zoomToggle = () => {
		const modalEl = document.querySelector('#modal');
		if (modalEl) {
			modalEl.classList.toggle('zoom');
		}
		setZoomUp(!isZoomUp);
		setTimeout(() => {
			instance?.repaintEverything();
		}, 120);
	};

	return {
		columns,
		matches,
		renderMatch,
		handleResize,
		isZoomUp,
		zoomToggle,
		nextColumnHandler,
		prevColumnHandler,
		activeColumn,
		isShowZoom,
		search,
		changeSearch,
		scale,
	};
};
