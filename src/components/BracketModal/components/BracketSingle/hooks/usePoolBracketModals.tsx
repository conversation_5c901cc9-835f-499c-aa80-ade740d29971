import { useCallback, useState } from 'react';
import { useParams } from 'react-router-dom';

import { DivisionPool, useTeamSingleLazyQuery } from '@/generated/graphql';

export const usePoolBracketModals = () => {
	const { id } = useParams();
	const [getTeamPoolData, { data: teamPoolData, loading: teamPoolDataLoading }] =
		useTeamSingleLazyQuery();

	const [isShowPoolBracketAnchorModal, setIsShowPoolBracketAnchorModal] = useState(false);
	const [poolOfTeam, setPoolOfTeam] = useState<DivisionPool | null>(null);
	const openPoolBracketAnchorModal = useCallback(
		(teamId: number, poolOfTeam?: DivisionPool) => {
			setIsShowPoolBracketAnchorModal(true);
			poolOfTeam && setPoolOfTeam(poolOfTeam);
			getTeamPoolData({
				variables: {
					id: id!,
					teamId: `${teamId}`,
				},
				onCompleted: () => {
					setIsShowPoolBracketAnchorModal(true);
				},
			});
		},
		[getTeamPoolData, id],
	);

	const closePoolBracketAnchorModal = () => {
		setPoolOfTeam(null);
		setIsShowPoolBracketAnchorModal(false);
	};

	const [isNavButtonClicked, setIsNavButtonClicked] = useState(false);

	const returnNavButtonToDefaultState = () => {
		setTimeout(() => {
			setIsNavButtonClicked(false);
		}, 350);
	};

	return {
		teamPoolData,
		isLoading: teamPoolDataLoading,
		isShowPoolBracketAnchorModal,
		openPoolBracketAnchorModal,
		closePoolBracketAnchorModal,
		poolOfTeam,
		isNavButtonClicked,
		setIsNavButtonClicked,
		returnNavButtonToDefaultState,
	};
};
