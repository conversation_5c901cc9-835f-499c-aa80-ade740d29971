import arrowPrev from '@assets/leftBlack-icon.svg';
import arrowNext from '@assets/rightBlack-icon.svg';
import zoomLessIcon from '@assets/zoomLess-icon.svg';
import zoomMoreIcon from '@assets/zoomMore-icon.svg';
import { Dispatch, SetStateAction, useRef } from 'react';

import { AnchorModal } from '@/components/AnchorModal';

import { DivisionPool } from '@/generated/graphql';

import { IBracketTemplate } from '../../types';
import { BracketSearch } from '../BracketSearch';
import { Column } from './components/Column';
import { useBracketSingle } from './hooks/useBracketSingle';
import { usePoolBracketModals } from './hooks/usePoolBracketModals';
import {
	BracketSingle__ColumnContainer,
	BracketSingle__ColumnWrapper,
	BracketSingle__NavigateNext,
	BracketSingle__NavigatePrev,
	BracketSingle__WrapperStyled,
	BracketSingle__ZoomButton,
} from './styled';

type PropsT = {
	isTemplate?: boolean;
	bracketTemplate: IBracketTemplate;
	currentRound: DivisionPool;
	bracketRounds: DivisionPool[];
	setCurrentPoolId: Dispatch<SetStateAction<string>>;
	close: () => void;
};
export const BracketSingle = ({
	bracketTemplate,
	currentRound,
	bracketRounds,
	setCurrentPoolId,
	close,
	isTemplate,
}: PropsT) => {
	const container = useRef<HTMLDivElement>(null);

	const {
		columns,
		matches,
		renderMatch,
		handleResize,
		isZoomUp,
		nextColumnHandler,
		prevColumnHandler,
		zoomToggle,
		activeColumn,
		isShowZoom,
		search,
		changeSearch,
	} = useBracketSingle({
		container,
		bracketTemplate,
		isTemplate,
	});

	const {
		isShowPoolBracketAnchorModal,
		openPoolBracketAnchorModal,
		closePoolBracketAnchorModal,
		teamPoolData: teamPoolData,
		poolOfTeam: poolOfTeamData,
		isNavButtonClicked,
		setIsNavButtonClicked,
		returnNavButtonToDefaultState,
	} = usePoolBracketModals();

	const columnWrapperRef = useRef<HTMLDivElement>(null);
	const isScrollableColumns = false;

	return (
		<>
			{isShowPoolBracketAnchorModal && teamPoolData ? (
				<AnchorModal
					isPool
					teamPoolData={teamPoolData}
					close={() => {
						closePoolBracketAnchorModal();
						close();
					}}
					poolOfTeam={poolOfTeamData}
				/>
			) : (
				<>
					<BracketSearch
						openPoolBracketAnchorModal={openPoolBracketAnchorModal}
						search={search}
						setCurrentPoolId={setCurrentPoolId}
						changeSearch={changeSearch}
						currentRound={currentRound}
						bracketRounds={bracketRounds}
					/>
					{!isZoomUp && (
						<BracketSingle__ColumnWrapper $isScrollableColumns={isScrollableColumns}>
							{isScrollableColumns && isShowZoom && !!activeColumn && (
								<BracketSingle__NavigatePrev
									onClick={() => {
										prevColumnHandler();
										setIsNavButtonClicked(true);
										returnNavButtonToDefaultState();
									}}
								>
									<img src={arrowPrev} alt="" />
								</BracketSingle__NavigatePrev>
							)}
							{isScrollableColumns &&
								isShowZoom &&
								!isZoomUp &&
								activeColumn !== columns.length - 1 && (
									<BracketSingle__NavigateNext
										onClick={() => {
											nextColumnHandler();
											setIsNavButtonClicked(true);
											returnNavButtonToDefaultState();
										}}
									>
										<img src={arrowNext} alt="" />
									</BracketSingle__NavigateNext>
								)}
							<BracketSingle__ColumnContainer
								ref={columnWrapperRef}
								onScroll={(e) => {
									if (isScrollableColumns && container.current && !isNavButtonClicked) {
										const target = e.target as HTMLDivElement;
										container.current.scrollTo({
											left: target.scrollLeft,
										});
									}
								}}
							>
								{isScrollableColumns &&
									!isZoomUp &&
									columns.map((column) => (
										<div key={column.id}>
											<Column column={column} />
										</div>
									))}
							</BracketSingle__ColumnContainer>
						</BracketSingle__ColumnWrapper>
					)}

					<BracketSingle__WrapperStyled
						$isZoomUp={isZoomUp}
						ref={container}
						onScroll={(e) => {
							if (isScrollableColumns && columnWrapperRef.current) {
								const target = e.target as HTMLDivElement;
								columnWrapperRef.current.scrollTo({
									left: target.scrollLeft,
								});
							}
							handleResize();
						}}
					>
						{columns.map((column) => (
							<Column
								key={column.id}
								column={!isScrollableColumns || isZoomUp ? column : undefined}
								isShowColumnName={columns.length > 1}
							>
								<div>{renderMatch({ column, matches })}</div>
							</Column>
						))}
					</BracketSingle__WrapperStyled>
					{(isShowZoom || (!isShowZoom && isZoomUp)) && (
						<BracketSingle__ZoomButton onClick={zoomToggle}>
							<img src={isZoomUp ? zoomMoreIcon : zoomLessIcon} alt="" />
						</BracketSingle__ZoomButton>
					)}
				</>
			)}
		</>
	);
};
