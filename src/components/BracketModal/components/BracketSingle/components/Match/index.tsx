import { bracketGridMatchNameSignal } from '@/signals/bracketGridMatchNameSignal';
import { jsPlumbInstance } from 'jsplumb';
import { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';

import { AnchorModal } from '@/components/AnchorModal';
import { IMatchData } from '@/components/BracketModal/types';
import { Loader } from '@/components/Loader';
import { PreviouslyQualifiedInfo } from '@/components/PreviouslyQualifiedInfo';

import { BracketMatchResultsTeam, Maybe, useTeamSingleLazyQuery } from '@/generated/graphql';

import { SearchService } from '@/utils/search.service';
import { getFormateTime } from '@/utils/time';

import { IMatch } from '../../interfaces/match.interface';
import { ITeam } from '../../interfaces/team.interface';
import { addPrefixToMatchId, setEndpoint } from '../../services/plumb';
import {
	Match__FooterExtraData,
	Match__HeaderExtraData,
	Match__PreviouslyQualifiedWrapper,
	Match__Scores,
	Match__SingleDescriptionStyled,
	Match__SingleNumberStyled,
	Match__SingleParticipantName,
	Match__SingleParticipantStyled,
	Match__SingleParticipantWrapperStyled,
	Match__SingleWrapperStyled,
} from './styled';

type PropsT = {
	search: string;
	isTemplate?: boolean;
	highlightTeamId: string[];
	match: IMatchData;
	instance?: jsPlumbInstance | null;
	isLastColumn: boolean;
	handleMouseEnter: (_props: { match: IMatch; participantIndex: number }) => void;
	handleMouseLeave: () => void;
};
export const Match = ({
	search,
	match,
	instance,
	isLastColumn,
	highlightTeamId,
	handleMouseEnter,
	handleMouseLeave,
	isTemplate,
}: PropsT) => {
	const searchService = new SearchService();
	const { id } = useParams();
	const [isShowPoolBracketAnchorModal, setIsShowPoolBracketAnchorModal] = useState(false);
	const [getTeamPoolData, { data: teamPoolData, loading: teamPoolDataLoading }] =
		useTeamSingleLazyQuery();
	const openPoolBracketAnchorModal = (teamId: number) => {
		getTeamPoolData({
			variables: {
				id: id!,
				teamId: `${teamId}`,
			},
			onCompleted: () => {
				setIsShowPoolBracketAnchorModal(true);
			},
		});
	};
	const closePoolBracketAnchorModal = () => {
		setIsShowPoolBracketAnchorModal(false);
	};
	const participantClickHandler = (team: ITeam) => {
		if (isTemplate) return;
		openPoolBracketAnchorModal(+team.id!);
	};
	const element = useRef<HTMLDivElement>(null);
	useEffect(() => {
		if (element.current && instance) {
			setEndpoint({ instance, match, isLastColumn, matchRef: element.current });
		}
	}, [instance, isLastColumn, match]);
	let winner: '1' | '2' | undefined;
	let teamWinner: Maybe<BracketMatchResultsTeam> | undefined;
	if (match?.results) {
		winner = match?.results?.winner as '1' | '2';
		teamWinner = match?.results[`team${winner}`];
	}
	const matchGridNumber = bracketGridMatchNameSignal.value.match(/M(\d+)/)?.[0];
	const matchName = `M${match.matchName}`;

	useEffect(() => {
		if (matchGridNumber === matchName && element.current) {
			const scrollContainer = element.current.closest('.bracket-scroll-container');

			if (scrollContainer) {
				setTimeout(() => {
					if (element.current) {
						scrollContainer.scrollTo({
							top:
								element.current.offsetTop -
								scrollContainer.clientHeight / 2 +
								element.current.clientHeight / 2,
							left:
								element.current.getBoundingClientRect().left / 2 -
								scrollContainer.clientWidth / 2 +
								element.current.clientWidth / 2,

							behavior: 'smooth',
						});
					}
				}, 300);
			}
		}
	}, [match.matchName, matchGridNumber, matchName]);

	return (
		<>
			{teamPoolDataLoading && <Loader />}
			{isShowPoolBracketAnchorModal && teamPoolData && (
				<AnchorModal
					isPool={false}
					teamPoolData={teamPoolData}
					close={closePoolBracketAnchorModal}
				/>
			)}
			<Match__SingleWrapperStyled ref={element} id={addPrefixToMatchId(match.id)}>
				{match.description && (
					<Match__SingleDescriptionStyled>{match.description}</Match__SingleDescriptionStyled>
				)}
				<Match__SingleNumberStyled $isActive={matchGridNumber === matchName}>
					{matchName}
				</Match__SingleNumberStyled>
				<Match__HeaderExtraData>
					<span>
						{match.date_start && getFormateTime({ time: match.date_start, format: 'EEE, h:mmaaa' })}
					</span>
					{!isTemplate && <span>@ {match.court_name}</span>}
				</Match__HeaderExtraData>
				<Match__FooterExtraData>
					{!isTemplate && <span>{match.ref_name || 'unknown'}</span>}
				</Match__FooterExtraData>
				<Match__SingleParticipantWrapperStyled
					$isOpacity={
						!!search &&
						search.length >= 3 &&
						!!searchService.performSearch({ page: 'brackets', data: match }, search)
					}
				>
					{match.participants.map((participant, index) => (
						<Match__SingleParticipantStyled
							$isActive={highlightTeamId.includes(match.participants[index].id.toString())}
							$isWinner={winner ? +winner !== index + 1 : true}
							key={participant.id}
							onMouseEnter={() => handleMouseEnter({ match, participantIndex: index })}
							onMouseLeave={handleMouseLeave}
							onClick={() => participantClickHandler(participant)}
						>
							<Match__SingleParticipantName
								$isQualified
								className={`participant__${participant.id}`}
							>
								{participant.name}
							</Match__SingleParticipantName>
							{match.results && (
								<Match__Scores
									$isWinner={winner ? +winner !== index + 1 : true}
									$isActive={highlightTeamId.includes(match.participants[index].id.toString())}
								>
									{match.results.set1 && <li>{match.results.set1.split('-')[index]}</li>}
									{match.results.set2 && <li>{match.results.set2.split('-')[index]}</li>}
									{match.results.set3 && <li>{match.results.set3.split('-')[index]}</li>}
									{match.results.set4 && <li>{match.results.set4.split('-')[index]}</li>}
									{match.results.set5 && <li>{match.results.set5.split('-')[index]}</li>}

									{!!(teamWinner && (winner ? +winner === index + 1 : false)) && (
										<li>
											<b>{teamWinner.sets_won}</b>
										</li>
									)}
									{!!(teamWinner && (winner ? +winner !== index + 1 : true)) && (
										<li>
											<b>{teamWinner.sets_lost}</b>
										</li>
									)}
								</Match__Scores>
							)}
							{match.show_previously_accepted_bid_team1 && !index && (
								<Match__PreviouslyQualifiedWrapper
									$isSet3Exists={!!match.results?.set3}
									$isSet4Exists={!!match.results?.set4}
									$isSet5Exists={!!match.results?.set5}
									$isFirstTeam={!index}
								>
									<PreviouslyQualifiedInfo info={match.show_previously_accepted_bid_team1} />
								</Match__PreviouslyQualifiedWrapper>
							)}
							{match.show_previously_accepted_bid_team2 && !!index && (
								<Match__PreviouslyQualifiedWrapper
									$isSet3Exists={!!match.results?.set3}
									$isSet4Exists={!!match.results?.set4}
									$isSet5Exists={!!match.results?.set5}
									$isFirstTeam={!index}
								>
									<PreviouslyQualifiedInfo info={match.show_previously_accepted_bid_team2} />
								</Match__PreviouslyQualifiedWrapper>
							)}
						</Match__SingleParticipantStyled>
					))}
				</Match__SingleParticipantWrapperStyled>
			</Match__SingleWrapperStyled>
		</>
	);
};
