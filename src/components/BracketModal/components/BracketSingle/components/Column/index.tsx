import { IColumn } from '../../interfaces/column.interface';
import { Column__TitleStyled, Column__WrapperStyled } from './styled';

type PropsT = {
	column?: IColumn;
	children?: React.ReactNode;
	isShowColumnName?: boolean;
};
export const Column = ({ column, children, isShowColumnName = true }: PropsT) => {
	const isNotEmptyColumn = column?.columnIndex !== null;
	return (
		<>
			<Column__WrapperStyled>
				{column && isShowColumnName && <Column__TitleStyled>{column.name}</Column__TitleStyled>}
				{isNotEmptyColumn && children}
			</Column__WrapperStyled>
		</>
	);
};
