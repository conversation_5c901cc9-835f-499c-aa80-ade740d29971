import arrowClose from '@/assets/arrowCloseWhite.svg';

import { HookProps, useTimeRange } from './hooks/useTimeRange';
import {
	TimeRange__Box,
	TimeRange__Button,
	TimeRange__Content,
	TimeRange__Footer,
	TimeRange__Header,
	TimeRange__ItemEnd,
	TimeRange__ItemStart,
	TimeRange__List,
	TimeRange__Main,
	TimeRange__Select,
	TimeRange__SelectedField,
	TimeRange__Wrapper,
} from './styled';

type Props = HookProps & {
	title: string;
};

export const TimeRange = (props: Props) => {
	const {
		isOpen,
		setIsOpen,
		startTime,
		setStartTime,
		endTime,
		setEndTime,
		clear,
		apply,
		timeSlots,
		minSelectableTime,
		maxSelectableTime,
		startRangeMaxTime,
		dropdownRef,
		timeRangeStartRef,
		timeRangeEndRef,
		displaySelectedStartTime,
		displaySelectedEndTime,
		displayStartTime,
		displayEndTime,
	} = useTimeRange(props);

	return (
		<TimeRange__Wrapper ref={dropdownRef}>
			<TimeRange__Select onClick={() => setIsOpen(!isOpen)} $isOpen={isOpen}>
				{displaySelectedStartTime && displaySelectedEndTime
					? `${displaySelectedStartTime} - ${displaySelectedEndTime}`
					: 'Time Range'}
				<img src={arrowClose} alt="close" />
			</TimeRange__Select>
			<TimeRange__Content $isOpen={isOpen}>
				<TimeRange__Header>{props.title}</TimeRange__Header>
				<TimeRange__Main>
					<TimeRange__Box>
						<TimeRange__SelectedField
							type="text"
							value={displayStartTime || 'Start'}
							disabled
							$isEmpty={!startTime}
						/>
						<TimeRange__List ref={timeRangeStartRef}>
							{timeSlots.map(({ time24h, displayTime }, index) => {
								const isDisabled =
									time24h < minSelectableTime ||
									time24h > maxSelectableTime ||
									time24h >= startRangeMaxTime;
								return (
									<TimeRange__ItemStart
										$isDisabled={isDisabled}
										$isSelected={time24h === startTime}
										key={index}
										data-time24h={time24h}
										onClick={() => {
											setStartTime(time24h);
											setEndTime('');
										}}
									>
										{displayTime}
									</TimeRange__ItemStart>
								);
							})}
						</TimeRange__List>
					</TimeRange__Box>
					<TimeRange__Box>
						<TimeRange__SelectedField
							type="text"
							disabled
							$isEmpty={!endTime}
							value={displayEndTime || 'Select time'}
						/>
						<TimeRange__List ref={timeRangeEndRef}>
							{timeSlots.map(({ time24h, displayTime }, index) => {
								// Using isActive instead of isDisabled to deal with non-proper values in startTime
								const isActive =
									time24h >= minSelectableTime &&
									time24h <= maxSelectableTime &&
									time24h > startTime!;

								return (
									<TimeRange__ItemEnd
										$isDisabled={!isActive}
										$isSelected={endTime === time24h}
										key={index}
										data-time24h={time24h}
										onClick={() => isActive && setEndTime(time24h)}
									>
										{displayTime}
									</TimeRange__ItemEnd>
								);
							})}
						</TimeRange__List>
					</TimeRange__Box>
				</TimeRange__Main>
				<TimeRange__Footer>
					<TimeRange__Button onClick={clear}>Clear all</TimeRange__Button>
					<TimeRange__Button
						$variant="contained"
						$isDisabled={!startTime || !endTime}
						onClick={apply}
					>
						Apply
					</TimeRange__Button>
				</TimeRange__Footer>
			</TimeRange__Content>
		</TimeRange__Wrapper>
	);
};
