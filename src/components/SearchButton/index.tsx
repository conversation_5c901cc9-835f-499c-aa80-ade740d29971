import downIcon from '@assets/chevron-down-icon.svg';
import upIcon from '@assets/chevron-up-icon.svg';
import closeIcon from '@assets/closeGray-icon.svg';
import searchIcon from '@assets/search-icon.svg';
import { Dispatch, SetStateAction, useRef, useState } from 'react';

import {
	SearchButton__ResultDown,
	SearchButton__ResultUp,
	SearchButton__ResultWrapper,
	SearchButton__SearchField,
	SearchButton__Wrapper,
} from './styled';

type Props = {
	currentResult: number;
	resultsCount: number;
	navigateResults: (_type: 'prev' | 'next') => void;
	search: string;
	setSearch: Dispatch<SetStateAction<string>>;
	onClose: () => void;
};

export const SearchButton = ({
	currentResult,
	resultsCount,
	navigateResults,
	search,
	setSearch,
	onClose,
}: Props) => {
	const [isAutofocus, setIsAutofocus] = useState(false);
	const inputRef = useRef<HTMLInputElement>(null);
	const [isActive, setIsActive] = useState(false);
	const toggleActive = () => {
		if (inputRef.current) {
			setTimeout(() => {
				if (!isAutofocus) {
					inputRef.current?.focus();
					setIsAutofocus(true);
				}
			}, 100);
		}

		setIsActive(true);
	};
	const closeHandler = (e: React.MouseEvent<HTMLImageElement>) => {
		e.stopPropagation();
		setIsAutofocus(false);
		setTimeout(() => {
			setIsActive(false);
			setSearch('');
			onClose();
		}, 100);
	};

	return (
		<SearchButton__Wrapper onClick={toggleActive} $isActive={isActive}>
			{!isActive && <img src={searchIcon} alt="search" />}
			{isActive && <img src={closeIcon} alt="close" onClick={closeHandler} />}
			{isActive && (
				<SearchButton__ResultWrapper>
					<SearchButton__ResultUp src={upIcon} alt="up" onClick={() => navigateResults('prev')} />
					{currentResult + 1} of {resultsCount}
					<SearchButton__ResultDown
						src={downIcon}
						alt="down"
						$offset={resultsCount.toString().length}
						onClick={() => navigateResults('next')}
					/>
				</SearchButton__ResultWrapper>
			)}
			<SearchButton__SearchField
				autoFocus={isAutofocus}
				ref={inputRef}
				value={search}
				onChange={(e) => setSearch(e.target.value)}
				$isActive={isActive}
			/>
		</SearchButton__Wrapper>
	);
};
