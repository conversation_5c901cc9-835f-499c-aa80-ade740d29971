import styled from 'styled-components';

export const SearchButton__Wrapper = styled.div<{ $isActive: boolean }>`
	width: ${({ $isActive }) => ($isActive ? 'calc(100% - 32px)' : '40px')};
	border-radius: 4px;
	border: 1px solid #f9fafb;
	position: ${({ $isActive }) => ($isActive ? 'absolute' : 'relative')};
	height: ${({ $isActive }) => ($isActive ? '43px' : 'auto')};
	background: ${(props) => (props.$isActive ? '#fff' : props.theme.colors.blue)};
	z-index: 3;
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		width: ${({ $isActive }) => ($isActive ? '100%' : '40px')};
	}
	img {
		cursor: pointer;
		position: absolute;
		top: 50%;
		right: ${({ $isActive }) => ($isActive ? '0' : '50%')};
		transform: ${({ $isActive }) => ($isActive ? 'translate(-50%, -50%)' : 'translate(50%, -50%)')};
	}
`;
export const SearchButton__SearchField = styled.input<{ $isActive?: boolean }>`
	border: none;
	background: transparent;
	border-radius: 4px;
	width: 100%;
	height: 43px;
	outline: none;
	color: #f9fafb;
	padding: 0 40px 0 8px;
	color: ${(props) => props.theme.colors.blue};
	background: ${(props) => props.$isActive && '#fff'};
	caret-color: #f9fafb;
	caret-color: ${(props) => props.theme.colors.blue};
`;
export const SearchButton__ResultWrapper = styled.div`
	position: absolute;
	right: 45px;
	background: #fff;
	font-size: 12px;
	color: ${(props) => props.theme.colors.blue};
	top: 22px;
	transform: translateY(-50%);
	height: 25px;
	display: flex;
	align-items: center;
	padding: 0 0 0 5px;
	width: 80px;
	border-radius: 4px;
`;

export const SearchButton__ResultUp = styled.img`
	left: -15px !important;
`;
export const SearchButton__ResultDown = styled.img<{ $offset: number }>`
	right: ${(props) => (props.$offset > 2 ? '-10px' : '-0')} !important;
`;
