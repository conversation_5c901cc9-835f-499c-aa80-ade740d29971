import { EventDetailsContextValue } from '@/shared/contexts/eventDetails.context';
import { useEventDetails } from '@/shared/hooks/useEventDetails';
import { useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { useDebounce } from 'use-debounce';

import { useReactGA } from './react-ga.ts';

type SharedParams = {
	pathname?: string;
	title?: string;
	ready?: boolean;
};

type TrackPageViewParams = SharedParams;

type TrackUserSearchParams = {
	search: string;
} & SharedParams;

const SEARCH_RESULT_DEBOUNCE_TIME = 5000;

const prepareEventDetailsParams = (eventDetails: EventDetailsContextValue) => {
	return {
		event_id: eventDetails?.event?.id,
		event_name: eventDetails?.event?.long_name,
		esw_id: eventDetails?.eswId,
	};
};

export const useTrackPageView = (params: TrackPageViewParams) => {
	const { pathname, title, ready = true } = params;
	const location = useLocation();
	const eventDetails = useEventDetails();
	const eventSent = useRef(false);

	const ReactGA = useReactGA();

	useEffect(() => {
		eventSent.current = false;
	}, [pathname, title]);

	useEffect(() => {
		const eventDetailsReady = !eventDetails || !!(eventDetails?.eswId && eventDetails?.event);
		if (!ReactGA || eventSent.current || !eventDetailsReady || !ready) return;
		eventSent.current = true;
		ReactGA.send({
			hitType: 'pageview',
			page: pathname || location?.pathname,
			title: title || document.title,
			...(eventDetails ? prepareEventDetailsParams(eventDetails) : {}),
		});
	}, [location, eventDetails, pathname, title, ready, ReactGA]);
};

export const useTrackUserSearch = (params: TrackUserSearchParams) => {
	const { search, pathname, title, ready = true } = params;
	const [searchTerm] = useDebounce(search, SEARCH_RESULT_DEBOUNCE_TIME);
	const location = useLocation();
	const eventDetails = useEventDetails();

	const ReactGA = useReactGA();

	useEffect(() => {
		const eventDetailsReady = !eventDetails || !!(eventDetails?.eswId && eventDetails?.event);
		if (!ReactGA || !searchTerm || !eventDetailsReady || !ready) return;
		ReactGA.event('view_search_results', {
			search_term: searchTerm,
			page: pathname || location?.pathname,
			page_title: title || document.title,
			...(eventDetails ? prepareEventDetailsParams(eventDetails) : {}),
		});
	}, [searchTerm, location, eventDetails, pathname, title, ready, ReactGA]);
};
