import { PoolOrBracketSettings } from '@/generated/graphql';

export const getCourtName = (name: string) => {
	return `Ct ${name.split('Ct')[name.split('Ct').length - 1]}`;
};

export const getMatchName = (
	settings?: Pick<PoolOrBracketSettings, 'PlayAllSets' | 'SetCount' | 'WinningPoints'> | null,
) => {
	if (settings) {
		if (!settings['PlayAllSets']) {
			return 'Best of ' + settings['SetCount'];
		} else {
			return settings['SetCount'] + ' to ' + settings['WinningPoints'];
		}
	}
};
