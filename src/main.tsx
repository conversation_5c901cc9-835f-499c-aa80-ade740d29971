/* eslint-disable */
import './utils/polyfills';
import './utils/sentry';
import { ApolloClient, ApolloProvider, InMemoryCache } from '@apollo/client';
import '@preact/signals-react/auto';
import ReactDOM from 'react-dom/client';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { App } from './app';
/* eslint-enable */

const client = new ApolloClient({
	uri: import.meta.env.VITE_BASE_URL,
	cache: new InMemoryCache(),
});

ReactDOM.createRoot(document.getElementById('root')!).render(
	<ApolloProvider client={client}>
		<>
			<ToastContainer />
			<App />
		</>
	</ApolloProvider>,
);
