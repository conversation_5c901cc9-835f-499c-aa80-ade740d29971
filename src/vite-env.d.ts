/// <reference types="vite/client" />

interface ImportMetaEnv {
	readonly VITE_APP_ENV: string;
	readonly VITE_DEV_SERVER: string;
	readonly VITE_BASE_URL: string;
	readonly VITE_BRACKETS_API_URL: string;
	readonly VITE_PROD_SERVER: string;
	readonly VITE_SWT_URL: string;
	readonly VITE_SENTRY_DSN: string;
	readonly VITE_GA_MEASUREMENT_ID: string;
}

interface ImportMeta {
	readonly env: ImportMetaEnv;
}
