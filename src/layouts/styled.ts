import styled from 'styled-components';

import { ThemeT } from '@/styles/theme';

export const MainLayout__Wrapper = styled.div`
	@media (max-width: 375px) {
		/* Between 0 and 375px */
		min-width: 375px;
	}
	margin: auto;
`;
export const MainLayout__Content = styled.div<ThemeT>`
	@media screen and (max-width: ${(props) => props.theme.breakpoints.small}) {
		overflow: hidden;
	}
	padding-top: ${(props) => props.theme.dimensions.mobile.headerHeight};
	@media screen and (min-width: ${(props) => props.theme.breakpoints.small}) {
		padding-top: 62px;
	}
`;
