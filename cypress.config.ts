import { defineConfig } from 'cypress';
import * as dotenv from 'dotenv';

dotenv.config({ path: '.env.development' });

export default defineConfig({
	component: {
		specPattern: 'src/**/*.cy.{js,jsx,ts,tsx}',
		devServer: {
			framework: 'react',
			bundler: 'vite',
		},
	},
	e2e: {
		baseUrl: `http://localhost:${process.env.PORT}`,
		setupNodeEvents(on, config) {
			console.log(config);

			// implement node event listeners here
		},
	},
	env: {
		BASE_URL: `http://localhost:${process.env.PORT}`,
		SERVER_URL: process.env.VITE_BASE_URL,
		EVENT_ID: '7e301a7b2',
	},
});
