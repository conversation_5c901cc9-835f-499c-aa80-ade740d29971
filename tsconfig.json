{
	"compilerOptions": {
		"target": "ES2020",
		"useDefineForClassFields": true,
		"lib": ["ES2023", "DOM", "DOM.Iterable", "ESNext.Array", "ESNext"],
		"module": "ESNext",
		"skipLibCheck": true,
		"baseUrl": ".",
		"paths": {
			"@/*": ["./src/*"],
			"@hooks/*": ["src/shared/hooks/*"],
			"@assets/*": ["./src/assets/*"],
			"@generated/*": ["./src/generated/*"],
			"@config/*": ["./src/config/*"],
			"@gql/*": ["./src/gql/*"],
			"@services/*": ["./src/services/*"],
			"@utils/*": ["./src/utils/*"],
			"@components/*": ["./src/components/*"],
			"@styles/*": ["./src/styles/*"],
			"@layouts/*": ["./src/layouts/*"],
			"@pages/*": ["./src/pages/*"]
		},
		/* Bundler mode */
		"moduleResolution": "bundler",
		"allowImportingTsExtensions": true,
		"resolveJsonModule": true,
		"isolatedModules": true,
		"noEmit": true,
		"jsx": "react-jsx",

		/* Linting */
		"strict": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"noFallthroughCasesInSwitch": true
	},
	"include": ["src", "cypress"],
	"references": [{ "path": "./tsconfig.node.json" }]
}
