import { sentryVitePlugin } from '@sentry/vite-plugin';
import react from '@vitejs/plugin-react';
import path from 'path';
import { defineConfig, splitVendorChunkPlugin } from 'vite';

// https://vitejs.dev/config/
const plugins = [
	react({
		babel: {
			plugins: [
				[
					'babel-plugin-styled-components',
					{
						displayName: true,
						fileName: false,
						sourceMap: true,
					},
				],
			],
		},
	}),
	splitVendorChunkPlugin(),
];

if (process.env.VITE_SENTRY_AUTH_TOKEN) {
	plugins.push(
		sentryVitePlugin({
			org: 'sentry',
			project: 'esw-web',
			authToken: process.env.VITE_SENTRY_AUTH_TOKEN,
			sourcemaps: {
				filesToDeleteAfterUpload: ['**/*.js.map'],
			},
		}),
	);
}

export default defineConfig({
	resolve: {
		alias: {
			'@': path.resolve(__dirname, './src'),
			'@assets': path.resolve(__dirname, './src/assets'),
			'@config': path.resolve(__dirname, './src/config'),
			'@gql': path.resolve(__dirname, './src/gql'),
			'@generated': path.resolve(__dirname, './src/generated'),
			'@hooks': path.resolve(__dirname, './src/hooks'),
			'@services': path.resolve(__dirname, './src/services'),
			'@utils': path.resolve(__dirname, './src/utils'),
			'@components': path.resolve(__dirname, './src/components'),
			'@styles': path.resolve(__dirname, './src/styles'),
			'@layouts': path.resolve(__dirname, './src/layouts'),
			'@pages': path.resolve(__dirname, './src/pages'),
		},
	},
	build: {
		chunkSizeWarningLimit: 1000,
		sourcemap: true,
	},
	plugins: plugins,
	server: {
		watch: {
			usePolling: true,
		},
		host: true,
		port: 5174,
		strictPort: true,
	},
});
