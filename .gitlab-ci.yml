# Cache modules using lock file
cache:
  key:
    files:
      - yarn.lock
  paths:
    - node_modules/

deploy_static:
  only:
    - master
  script:
    - |
      docker run --rm \
        -v "$PWD":/usr/src/app \
        -w /usr/src/app \
        -u `id -u $USER`:`id -g $USER` \
        --env HOME=. \
        --env VITE_APP_ENV=production \
        --env VITE_BALLERTV_URL=$VITE_BALLERTV_URL \
        --env VITE_BRACKETS_API_URL=$VITE_BRACKETS_API_URL \
        --env VITE_SWT_URL=$VITE_SWT_URL \
        --env VITE_PROD_SERVER=$VITE_PROD_SERVER \
        --env VITE_DEV_SERVER=$VITE_DEV_SERVER \
        --env VITE_BASE_URL=$PROD_BASE_URL \
        --env VITE_SENTRY_DSN=$VITE_SENTRY_DSN \
        --env VITE_SENTRY_AUTH_TOKEN=$VITE_SENTRY_AUTH_TOKEN \
        --env VITE_GA_MEASUREMENT_ID=$VITE_GA_MEASUREMENT_ID \
        --env VITE_DEV_EVENTS_SERVER=$VITE_DEV_EVENTS_SERVER \
        --env VITE_PROD_EVENTS_SERVER=$VITE_PROD_EVENTS_SERVER \
        node:21 \
        bash -c 'yarn install; yarn build;'
    - ansible-playbook -l marc-aws-sw deploy/static.yml

deploy_static_dev:
  only:
    - development
  script:
    - |
      docker run --rm \
        -v "$PWD":/usr/src/app \
        -w /usr/src/app \
        -u `id -u $USER`:`id -g $USER` \
        --env HOME=. \
        --env VITE_APP_ENV=development \
        --env VITE_BALLERTV_URL=$VITE_BALLERTV_URL \
        --env VITE_BRACKETS_API_URL=$VITE_BRACKETS_API_URL \
        --env VITE_SWT_URL=$VITE_SWT_URL \
        --env VITE_PROD_SERVER=$VITE_PROD_SERVER \
        --env VITE_DEV_SERVER=$VITE_DEV_SERVER \
        --env VITE_BASE_URL=$DEV_BASE_URL \
        --env VITE_SENTRY_DSN=$VITE_SENTRY_DSN \
        --env VITE_SENTRY_AUTH_TOKEN=$VITE_SENTRY_AUTH_TOKEN \
        --env VITE_GA_MEASUREMENT_ID=$VITE_GA_MEASUREMENT_ID \
        --env VITE_DEV_EVENTS_SERVER=$VITE_DEV_EVENTS_SERVER \
        --env VITE_PROD_EVENTS_SERVER=$VITE_PROD_EVENTS_SERVER \
        node:21 \
        bash -c 'yarn install; yarn build;'
    - ansible-playbook -l marc-aws-sw-dev deploy/static.yml