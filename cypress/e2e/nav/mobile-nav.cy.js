import { getEventsForAllEventsPage } from '../../fixtures/requests/getEventForEventsPage.request';

describe('Mobile Nav', () => {
	beforeEach(() => {
		cy.viewport('iphone-xr');
	});

	it('should open and close the drawer', () => {
		getEventsForAllEventsPage();
		cy.getByDataTestId('drawer').should('not.exist');

		cy.get('[data-testid="aside-button"]').click();
		cy.get('[data-testid="drawer"]').should('be.visible');
		cy.get('.MuiBackdrop-root.MuiModal-backdrop').click({ force: true });
		cy.get('[data-testid="drawer"]').should('not.exist');
	});
	it('should have the drawer title', () => {
		getEventsForAllEventsPage();
		cy.get('[data-testid="aside-button"]').click({ force: true });
		cy.get('[data-testid="drawerTitle"]').contains('Sportwrench');
	});
});
