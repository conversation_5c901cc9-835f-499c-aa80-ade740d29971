import { getEventForDivisionsPage } from '../../fixtures/requests/getEventForDivisionsPage.request';

const COUNT_OF_DIVISIONS = 23;

describe('Divisions Page', () => {
	beforeEach(() => {
		getEventForDivisionsPage();
	});

	it('should retain typed value in the search input field', () => {
		const searchValue = 'searchValue';
		cy.get('[data-testid="search-field"]').type(searchValue);
		cy.get('[data-testid="search-field"]').should('have.value', searchValue);
	});

	it('should not display not found', () => {
		cy.get('[data-testid="not-found"]').should('not.exist');
	});

	it(`should display ${COUNT_OF_DIVISIONS} divisions by default`, () => {
		cy.get('[data-testid="divisions-list"]').children().should('have.length', COUNT_OF_DIVISIONS);
	});

	it('should not filter divisions when search value less than 2 characters', () => {
		cy.get('[data-testid="search-field"]').type('a');
		cy.get('[data-testid="divisions-list"]').children().should('have.length', COUNT_OF_DIVISIONS);
	});

	it('should filter divisions when searching for "open"', () => {
		cy.get('[data-testid="search-field"]').type('open');
		cy.get('[data-testid="divisions-list"]').children().should('have.length', 5);
		cy.get('[data-testid="divisions-list"]')
			.children()
			.each(($el) => {
				cy.wrap($el).should('contain.text', 'Open');
			});
	});

	it('should display no divisions when searching for "not-found"', () => {
		cy.get('[data-testid="search-field"]').type('not-found');
		cy.get('[data-testid="divisions-list"]').children().should('have.length', 0);
		cy.get('[data-testid="not-found"]').should('be.visible');
	});
});
