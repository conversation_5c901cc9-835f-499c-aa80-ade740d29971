const SERVER_URL = Cypress.env('SERVER_URL');
const EVENT_ID = Cypress.env('EVENT_ID');

export const getEventsForAllEventsPage = () => {
	cy.fixture(`${EVENT_ID}/Events`).then((data) => {
		cy.intercept('POST', `${SERVER_URL}`, (req) => {
			const { operationName, variables } = req.body;
			if (operationName === 'EventForDivisionsPage' && variables.id === EVENT_ID) {
				req.reply({
					statusCode: 200,
					body: { data },
				});
			}
		}).as('getEventsForAllEventsPage');
	});

	cy.visit(`/events`);
	cy.wait('@getEventsForAllEventsPage');
};
