const SERVER_URL = Cypress.env('SERVER_URL');
const EVENT_ID = Cypress.env('EVENT_ID');

export const getEventWithRosterForRosterPage = () => {
	cy.fixture(`${EVENT_ID}/EventWithRosterForRosterPage`).then((data) => {
		cy.intercept('POST', `${SERVER_URL}`, (req) => {
			const { operationName, variables } = req.body;
			if (operationName === 'EventWithRosterForRosterPage' && variables.id === EVENT_ID) {
				req.reply({
					statusCode: 200,
					body: { data },
				});
			}
		}).as('getEventWithRosterForRosterPage');
	});

	cy.visit(`/events/${EVENT_ID}/roster`);
	cy.wait('@getEventWithRosterForRosterPage');
};
