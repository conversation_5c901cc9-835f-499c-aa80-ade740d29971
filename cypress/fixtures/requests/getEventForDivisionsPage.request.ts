const SERVER_URL = Cypress.env('SERVER_URL');
const EVENT_ID = Cypress.env('EVENT_ID');

if (!SERVER_URL || !EVENT_ID) {
	throw new Error('SERVER_URL and EVENT_ID must be set in Cypress.env');
}

export const getEventForDivisionsPage = () => {
	cy.fixture(`${EVENT_ID}/EventForDivisionsPage`).then((data) => {
		cy.intercept('POST', `${SERVER_URL}`, (req) => {
			const { operationName, variables } = req.body || {};

			if (operationName === 'EventForDivisionsPage' && variables?.id === EVENT_ID) {
				req.reply({
					statusCode: 200,
					body: { data },
				});
			}
		}).as('getEventForDivisionsPage');
	});

	cy.visit(`/events/${EVENT_ID}/divisions`);
	cy.wait('@getEventForDivisionsPage');
};
